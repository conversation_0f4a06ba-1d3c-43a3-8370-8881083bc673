#!/usr/bin/env python3
"""
Script pour corriger les erreurs Debug.Log dans les scripts Editor de ChronoForge
"""

import os
import re

def fix_debug_logs_in_file(file_path):
    """Corrige les Debug.Log dans un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Corriger les Debug.Log incorrects
        content = re.sub(r'UnityEngine\.UnityEngine\.Debug\.', 'UnityEngine.Debug.', content)

        # Remplacer Debug.Log par UnityEngine.Debug.Log
        content = re.sub(r'\bDebug\.Log\(', 'UnityEngine.Debug.Log(', content)
        content = re.sub(r'\bDebug\.LogWarning\(', 'UnityEngine.Debug.LogWarning(', content)
        content = re.sub(r'\bDebug\.LogError\(', 'UnityEngine.Debug.LogError(', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
    
    return False

def main():
    """Fonction principale"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    editor_dir = os.path.join(script_dir, "Assets", "Scripts", "Editor")
    
    if not os.path.exists(editor_dir):
        print(f"❌ Directory not found: {editor_dir}")
        return
    
    fixed_count = 0
    
    # Parcourir tous les fichiers .cs dans le dossier Editor
    for file in os.listdir(editor_dir):
        if file.endswith('.cs'):
            file_path = os.path.join(editor_dir, file)
            if fix_debug_logs_in_file(file_path):
                fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} editor script files!")

if __name__ == "__main__":
    main()
