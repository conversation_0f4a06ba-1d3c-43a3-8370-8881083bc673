using UnityEngine;
using UnityEditor;
using UnityEngine.SceneManagement;
using SpaceClicker.Core;
using SpaceClicker.UI;
using SpaceClicker.Audio;
using SpaceClicker.Effects;
using SpaceClicker.Scenes;
using System.Collections.Generic;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Validateur d'assemblage pour Space Clicker
    /// </summary>
    public class AssemblyValidator : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<ValidationResult> validationResults = new List<ValidationResult>();
        private bool showOnlyErrors = false;
        private bool autoFix = true;
        
        private struct ValidationResult
        {
            public string category;
            public string message;
            public ValidationStatus status;
            public System.Action fixAction;
        }
        
        private enum ValidationStatus
        {
            Success,
            Warning,
            Error,
            Info
        }
        
        [MenuItem("Space Clicker/Assembly Validator")]
        public static void ShowWindow()
        {
            GetWindow<AssemblyValidator>("Assembly Validator");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker Assembly Validator", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Options
            GUILayout.BeginHorizontal();
            showOnlyErrors = EditorGUILayout.Toggle("Show Only Errors", showOnlyErrors);
            autoFix = EditorGUILayout.Toggle("Auto Fix Issues", autoFix);
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Validation buttons
            if (GUILayout.Button("Validate Assembly", GUILayout.Height(30)))
            {
                ValidateAssembly();
            }
            
            if (GUILayout.Button("Quick Fix All", GUILayout.Height(25)))
            {
                QuickFixAll();
            }
            
            GUILayout.Space(10);
            
            // Results
            if (validationResults.Count > 0)
            {
                DisplayResults();
            }
        }
        
        private void ValidateAssembly()
        {
            Debug.Log("🔍 Validating Space Clicker assembly...");
            validationResults.Clear();
            
            // Validate different aspects
            ValidateProjectStructure();
            ValidateScripts();
            ValidatePrefabs();
            ValidateScene();
            ValidateReferences();
            ValidateSettings();
            
            Debug.Log($"✅ Validation completed: {validationResults.Count} results");
        }
        
        #region Validation Methods
        
        private void ValidateProjectStructure()
        {
            AddResult("Project Structure", "Validating folder structure...", ValidationStatus.Info);
            
            // Check required folders
            string[] requiredFolders = {
                "Assets/Scripts",
                "Assets/Prefabs",
                "Assets/Sprites",
                "Assets/Materials",
                "Assets/Textures",
                "Assets/Audio",
                "Assets/Scenes"
            };
            
            foreach (string folder in requiredFolders)
            {
                if (AssetDatabase.IsValidFolder(folder))
                {
                    AddResult("Project Structure", $"✅ {folder} exists", ValidationStatus.Success);
                }
                else
                {
                    AddResult("Project Structure", $"❌ Missing folder: {folder}", ValidationStatus.Error,
                        () => AssetDatabase.CreateFolder("Assets", folder.Substring(7)));
                }
            }
        }
        
        private void ValidateScripts()
        {
            AddResult("Scripts", "Validating scripts compilation...", ValidationStatus.Info);
            
            // Check for compilation errors
            if (EditorUtility.scriptCompilationFailed)
            {
                AddResult("Scripts", "❌ Script compilation failed", ValidationStatus.Error);
                return;
            }
            
            AddResult("Scripts", "✅ All scripts compiled successfully", ValidationStatus.Success);
            
            // Check core script types exist
            System.Type[] coreTypes = {
                typeof(GameManager),
                typeof(ResourceManager),
                typeof(UpgradeManager),
                typeof(MainUIController),
                typeof(AudioManager)
            };
            
            foreach (var type in coreTypes)
            {
                if (type != null)
                {
                    AddResult("Scripts", $"✅ {type.Name} found", ValidationStatus.Success);
                }
                else
                {
                    AddResult("Scripts", $"❌ {type.Name} not found", ValidationStatus.Error);
                }
            }
        }
        
        private void ValidatePrefabs()
        {
            AddResult("Prefabs", "Validating prefabs...", ValidationStatus.Info);
            
            string[] requiredPrefabs = {
                "Assets/Prefabs/GameManager.prefab",
                "Assets/Prefabs/ResourceManager.prefab",
                "Assets/Prefabs/UpgradeManager.prefab",
                "Assets/Prefabs/MainCanvas.prefab",
                "Assets/Prefabs/SolarPanelModule.prefab",
                "Assets/Prefabs/MiningDrillModule.prefab",
                "Assets/Prefabs/ResearchLabModule.prefab",
                "Assets/Prefabs/SpaceStationModule.prefab",
                "Assets/Prefabs/AudioManager.prefab"
            };
            
            foreach (string prefabPath in requiredPrefabs)
            {
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab != null)
                {
                    AddResult("Prefabs", $"✅ {prefab.name} exists", ValidationStatus.Success);
                    
                    // Validate prefab components
                    ValidatePrefabComponents(prefab);
                }
                else
                {
                    AddResult("Prefabs", $"❌ Missing prefab: {prefabPath}", ValidationStatus.Error);
                }
            }
        }
        
        private void ValidatePrefabComponents(GameObject prefab)
        {
            string prefabName = prefab.name;
            
            switch (prefabName)
            {
                case "GameManager":
                    if (prefab.GetComponent<GameManager>() == null)
                        AddResult("Prefabs", $"❌ {prefabName} missing GameManager component", ValidationStatus.Error);
                    break;
                    
                case "ResourceManager":
                    if (prefab.GetComponent<ResourceManager>() == null)
                        AddResult("Prefabs", $"❌ {prefabName} missing ResourceManager component", ValidationStatus.Error);
                    break;
                    
                case "MainCanvas":
                    if (prefab.GetComponent<Canvas>() == null)
                        AddResult("Prefabs", $"❌ {prefabName} missing Canvas component", ValidationStatus.Error);
                    if (prefab.GetComponent<MainUIController>() == null)
                        AddResult("Prefabs", $"❌ {prefabName} missing MainUIController component", ValidationStatus.Error);
                    break;
            }
        }
        
        private void ValidateScene()
        {
            AddResult("Scene", "Validating current scene...", ValidationStatus.Info);
            
            Scene currentScene = SceneManager.GetActiveScene();
            if (string.IsNullOrEmpty(currentScene.path))
            {
                AddResult("Scene", "⚠️ Scene not saved", ValidationStatus.Warning,
                    () => EditorSceneManager.SaveScene(currentScene, "Assets/Scenes/GameScene.unity"));
                return;
            }
            
            AddResult("Scene", $"✅ Scene: {currentScene.name}", ValidationStatus.Success);
            
            // Check required GameObjects in scene
            ValidateSceneObjects();
        }
        
        private void ValidateSceneObjects()
        {
            // Check for Camera
            Camera camera = FindObjectOfType<Camera>();
            if (camera != null)
            {
                AddResult("Scene", "✅ Main Camera found", ValidationStatus.Success);
            }
            else
            {
                AddResult("Scene", "❌ No Camera in scene", ValidationStatus.Error);
            }
            
            // Check for EventSystem
            UnityEngine.EventSystems.EventSystem eventSystem = FindObjectOfType<UnityEngine.EventSystems.EventSystem>();
            if (eventSystem != null)
            {
                AddResult("Scene", "✅ EventSystem found", ValidationStatus.Success);
            }
            else
            {
                AddResult("Scene", "❌ No EventSystem in scene", ValidationStatus.Error);
            }
            
            // Check for Core Managers
            GameManager gameManager = FindObjectOfType<GameManager>();
            ResourceManager resourceManager = FindObjectOfType<ResourceManager>();
            UpgradeManager upgradeManager = FindObjectOfType<UpgradeManager>();
            
            if (gameManager != null)
                AddResult("Scene", "✅ GameManager in scene", ValidationStatus.Success);
            else
                AddResult("Scene", "❌ GameManager missing from scene", ValidationStatus.Error);
                
            if (resourceManager != null)
                AddResult("Scene", "✅ ResourceManager in scene", ValidationStatus.Success);
            else
                AddResult("Scene", "❌ ResourceManager missing from scene", ValidationStatus.Error);
                
            if (upgradeManager != null)
                AddResult("Scene", "✅ UpgradeManager in scene", ValidationStatus.Success);
            else
                AddResult("Scene", "❌ UpgradeManager missing from scene", ValidationStatus.Error);
            
            // Check for UI
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas != null)
            {
                AddResult("Scene", "✅ Canvas found", ValidationStatus.Success);
                
                MainUIController mainUI = canvas.GetComponent<MainUIController>();
                if (mainUI != null)
                    AddResult("Scene", "✅ MainUIController on Canvas", ValidationStatus.Success);
                else
                    AddResult("Scene", "❌ MainUIController missing on Canvas", ValidationStatus.Error);
            }
            else
            {
                AddResult("Scene", "❌ No Canvas in scene", ValidationStatus.Error);
            }
        }
        
        private void ValidateReferences()
        {
            AddResult("References", "Validating component references...", ValidationStatus.Info);
            
            // Check GameSceneManager references
            GameSceneManager sceneManager = FindObjectOfType<GameSceneManager>();
            if (sceneManager != null)
            {
                if (sceneManager.gameManager != null)
                    AddResult("References", "✅ GameSceneManager.gameManager connected", ValidationStatus.Success);
                else
                    AddResult("References", "❌ GameSceneManager.gameManager not connected", ValidationStatus.Warning);
                    
                if (sceneManager.resourceManager != null)
                    AddResult("References", "✅ GameSceneManager.resourceManager connected", ValidationStatus.Success);
                else
                    AddResult("References", "❌ GameSceneManager.resourceManager not connected", ValidationStatus.Warning);
                    
                if (sceneManager.upgradeManager != null)
                    AddResult("References", "✅ GameSceneManager.upgradeManager connected", ValidationStatus.Success);
                else
                    AddResult("References", "❌ GameSceneManager.upgradeManager not connected", ValidationStatus.Warning);
            }
            
            // Check MainUIController references
            MainUIController mainUI = FindObjectOfType<MainUIController>();
            if (mainUI != null)
            {
                if (mainUI.upgradeContainer != null)
                    AddResult("References", "✅ MainUIController.upgradeContainer connected", ValidationStatus.Success);
                else
                    AddResult("References", "❌ MainUIController.upgradeContainer not connected", ValidationStatus.Warning);
            }
        }
        
        private void ValidateSettings()
        {
            AddResult("Settings", "Validating project settings...", ValidationStatus.Info);
            
            // Check build settings
            EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;
            if (scenes.Length > 0)
            {
                AddResult("Settings", $"✅ {scenes.Length} scene(s) in build settings", ValidationStatus.Success);
            }
            else
            {
                AddResult("Settings", "⚠️ No scenes in build settings", ValidationStatus.Warning,
                    () => {
                        Scene currentScene = SceneManager.GetActiveScene();
                        if (!string.IsNullOrEmpty(currentScene.path))
                        {
                            EditorBuildSettings.scenes = new EditorBuildSettingsScene[] {
                                new EditorBuildSettingsScene(currentScene.path, true)
                            };
                        }
                    });
            }
            
            // Check player settings
            if (!string.IsNullOrEmpty(PlayerSettings.productName))
            {
                AddResult("Settings", $"✅ Product Name: {PlayerSettings.productName}", ValidationStatus.Success);
            }
            else
            {
                AddResult("Settings", "⚠️ Product Name not set", ValidationStatus.Warning,
                    () => PlayerSettings.productName = "Space Clicker");
            }
            
            if (!string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                AddResult("Settings", $"✅ Company Name: {PlayerSettings.companyName}", ValidationStatus.Success);
            }
            else
            {
                AddResult("Settings", "⚠️ Company Name not set", ValidationStatus.Warning,
                    () => PlayerSettings.companyName = "Space Clicker Studio");
            }
        }
        
        #endregion
        
        #region UI Display
        
        private void DisplayResults()
        {
            GUILayout.Label("Validation Results:", EditorStyles.boldLabel);
            
            // Summary
            int successCount = 0, warningCount = 0, errorCount = 0;
            foreach (var result in validationResults)
            {
                switch (result.status)
                {
                    case ValidationStatus.Success: successCount++; break;
                    case ValidationStatus.Warning: warningCount++; break;
                    case ValidationStatus.Error: errorCount++; break;
                }
            }
            
            GUILayout.Label($"✅ Success: {successCount}  ⚠️ Warnings: {warningCount}  ❌ Errors: {errorCount}");
            GUILayout.Space(5);
            
            // Results list
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            string currentCategory = "";
            foreach (var result in validationResults)
            {
                if (showOnlyErrors && result.status != ValidationStatus.Error)
                    continue;
                
                if (result.category != currentCategory)
                {
                    currentCategory = result.category;
                    GUILayout.Space(5);
                    GUILayout.Label(currentCategory, EditorStyles.boldLabel);
                }
                
                GUILayout.BeginHorizontal();
                
                // Status color
                Color originalColor = GUI.color;
                switch (result.status)
                {
                    case ValidationStatus.Success: GUI.color = Color.green; break;
                    case ValidationStatus.Warning: GUI.color = Color.yellow; break;
                    case ValidationStatus.Error: GUI.color = Color.red; break;
                    case ValidationStatus.Info: GUI.color = Color.cyan; break;
                }
                
                GUILayout.Label("●", GUILayout.Width(20));
                GUI.color = originalColor;
                
                GUILayout.Label(result.message);
                
                // Fix button
                if (result.fixAction != null && GUILayout.Button("Fix", GUILayout.Width(50)))
                {
                    result.fixAction.Invoke();
                    ValidateAssembly(); // Re-validate after fix
                }
                
                GUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        #endregion
        
        #region Utility Methods
        
        private void AddResult(string category, string message, ValidationStatus status, System.Action fixAction = null)
        {
            validationResults.Add(new ValidationResult
            {
                category = category,
                message = message,
                status = status,
                fixAction = fixAction
            });
        }
        
        private void QuickFixAll()
        {
            Debug.Log("🔧 Applying quick fixes...");
            
            int fixedCount = 0;
            foreach (var result in validationResults)
            {
                if (result.fixAction != null && autoFix)
                {
                    try
                    {
                        result.fixAction.Invoke();
                        fixedCount++;
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Failed to apply fix: {e.Message}");
                    }
                }
            }
            
            Debug.Log($"✅ Applied {fixedCount} fixes");
            
            // Re-validate
            ValidateAssembly();
        }
        
        #endregion
    }
}
