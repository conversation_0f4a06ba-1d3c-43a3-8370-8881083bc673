# ChronoForge - Medieval Neo-Futuristic Rogue-like

![ChronoForge Logo](logo.png)

**ChronoForge** est un rogue-like 2D médiéval néo-futuriste développé avec Unity. Explorez des donjons cybernétiques, combattez des ennemis IA corrompues, et forgez votre destin dans un monde où magie et technologie se rencontrent.

## 🎮 **Caractéristiques du Jeu**

### **🏰 Thème Medieval Neo-Futuristic**
- **Fusion unique** entre éléments médiévaux et futuristes
- **3 Classes** : Cyber Warrior, Techno Mage, Dimensional Rogue
- **Armes hybrides** : Épées plasma, fusils quantiques, lames nano
- **Biomes variés** : Châteaux en ruines, catacombes cyber, laboratoires quantiques

### **⚔️ Combat Temps Réel**
- **Système de combat fluide** avec combos et critiques
- **Capacités spéciales** et ultimates uniques par classe
- **Dash tactique** pour esquiver et repositionnement
- **IA d'ennemis** avec 8 comportements différents

### **🌍 Génération Procédurale**
- **Niveaux générés** avec seed reproductible
- **10 types de salles** (Combat, Boss, Trésor, Shop, etc.)
- **Chemin principal garanti** avec exploration optionnelle
- **4 biomes thématiques** avec ambiances uniques

### **📈 Progression Avancée**
- **Système de niveaux** temporaires et permanents
- **Meta-upgrades** avec 5 catégories d'améliorations
- **Achievements** avec conditions variées
- **Contenu débloquable** (classes, armes, biomes, modes)

## 🛠️ **Installation et Setup**

### **Prérequis**
- **Unity 2022.3 LTS** ou plus récent
- **Visual Studio** ou **VS Code** avec extension C#
- **Git** pour le contrôle de version

### **Setup du Projet**

1. **Créer le projet Unity**
   ```bash
   # Ouvrir Unity Hub
   # Nouveau Projet > 2D Core (URP)
   # Nom: ChronoForge
   ```

2. **Importer les scripts**
   ```bash
   # Copier tous les fichiers .cs dans Assets/Scripts/
   # Respecter la structure des dossiers
   ```

3. **Configuration des dossiers**
   ```
   Assets/
   ├── Scripts/
   │   ├── Core/
   │   ├── Player/
   │   ├── Combat/
   │   ├── Procedural/
   │   ├── UI/
   │   ├── Audio/
   │   ├── Effects/
   │   ├── Progression/
   │   ├── Debug/
   │   └── Localization/
   ├── Prefabs/
   ├── Sprites/
   ├── Audio/
   ├── Materials/
   └── Scenes/
   ```

4. **Créer la scène principale**
   - Créer une nouvelle scène : `MainGame`
   - Ajouter les GameObjects principaux (voir section Architecture)

### **Architecture des GameObjects**

```
Hierarchy:
├── GameManager (GameManager.cs)
├── RunManager (RunManager.cs)
├── SaveSystem (SaveSystem.cs)
├── ProgressionManager (ProgressionManager.cs)
├── AchievementSystem (AchievementSystem.cs)
├── AudioManager (AudioManager.cs)
├── EffectsManager (EffectsManager.cs)
├── LevelGenerator (LevelGenerator.cs)
├── Player (PlayerController.cs, PlayerCombat.cs, PlayerStats.cs, HealthSystem.cs)
├── UI Canvas
│   ├── UIManager (UIManager.cs)
│   ├── HUD (HUDController.cs)
│   ├── Minimap (MinimapController.cs)
│   ├── Notifications (NotificationController.cs)
│   └── Menus (PauseMenuController.cs)
├── Debug Systems
│   ├── PerformanceMonitor (PerformanceMonitor.cs)
│   ├── DebugConsole (DebugConsole.cs)
│   └── GameSettings (GameSettings.cs)
└── Localization (LocalizationManager.cs)
```

## 🎯 **Guide de Développement**

### **1. Configuration Initiale**

1. **GameManager Setup**
   ```csharp
   // Créer un GameObject vide "GameManager"
   // Attacher le script GameManager.cs
   // Configurer les références dans l'inspecteur
   ```

2. **Player Setup**
   ```csharp
   // Créer un GameObject "Player"
   // Attacher PlayerController.cs, PlayerCombat.cs, PlayerStats.cs
   // Ajouter Rigidbody2D, Collider2D, SpriteRenderer
   // Configurer les paramètres dans l'inspecteur
   ```

3. **UI Setup**
   ```csharp
   // Créer un Canvas avec CanvasScaler
   // Attacher UIManager.cs au Canvas
   // Créer les panneaux UI (HUD, Pause, etc.)
   // Configurer les références
   ```

### **2. Systèmes Audio**

1. **AudioManager Configuration**
   ```csharp
   // Créer AudioMixerGroups dans Unity
   // Assigner les groupes dans AudioManager
   // Créer des AudioLibrary ScriptableObjects
   // Populer avec les clips audio
   ```

2. **Bibliothèques Audio**
   ```csharp
   // Créer Assets > ChronoForge > Audio Library
   // Organiser par catégories (Music, SFX, Ambient, UI)
   // Configurer les paramètres de chaque clip
   ```

### **3. Génération Procédurale**

1. **LevelGenerator Setup**
   ```csharp
   // Configurer la taille de grille (5x5 par défaut)
   // Créer des RoomTemplate ScriptableObjects
   // Définir les types de salles et leurs probabilités
   // Tester la génération avec différents seeds
   ```

2. **Room Templates**
   ```csharp
   // Créer Assets > ChronoForge > Room Template
   // Configurer spawn points pour ennemis/objets
   // Définir les connexions possibles
   // Assigner les prefabs appropriés
   ```

### **4. Progression et Achievements**

1. **ProgressionManager Setup**
   ```csharp
   // Configurer les courbes d'expérience
   // Définir les meta-upgrades disponibles
   // Créer le contenu débloquable
   // Tester la sauvegarde/chargement
   ```

2. **Achievement System**
   ```csharp
   // Définir les achievements dans AchievementSystem
   // Configurer les conditions de déblocage
   // Tester les notifications d'achievements
   ```

## 🔧 **Outils de Debug**

### **Console de Debug**
- **Touche** : `~` (tilde)
- **Commandes disponibles** :
  - `help` - Liste toutes les commandes
  - `god` - Active/désactive le mode dieu
  - `heal` - Soigne le joueur
  - `addexp <amount>` - Ajoute de l'expérience
  - `addcurrency <amount>` - Ajoute de la méta-monnaie
  - `newrun` - Démarre une nouvelle run
  - `timescale <value>` - Modifie la vitesse du jeu

### **Moniteur de Performance**
- **Touche** : `F3`
- **Affiche** : FPS, mémoire, draw calls
- **Seuils configurables** pour alertes de performance

### **Paramètres de Jeu**
- **Accès** : Menu Settings ou GameSettings.Instance
- **Sauvegarde automatique** des préférences
- **Support multi-langues**

## 🌍 **Localisation**

### **Langues Supportées**
- **Anglais** (par défaut)
- **Français**
- **Espagnol**
- **Allemand**
- **Japonais**

### **Ajouter une Nouvelle Langue**
1. Ajouter la langue dans `supportedLanguages`
2. Créer le fichier JSON dans `StreamingAssets/Localization/`
3. Implémenter `GetXXXData()` dans LocalizationManager
4. Tester avec `LocalizationManager.Instance.SetLanguage()`

## 📊 **Performance**

### **Optimisations Implémentées**
- **Object Pooling** pour effets et SFX
- **Cache système** pour audio et données
- **Génération à la demande** des salles
- **Batching intelligent** des événements

### **Recommandations**
- **Target FPS** : 60 FPS
- **Mémoire** : < 500MB sur mobile
- **Draw Calls** : < 100 par frame
- **Profiling** régulier avec Unity Profiler

## 🎨 **Art et Assets**

### **Style Visuel**
- **Pixel Art** 2D avec palette limitée
- **Thème sombre** avec accents néon
- **Animations fluides** à 12-24 FPS
- **Effets de particules** pour feedback

### **Audio**
- **Musique synthwave** avec éléments orchestraux
- **SFX** métalliques et électroniques
- **Ambiances** immersives par biome
- **Audio spatialisé** pour immersion

## 🚀 **Déploiement**

### **Plateformes Cibles**
- **PC** (Windows, Mac, Linux)
- **Mobile** (Android, iOS)
- **Consoles** (Nintendo Switch, PlayStation, Xbox)

### **Build Settings**
```csharp
// Player Settings recommandés
Company Name: YourStudio
Product Name: ChronoForge
Version: 1.0.0
Bundle Identifier: com.yourstudio.chronoforge

// Graphics
Color Space: Linear
Rendering Path: URP
Target Frame Rate: 60
```

## 📝 **Contribution**

### **Standards de Code**
- **Naming** : PascalCase pour classes, camelCase pour variables
- **Documentation** : Commentaires XML pour méthodes publiques
- **Architecture** : Patterns SOLID et séparation des responsabilités
- **Tests** : Unit tests pour logique critique

### **Git Workflow**
```bash
# Branches principales
main        # Version stable
develop     # Développement actif
feature/*   # Nouvelles fonctionnalités
hotfix/*    # Corrections urgentes
```

## 📞 **Support**

### **Documentation**
- **Wiki** : Documentation détaillée des systèmes
- **API Reference** : Documentation des classes publiques
- **Tutorials** : Guides pas-à-pas pour développeurs

### **Contact**
- **Issues** : GitHub Issues pour bugs et suggestions
- **Discord** : Serveur communautaire pour discussions
- **Email** : <EMAIL>

---

**🌟 ChronoForge - Forgez votre destin dans le temps ! 🌟**

*Développé avec ❤️ et Unity*
