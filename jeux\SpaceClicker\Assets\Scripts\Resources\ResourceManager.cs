using System;
using System.Collections.Generic;
using UnityEngine;
using System.Collections;
using System.Numerics;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Types de ressources disponibles dans le jeu
    /// </summary>
    public enum ResourceType
    {
        Energy,         // Énergie - ressource de base
        Minerals,       // Minéraux - pour les upgrades
        ResearchData,   // Données de recherche - pour les technologies
        SpaceCurrency   // Monnaie premium - IAP
    }
    
    /// <summary>
    /// Structure pour stocker les récompenses hors ligne
    /// </summary>
    [System.Serializable]
    public class OfflineRewards
    {
        public BigInteger energy;
        public BigInteger minerals;
        public BigInteger researchData;
        
        public bool HasRewards()
        {
            return energy > 0 || minerals > 0 || researchData > 0;
        }
    }
    
    /// <summary>
    /// Gestionnaire des ressources du jeu
    /// </summary>
    public class ResourceManager : MonoBehaviour
    {
        [Header("Production Settings")]
        public float baseProductionRate = 1.0f;
        public float idleProductionInterval = 1.0f;
        public float clickMultiplier = 1.0f;
        
        [Header("Debug")]
        public bool showDebugInfo = false;
        
        // Ressources actuelles
        private Dictionary<ResourceType, BigInteger> resources;
        
        // Taux de production par seconde
        private Dictionary<ResourceType, float> productionRates;
        
        // Multiplicateurs globaux
        private Dictionary<ResourceType, float> globalMultipliers;
        
        // Coroutine de production
        private Coroutine idleProductionCoroutine;
        
        // Events
        public static event Action<ResourceType, BigInteger> OnResourceChanged;
        public static event Action<ResourceType, BigInteger> OnResourceGained;
        
        #region Initialization
        
        public void Initialize()
        {
            Debug.Log("🔋 Initializing ResourceManager...");
            
            // Initialiser les dictionnaires
            resources = new Dictionary<ResourceType, BigInteger>();
            productionRates = new Dictionary<ResourceType, float>();
            globalMultipliers = new Dictionary<ResourceType, float>();
            
            // Initialiser les ressources à zéro
            foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
            {
                resources[type] = BigInteger.Zero;
                productionRates[type] = 0f;
                globalMultipliers[type] = 1f;
            }
            
            // Production de base pour l'énergie
            productionRates[ResourceType.Energy] = baseProductionRate;
            
            Debug.Log("✅ ResourceManager initialized");
        }
        
        #endregion
        
        #region Resource Management
        
        /// <summary>
        /// Ajoute une quantité de ressource
        /// </summary>
        public void AddResource(ResourceType type, BigInteger amount)
        {
            if (amount <= 0) return;
            
            resources[type] += amount;
            
            OnResourceChanged?.Invoke(type, resources[type]);
            OnResourceGained?.Invoke(type, amount);
            
            if (showDebugInfo)
            {
                Debug.Log($"💰 Added {amount} {type}. Total: {resources[type]}");
            }
        }
        
        /// <summary>
        /// Retire une quantité de ressource
        /// </summary>
        public bool SpendResource(ResourceType type, BigInteger amount)
        {
            if (!CanAfford(type, amount))
                return false;
            
            resources[type] -= amount;
            OnResourceChanged?.Invoke(type, resources[type]);
            
            if (showDebugInfo)
            {
                Debug.Log($"💸 Spent {amount} {type}. Remaining: {resources[type]}");
            }
            
            return true;
        }
        
        /// <summary>
        /// Vérifie si on peut se permettre un coût
        /// </summary>
        public bool CanAfford(ResourceType type, BigInteger cost)
        {
            return resources[type] >= cost;
        }
        
        /// <summary>
        /// Obtient la quantité actuelle d'une ressource
        /// </summary>
        public BigInteger GetResource(ResourceType type)
        {
            return resources[type];
        }
        
        /// <summary>
        /// Obtient toutes les ressources
        /// </summary>
        public Dictionary<ResourceType, BigInteger> GetAllResources()
        {
            return new Dictionary<ResourceType, BigInteger>(resources);
        }
        
        #endregion
        
        #region Production System
        
        /// <summary>
        /// Démarre la production automatique
        /// </summary>
        public void StartIdleProduction()
        {
            if (idleProductionCoroutine != null)
                StopCoroutine(idleProductionCoroutine);
            
            idleProductionCoroutine = StartCoroutine(IdleProductionCoroutine());
            Debug.Log("⚡ Idle production started");
        }
        
        /// <summary>
        /// Arrête la production automatique
        /// </summary>
        public void StopIdleProduction()
        {
            if (idleProductionCoroutine != null)
            {
                StopCoroutine(idleProductionCoroutine);
                idleProductionCoroutine = null;
            }
            Debug.Log("⏹️ Idle production stopped");
        }
        
        /// <summary>
        /// Coroutine de production automatique
        /// </summary>
        private IEnumerator IdleProductionCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(idleProductionInterval);
                
                // Produire chaque type de ressource
                foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
                {
                    if (type == ResourceType.SpaceCurrency) continue; // Pas de production auto pour la monnaie premium
                    
                    float rate = GetProductionRate(type);
                    if (rate > 0)
                    {
                        BigInteger production = new BigInteger(rate * idleProductionInterval);
                        AddResource(type, production);
                    }
                }
            }
        }
        
        /// <summary>
        /// Obtient le taux de production actuel pour une ressource
        /// </summary>
        public float GetProductionRate(ResourceType type)
        {
            return productionRates[type] * globalMultipliers[type];
        }
        
        /// <summary>
        /// Met à jour le taux de production de base
        /// </summary>
        public void SetBaseProductionRate(ResourceType type, float rate)
        {
            productionRates[type] = rate;
        }
        
        /// <summary>
        /// Ajoute un bonus de production
        /// </summary>
        public void AddProductionBonus(ResourceType type, float bonus)
        {
            productionRates[type] += bonus;
        }
        
        /// <summary>
        /// Met à jour le multiplicateur global
        /// </summary>
        public void SetGlobalMultiplier(ResourceType type, float multiplier)
        {
            globalMultipliers[type] = multiplier;
        }
        
        #endregion
        
        #region Click System
        
        /// <summary>
        /// Gère un clic manuel sur une ressource
        /// </summary>
        public void OnResourceClicked(ResourceType type)
        {
            BigInteger clickValue = CalculateClickValue(type);
            AddResource(type, clickValue);
            
            // Effet visuel et sonore
            // TODO: Déclencher effets de particules et son
        }
        
        /// <summary>
        /// Calcule la valeur d'un clic
        /// </summary>
        private BigInteger CalculateClickValue(ResourceType type)
        {
            float baseValue = 1f;
            
            // Valeur de base selon le type
            switch (type)
            {
                case ResourceType.Energy:
                    baseValue = 1f;
                    break;
                case ResourceType.Minerals:
                    baseValue = 0.1f;
                    break;
                case ResourceType.ResearchData:
                    baseValue = 0.05f;
                    break;
            }
            
            // Appliquer les multiplicateurs
            float finalValue = baseValue * clickMultiplier * globalMultipliers[type];
            
            return new BigInteger(Mathf.Max(1, finalValue));
        }
        
        /// <summary>
        /// Met à jour le multiplicateur de clic
        /// </summary>
        public void SetClickMultiplier(float multiplier)
        {
            clickMultiplier = multiplier;
        }
        
        #endregion
        
        #region Offline Progress
        
        /// <summary>
        /// Calcule la production hors ligne
        /// </summary>
        public OfflineRewards CalculateOfflineProduction(float offlineSeconds)
        {
            OfflineRewards rewards = new OfflineRewards();
            
            // Calculer pour chaque ressource
            float energyRate = GetProductionRate(ResourceType.Energy);
            float mineralsRate = GetProductionRate(ResourceType.Minerals);
            float researchRate = GetProductionRate(ResourceType.ResearchData);
            
            rewards.energy = new BigInteger(energyRate * offlineSeconds);
            rewards.minerals = new BigInteger(mineralsRate * offlineSeconds);
            rewards.researchData = new BigInteger(researchRate * offlineSeconds);
            
            // Appliquer les récompenses
            if (rewards.energy > 0) AddResource(ResourceType.Energy, rewards.energy);
            if (rewards.minerals > 0) AddResource(ResourceType.Minerals, rewards.minerals);
            if (rewards.researchData > 0) AddResource(ResourceType.ResearchData, rewards.researchData);
            
            return rewards;
        }
        
        #endregion
        
        #region Save/Load
        
        /// <summary>
        /// Sauvegarde les données des ressources
        /// </summary>
        public ResourceSaveData GetSaveData()
        {
            ResourceSaveData saveData = new ResourceSaveData();
            
            saveData.energy = resources[ResourceType.Energy].ToString();
            saveData.minerals = resources[ResourceType.Minerals].ToString();
            saveData.researchData = resources[ResourceType.ResearchData].ToString();
            saveData.spaceCurrency = resources[ResourceType.SpaceCurrency].ToString();
            
            saveData.energyProductionRate = productionRates[ResourceType.Energy];
            saveData.mineralsProductionRate = productionRates[ResourceType.Minerals];
            saveData.researchProductionRate = productionRates[ResourceType.ResearchData];
            
            saveData.clickMultiplier = clickMultiplier;
            
            return saveData;
        }
        
        /// <summary>
        /// Charge les données des ressources
        /// </summary>
        public void LoadSaveData(ResourceSaveData saveData)
        {
            if (saveData == null) return;
            
            // Charger les quantités
            if (BigInteger.TryParse(saveData.energy, out BigInteger energy))
                resources[ResourceType.Energy] = energy;
            
            if (BigInteger.TryParse(saveData.minerals, out BigInteger minerals))
                resources[ResourceType.Minerals] = minerals;
            
            if (BigInteger.TryParse(saveData.researchData, out BigInteger research))
                resources[ResourceType.ResearchData] = research;
            
            if (BigInteger.TryParse(saveData.spaceCurrency, out BigInteger currency))
                resources[ResourceType.SpaceCurrency] = currency;
            
            // Charger les taux de production
            productionRates[ResourceType.Energy] = saveData.energyProductionRate;
            productionRates[ResourceType.Minerals] = saveData.mineralsProductionRate;
            productionRates[ResourceType.ResearchData] = saveData.researchProductionRate;
            
            clickMultiplier = saveData.clickMultiplier;
            
            // Notifier les changements
            foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
            {
                OnResourceChanged?.Invoke(type, resources[type]);
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Structure de sauvegarde pour les ressources
    /// </summary>
    [System.Serializable]
    public class ResourceSaveData
    {
        public string energy = "0";
        public string minerals = "0";
        public string researchData = "0";
        public string spaceCurrency = "0";
        
        public float energyProductionRate = 1f;
        public float mineralsProductionRate = 0f;
        public float researchProductionRate = 0f;
        
        public float clickMultiplier = 1f;
    }
}
