"""
Gestionnaire de contenu pour le bot de publication automatisé.
Charge et valide les posts depuis les fichiers YAML/JSON/MD.
"""

import os
import yaml
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class Post:
    """Représente un post à publier."""
    id: str
    title: str
    body: str
    platforms: List[str]
    schedule: str
    tags: List[str] = field(default_factory=list)
    subreddit: Optional[str] = None
    image: Optional[str] = None
    published_on: List[str] = field(default_factory=list)
    last_published: Optional[datetime] = None
    repost_count: int = 0
    
    def __post_init__(self):
        """Validation après initialisation."""
        if not self.platforms:
            raise ValueError(f"Post {self.id}: au moins une plateforme requise")
        
        valid_platforms = ["twitter", "reddit", "linkedin", "devto"]
        for platform in self.platforms:
            if platform not in valid_platforms:
                raise ValueError(f"Post {self.id}: plateforme '{platform}' non supportée")

class ContentManager:
    """Gestionnaire de contenu pour charger et gérer les posts."""
    
    def __init__(self, posts_directory: str = "posts"):
        self.posts_directory = Path(posts_directory)
        self.posts: List[Post] = []
        self.published_log: Dict[str, Dict] = {}
        
    def load_posts(self) -> List[Post]:
        """Charge tous les posts depuis le répertoire de posts."""
        self.posts = []
        
        if not self.posts_directory.exists():
            logger.warning(f"Répertoire de posts {self.posts_directory} introuvable")
            return self.posts
            
        # Charger les fichiers YAML
        for yaml_file in self.posts_directory.glob("*.yaml"):
            try:
                self._load_yaml_file(yaml_file)
            except Exception as e:
                logger.error(f"Erreur lors du chargement de {yaml_file}: {e}")
                
        # Charger les fichiers JSON
        for json_file in self.posts_directory.glob("*.json"):
            try:
                self._load_json_file(json_file)
            except Exception as e:
                logger.error(f"Erreur lors du chargement de {json_file}: {e}")
                
        logger.info(f"Chargé {len(self.posts)} posts")
        return self.posts
    
    def _load_yaml_file(self, file_path: Path):
        """Charge les posts depuis un fichier YAML."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            
        if 'posts' in data:
            for post_data in data['posts']:
                post = self._create_post_from_dict(post_data)
                self.posts.append(post)
    
    def _load_json_file(self, file_path: Path):
        """Charge les posts depuis un fichier JSON."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        if 'posts' in data:
            for post_data in data['posts']:
                post = self._create_post_from_dict(post_data)
                self.posts.append(post)
    
    def _create_post_from_dict(self, post_data: Dict[str, Any]) -> Post:
        """Crée un objet Post depuis un dictionnaire."""
        return Post(
            id=post_data['id'],
            title=post_data['title'],
            body=post_data['body'],
            platforms=post_data['platforms'],
            schedule=post_data['schedule'],
            tags=post_data.get('tags', []),
            subreddit=post_data.get('subreddit'),
            image=post_data.get('image'),
            published_on=post_data.get('published_on', []),
            repost_count=post_data.get('repost_count', 0)
        )
    
    def get_scheduled_posts(self, target_time: datetime = None) -> List[Post]:
        """Retourne les posts programmés pour une heure donnée."""
        if target_time is None:
            target_time = datetime.now()
            
        scheduled_posts = []
        
        for post in self.posts:
            if self._should_publish_now(post, target_time):
                scheduled_posts.append(post)
                
        return scheduled_posts
    
    def _should_publish_now(self, post: Post, target_time: datetime) -> bool:
        """Détermine si un post doit être publié maintenant."""
        if post.schedule == "recurring":
            return self._check_recurring_schedule(post, target_time)
        
        try:
            scheduled_time = datetime.fromisoformat(post.schedule.replace('Z', '+00:00'))
            # Tolérance de 5 minutes
            time_diff = abs((target_time - scheduled_time).total_seconds())
            return time_diff <= 300  # 5 minutes
        except ValueError:
            logger.error(f"Format de date invalide pour le post {post.id}: {post.schedule}")
            return False
    
    def _check_recurring_schedule(self, post: Post, target_time: datetime) -> bool:
        """Vérifie si un post récurrent doit être publié."""
        if post.last_published is None:
            return True
            
        frequency = getattr(post, 'frequency', 'weekly')
        
        if frequency == 'daily':
            return (target_time - post.last_published).days >= 1
        elif frequency == 'weekly':
            return (target_time - post.last_published).days >= 7
        elif frequency == 'monthly':
            return (target_time - post.last_published).days >= 30
            
        return False
    
    def mark_as_published(self, post: Post, platform: str, success: bool = True):
        """Marque un post comme publié sur une plateforme."""
        if success:
            if platform not in post.published_on:
                post.published_on.append(platform)
            post.last_published = datetime.now()
            
        # Log de publication
        if post.id not in self.published_log:
            self.published_log[post.id] = {}
            
        self.published_log[post.id][platform] = {
            'timestamp': datetime.now().isoformat(),
            'success': success
        }
    
    def get_posts_for_platform(self, platform: str) -> List[Post]:
        """Retourne tous les posts configurés pour une plateforme."""
        return [post for post in self.posts if platform in post.platforms]
    
    def get_post_by_id(self, post_id: str) -> Optional[Post]:
        """Retourne un post par son ID."""
        for post in self.posts:
            if post.id == post_id:
                return post
        return None
    
    def save_published_log(self, log_file: str = "published.json"):
        """Sauvegarde le log des publications."""
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.published_log, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde du log: {e}")
    
    def load_published_log(self, log_file: str = "published.json"):
        """Charge le log des publications."""
        try:
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    self.published_log = json.load(f)
        except Exception as e:
            logger.error(f"Erreur lors du chargement du log: {e}")
