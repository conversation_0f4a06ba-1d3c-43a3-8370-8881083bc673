using UnityEngine;
using ChronoForge.Audio;

namespace ChronoForge.Core
{
    /// <summary>
    /// Gestionnaire de paramètres globaux pour ChronoForge
    /// </summary>
    public class GameSettings : MonoBehaviour
    {
        [Header("Graphics Settings")]
        public int targetFrameRate = 60;
        public bool enableVSync = true;
        public int qualityLevel = 2;
        public bool fullscreen = true;
        public Vector2Int resolution = new Vector2Int(1920, 1080);
        
        [Header("Audio Settings")]
        [Range(0f, 1f)]
        public float masterVolume = 1f;
        [Range(0f, 1f)]
        public float musicVolume = 0.7f;
        [Range(0f, 1f)]
        public float sfxVolume = 0.8f;
        [Range(0f, 1f)]
        public float ambientVolume = 0.5f;
        [Range(0f, 1f)]
        public float uiVolume = 1f;
        
        [Header("Gameplay Settings")]
        public bool showDamageNumbers = true;
        public bool screenShakeEnabled = true;
        public bool autoAim = false;
        [Range(0.1f, 3f)]
        public float mouseSensitivity = 1f;
        public bool pauseOnFocusLoss = true;
        
        [Header("UI Settings")]
        public bool showMinimap = true;
        public bool showHealthBar = true;
        public bool showCrosshair = true;
        [Range(0.5f, 2f)]
        public float uiScale = 1f;
        public bool showFPS = false;
        
        [Header("Accessibility")]
        public bool colorBlindMode = false;
        public bool highContrastMode = false;
        [Range(0.8f, 1.5f)]
        public float textSize = 1f;
        public bool subtitlesEnabled = false;
        
        [Header("Debug Settings")]
        public bool debugMode = false;
        public bool showDebugInfo = false;
        public bool enableCheats = false;
        
        // Events
        public static System.Action OnSettingsChanged;
        public static System.Action<float> OnVolumeChanged;
        public static System.Action<bool> OnGraphicsChanged;
        
        // Singleton
        public static GameSettings Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSettings();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            ApplyAllSettings();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeSettings()
        {
            LoadSettings();
            UnityEngine.Debug.Log("⚙️ GameSettings initialized");
        }
        
        #endregion
        
        #region Settings Management
        
        public void LoadSettings()
        {
            // Graphics
            targetFrameRate = PlayerPrefs.GetInt("TargetFrameRate", 60);
            enableVSync = PlayerPrefs.GetInt("VSync", 1) == 1;
            qualityLevel = PlayerPrefs.GetInt("QualityLevel", 2);
            fullscreen = PlayerPrefs.GetInt("Fullscreen", 1) == 1;
            
            string resolutionStr = PlayerPrefs.GetString("Resolution", "1920x1080");
            ParseResolution(resolutionStr);
            
            // Audio
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 0.8f);
            ambientVolume = PlayerPrefs.GetFloat("AmbientVolume", 0.5f);
            uiVolume = PlayerPrefs.GetFloat("UIVolume", 1f);
            
            // Gameplay
            showDamageNumbers = PlayerPrefs.GetInt("ShowDamageNumbers", 1) == 1;
            screenShakeEnabled = PlayerPrefs.GetInt("ScreenShake", 1) == 1;
            autoAim = PlayerPrefs.GetInt("AutoAim", 0) == 1;
            mouseSensitivity = PlayerPrefs.GetFloat("MouseSensitivity", 1f);
            pauseOnFocusLoss = PlayerPrefs.GetInt("PauseOnFocusLoss", 1) == 1;
            
            // UI
            showMinimap = PlayerPrefs.GetInt("ShowMinimap", 1) == 1;
            showHealthBar = PlayerPrefs.GetInt("ShowHealthBar", 1) == 1;
            showCrosshair = PlayerPrefs.GetInt("ShowCrosshair", 1) == 1;
            uiScale = PlayerPrefs.GetFloat("UIScale", 1f);
            showFPS = PlayerPrefs.GetInt("ShowFPS", 0) == 1;
            
            // Accessibility
            colorBlindMode = PlayerPrefs.GetInt("ColorBlindMode", 0) == 1;
            highContrastMode = PlayerPrefs.GetInt("HighContrastMode", 0) == 1;
            textSize = PlayerPrefs.GetFloat("TextSize", 1f);
            subtitlesEnabled = PlayerPrefs.GetInt("SubtitlesEnabled", 0) == 1;
            
            // Debug
            debugMode = PlayerPrefs.GetInt("DebugMode", 0) == 1;
            showDebugInfo = PlayerPrefs.GetInt("ShowDebugInfo", 0) == 1;
            enableCheats = PlayerPrefs.GetInt("EnableCheats", 0) == 1;
            
            UnityEngine.Debug.Log("📂 Settings loaded from PlayerPrefs");
        }
        
        public void SaveSettings()
        {
            // Graphics
            PlayerPrefs.SetInt("TargetFrameRate", targetFrameRate);
            PlayerPrefs.SetInt("VSync", enableVSync ? 1 : 0);
            PlayerPrefs.SetInt("QualityLevel", qualityLevel);
            PlayerPrefs.SetInt("Fullscreen", fullscreen ? 1 : 0);
            PlayerPrefs.SetString("Resolution", $"{resolution.x}x{resolution.y}");
            
            // Audio
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", sfxVolume);
            PlayerPrefs.SetFloat("AmbientVolume", ambientVolume);
            PlayerPrefs.SetFloat("UIVolume", uiVolume);
            
            // Gameplay
            PlayerPrefs.SetInt("ShowDamageNumbers", showDamageNumbers ? 1 : 0);
            PlayerPrefs.SetInt("ScreenShake", screenShakeEnabled ? 1 : 0);
            PlayerPrefs.SetInt("AutoAim", autoAim ? 1 : 0);
            PlayerPrefs.SetFloat("MouseSensitivity", mouseSensitivity);
            PlayerPrefs.SetInt("PauseOnFocusLoss", pauseOnFocusLoss ? 1 : 0);
            
            // UI
            PlayerPrefs.SetInt("ShowMinimap", showMinimap ? 1 : 0);
            PlayerPrefs.SetInt("ShowHealthBar", showHealthBar ? 1 : 0);
            PlayerPrefs.SetInt("ShowCrosshair", showCrosshair ? 1 : 0);
            PlayerPrefs.SetFloat("UIScale", uiScale);
            PlayerPrefs.SetInt("ShowFPS", showFPS ? 1 : 0);
            
            // Accessibility
            PlayerPrefs.SetInt("ColorBlindMode", colorBlindMode ? 1 : 0);
            PlayerPrefs.SetInt("HighContrastMode", highContrastMode ? 1 : 0);
            PlayerPrefs.SetFloat("TextSize", textSize);
            PlayerPrefs.SetInt("SubtitlesEnabled", subtitlesEnabled ? 1 : 0);
            
            // Debug
            PlayerPrefs.SetInt("DebugMode", debugMode ? 1 : 0);
            PlayerPrefs.SetInt("ShowDebugInfo", showDebugInfo ? 1 : 0);
            PlayerPrefs.SetInt("EnableCheats", enableCheats ? 1 : 0);
            
            PlayerPrefs.Save();
            
            UnityEngine.Debug.Log("💾 Settings saved to PlayerPrefs");
        }
        
        public void ApplyAllSettings()
        {
            ApplyGraphicsSettings();
            ApplyAudioSettings();
            ApplyGameplaySettings();
            ApplyUISettings();
            ApplyAccessibilitySettings();
            
            OnSettingsChanged?.Invoke();
        }
        
        #endregion
        
        #region Graphics Settings
        
        public void ApplyGraphicsSettings()
        {
            // Frame rate
            Application.targetFrameRate = targetFrameRate;
            
            // VSync
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;
            
            // Quality level
            QualitySettings.SetQualityLevel(qualityLevel);
            
            // Resolution and fullscreen
            Screen.SetResolution(resolution.x, resolution.y, fullscreen);
            
            OnGraphicsChanged?.Invoke(true);
            
            UnityEngine.Debug.Log($"🎮 Graphics settings applied: {resolution.x}x{resolution.y}, Quality: {qualityLevel}");
        }
        
        public void SetResolution(int width, int height, bool isFullscreen)
        {
            resolution = new Vector2Int(width, height);
            fullscreen = isFullscreen;
            Screen.SetResolution(width, height, isFullscreen);
            SaveSettings();
        }
        
        public void SetQualityLevel(int level)
        {
            qualityLevel = Mathf.Clamp(level, 0, QualitySettings.names.Length - 1);
            QualitySettings.SetQualityLevel(qualityLevel);
            SaveSettings();
        }
        
        public void SetFrameRate(int frameRate)
        {
            targetFrameRate = frameRate;
            Application.targetFrameRate = frameRate;
            SaveSettings();
        }
        
        public void SetVSync(bool enabled)
        {
            enableVSync = enabled;
            QualitySettings.vSyncCount = enabled ? 1 : 0;
            SaveSettings();
        }
        
        #endregion
        
        #region Audio Settings
        
        public void ApplyAudioSettings()
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(masterVolume);
                AudioManager.Instance.SetMusicVolume(musicVolume);
                AudioManager.Instance.SetSFXVolume(sfxVolume);
                AudioManager.Instance.SetAmbientVolume(ambientVolume);
                AudioManager.Instance.SetUIVolume(uiVolume);
            }
            
            OnVolumeChanged?.Invoke(masterVolume);
            
            UnityEngine.Debug.Log("🔊 Audio settings applied");
        }
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            if (AudioManager.Instance != null)
                AudioManager.Instance.SetMasterVolume(masterVolume);
            SaveSettings();
        }
        
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (AudioManager.Instance != null)
                AudioManager.Instance.SetMusicVolume(musicVolume);
            SaveSettings();
        }
        
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            if (AudioManager.Instance != null)
                AudioManager.Instance.SetSFXVolume(sfxVolume);
            SaveSettings();
        }
        
        #endregion
        
        #region Gameplay Settings
        
        public void ApplyGameplaySettings()
        {
            // These would be applied to relevant systems
            UnityEngine.Debug.Log("🎯 Gameplay settings applied");
        }
        
        public void SetMouseSensitivity(float sensitivity)
        {
            mouseSensitivity = Mathf.Clamp(sensitivity, 0.1f, 3f);
            SaveSettings();
        }
        
        public void SetAutoAim(bool enabled)
        {
            autoAim = enabled;
            SaveSettings();
        }
        
        #endregion
        
        #region UI Settings
        
        public void ApplyUISettings()
        {
            // Apply UI scale to Canvas Scalers
            Canvas[] canvases = FindObjectsByType<Canvas>(FindObjectsSortMode.None);
            foreach (Canvas canvas in canvases)
            {
                UnityEngine.UI.CanvasScaler scaler = canvas.GetComponent<UnityEngine.UI.CanvasScaler>();
                if (scaler != null)
                {
                    scaler.scaleFactor = uiScale;
                }
            }
            
            UnityEngine.Debug.Log("🎨 UI settings applied");
        }
        
        public void SetUIScale(float scale)
        {
            uiScale = Mathf.Clamp(scale, 0.5f, 2f);
            ApplyUISettings();
            SaveSettings();
        }
        
        #endregion
        
        #region Accessibility Settings
        
        public void ApplyAccessibilitySettings()
        {
            // Apply accessibility settings to relevant systems
            UnityEngine.Debug.Log("♿ Accessibility settings applied");
        }
        
        public void SetColorBlindMode(bool enabled)
        {
            colorBlindMode = enabled;
            // Apply color blind friendly palette
            SaveSettings();
        }
        
        public void SetHighContrastMode(bool enabled)
        {
            highContrastMode = enabled;
            // Apply high contrast theme
            SaveSettings();
        }
        
        #endregion
        
        #region Utility Methods
        
        private void ParseResolution(string resolutionStr)
        {
            string[] parts = resolutionStr.Split('x');
            if (parts.Length == 2)
            {
                if (int.TryParse(parts[0], out int width) && int.TryParse(parts[1], out int height))
                {
                    resolution = new Vector2Int(width, height);
                }
            }
        }
        
        public void ResetToDefaults()
        {
            // Graphics
            targetFrameRate = 60;
            enableVSync = true;
            qualityLevel = 2;
            fullscreen = true;
            resolution = new Vector2Int(1920, 1080);
            
            // Audio
            masterVolume = 1f;
            musicVolume = 0.7f;
            sfxVolume = 0.8f;
            ambientVolume = 0.5f;
            uiVolume = 1f;
            
            // Gameplay
            showDamageNumbers = true;
            screenShakeEnabled = true;
            autoAim = false;
            mouseSensitivity = 1f;
            pauseOnFocusLoss = true;
            
            // UI
            showMinimap = true;
            showHealthBar = true;
            showCrosshair = true;
            uiScale = 1f;
            showFPS = false;
            
            // Accessibility
            colorBlindMode = false;
            highContrastMode = false;
            textSize = 1f;
            subtitlesEnabled = false;
            
            // Debug
            debugMode = false;
            showDebugInfo = false;
            enableCheats = false;
            
            ApplyAllSettings();
            SaveSettings();
            
            UnityEngine.Debug.Log("🔄 Settings reset to defaults");
        }
        
        #endregion
        
        #region Public Getters
        
        public bool IsDebugMode() => debugMode;
        public bool AreCheatsEnabled() => enableCheats;
        public bool ShouldShowFPS() => showFPS;
        public bool ShouldShowDamageNumbers() => showDamageNumbers;
        public bool IsScreenShakeEnabled() => screenShakeEnabled;
        public bool ShouldPauseOnFocusLoss() => pauseOnFocusLoss;
        
        #endregion
    }
}
