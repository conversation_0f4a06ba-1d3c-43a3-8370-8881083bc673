# -*- coding: utf-8 -*-
# File generated from our OpenAPI spec
from stripe._list_object import ListObject
from stripe._listable_api_resource import ListableAPIResource
from stripe._request_options import RequestOptions
from stripe._util import class_method_variant, sanitize_id
from typing import ClassVar, Dict, List, Optional, cast, overload
from typing_extensions import Literal, NotRequired, Unpack


class InvoiceRenderingTemplate(
    ListableAPIResource["InvoiceRenderingTemplate"]
):
    """
    Invoice Rendering Templates are used to configure how invoices are rendered on surfaces like the PDF. Invoice Rendering Templates
    can be created from within the Dashboard, and they can be used over the API when creating invoices.
    """

    OBJECT_NAME: ClassVar[Literal["invoice_rendering_template"]] = (
        "invoice_rendering_template"
    )

    class ArchiveParams(RequestOptions):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """

    class ListParams(RequestOptions):
        ending_before: NotRequired[str]
        """
        A cursor for use in pagination. `ending_before` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with `obj_bar`, your subsequent call can include `ending_before=obj_bar` in order to fetch the previous page of the list.
        """
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        limit: NotRequired[int]
        """
        A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.
        """
        starting_after: NotRequired[str]
        """
        A cursor for use in pagination. `starting_after` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with `obj_foo`, your subsequent call can include `starting_after=obj_foo` in order to fetch the next page of the list.
        """
        status: NotRequired[Literal["active", "archived"]]

    class RetrieveParams(RequestOptions):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        version: NotRequired[int]

    class UnarchiveParams(RequestOptions):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """

    created: int
    """
    Time at which the object was created. Measured in seconds since the Unix epoch.
    """
    id: str
    """
    Unique identifier for the object.
    """
    livemode: bool
    """
    Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
    """
    metadata: Optional[Dict[str, str]]
    """
    Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format.
    """
    nickname: Optional[str]
    """
    A brief description of the template, hidden from customers
    """
    object: Literal["invoice_rendering_template"]
    """
    String representing the object's type. Objects of the same type share the same value.
    """
    status: Literal["active", "archived"]
    """
    The status of the template, one of `active` or `archived`.
    """
    version: int
    """
    Version of this template; version increases by one when an update on the template changes any field that controls invoice rendering
    """

    @classmethod
    def _cls_archive(
        cls,
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        return cast(
            "InvoiceRenderingTemplate",
            cls._static_request(
                "post",
                "/v1/invoice_rendering_templates/{template}/archive".format(
                    template=sanitize_id(template)
                ),
                params=params,
            ),
        )

    @overload
    @staticmethod
    def archive(
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        ...

    @overload
    def archive(
        self, **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        ...

    @class_method_variant("_cls_archive")
    def archive(  # pyright: ignore[reportGeneralTypeIssues]
        self, **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        return cast(
            "InvoiceRenderingTemplate",
            self._request(
                "post",
                "/v1/invoice_rendering_templates/{template}/archive".format(
                    template=sanitize_id(self.get("id"))
                ),
                params=params,
            ),
        )

    @classmethod
    async def _cls_archive_async(
        cls,
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        return cast(
            "InvoiceRenderingTemplate",
            await cls._static_request_async(
                "post",
                "/v1/invoice_rendering_templates/{template}/archive".format(
                    template=sanitize_id(template)
                ),
                params=params,
            ),
        )

    @overload
    @staticmethod
    async def archive_async(
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        ...

    @overload
    async def archive_async(
        self, **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        ...

    @class_method_variant("_cls_archive_async")
    async def archive_async(  # pyright: ignore[reportGeneralTypeIssues]
        self, **params: Unpack["InvoiceRenderingTemplate.ArchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Updates the status of an invoice rendering template to ‘archived' so no new Stripe objects (customers, invoices, etc.) can reference it. The template can also no longer be updated. However, if the template is already set on a Stripe object, it will continue to be applied on invoices generated by it.
        """
        return cast(
            "InvoiceRenderingTemplate",
            await self._request_async(
                "post",
                "/v1/invoice_rendering_templates/{template}/archive".format(
                    template=sanitize_id(self.get("id"))
                ),
                params=params,
            ),
        )

    @classmethod
    def list(
        cls, **params: Unpack["InvoiceRenderingTemplate.ListParams"]
    ) -> ListObject["InvoiceRenderingTemplate"]:
        """
        List all templates, ordered by creation date, with the most recently created template appearing first.
        """
        result = cls._static_request(
            "get",
            cls.class_url(),
            params=params,
        )
        if not isinstance(result, ListObject):
            raise TypeError(
                "Expected list object from API, got %s"
                % (type(result).__name__)
            )

        return result

    @classmethod
    async def list_async(
        cls, **params: Unpack["InvoiceRenderingTemplate.ListParams"]
    ) -> ListObject["InvoiceRenderingTemplate"]:
        """
        List all templates, ordered by creation date, with the most recently created template appearing first.
        """
        result = await cls._static_request_async(
            "get",
            cls.class_url(),
            params=params,
        )
        if not isinstance(result, ListObject):
            raise TypeError(
                "Expected list object from API, got %s"
                % (type(result).__name__)
            )

        return result

    @classmethod
    def retrieve(
        cls,
        id: str,
        **params: Unpack["InvoiceRenderingTemplate.RetrieveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Retrieves an invoice rendering template with the given ID. It by default returns the latest version of the template. Optionally, specify a version to see previous versions.
        """
        instance = cls(id, **params)
        instance.refresh()
        return instance

    @classmethod
    async def retrieve_async(
        cls,
        id: str,
        **params: Unpack["InvoiceRenderingTemplate.RetrieveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Retrieves an invoice rendering template with the given ID. It by default returns the latest version of the template. Optionally, specify a version to see previous versions.
        """
        instance = cls(id, **params)
        await instance.refresh_async()
        return instance

    @classmethod
    def _cls_unarchive(
        cls,
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        return cast(
            "InvoiceRenderingTemplate",
            cls._static_request(
                "post",
                "/v1/invoice_rendering_templates/{template}/unarchive".format(
                    template=sanitize_id(template)
                ),
                params=params,
            ),
        )

    @overload
    @staticmethod
    def unarchive(
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        ...

    @overload
    def unarchive(
        self, **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        ...

    @class_method_variant("_cls_unarchive")
    def unarchive(  # pyright: ignore[reportGeneralTypeIssues]
        self, **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        return cast(
            "InvoiceRenderingTemplate",
            self._request(
                "post",
                "/v1/invoice_rendering_templates/{template}/unarchive".format(
                    template=sanitize_id(self.get("id"))
                ),
                params=params,
            ),
        )

    @classmethod
    async def _cls_unarchive_async(
        cls,
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        return cast(
            "InvoiceRenderingTemplate",
            await cls._static_request_async(
                "post",
                "/v1/invoice_rendering_templates/{template}/unarchive".format(
                    template=sanitize_id(template)
                ),
                params=params,
            ),
        )

    @overload
    @staticmethod
    async def unarchive_async(
        template: str,
        **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"],
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        ...

    @overload
    async def unarchive_async(
        self, **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        ...

    @class_method_variant("_cls_unarchive_async")
    async def unarchive_async(  # pyright: ignore[reportGeneralTypeIssues]
        self, **params: Unpack["InvoiceRenderingTemplate.UnarchiveParams"]
    ) -> "InvoiceRenderingTemplate":
        """
        Unarchive an invoice rendering template so it can be used on new Stripe objects again.
        """
        return cast(
            "InvoiceRenderingTemplate",
            await self._request_async(
                "post",
                "/v1/invoice_rendering_templates/{template}/unarchive".format(
                    template=sanitize_id(self.get("id"))
                ),
                params=params,
            ),
        )
