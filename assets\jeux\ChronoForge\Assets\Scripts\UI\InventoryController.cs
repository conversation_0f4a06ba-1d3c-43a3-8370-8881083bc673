using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using ChronoForge.Loot;
using ChronoForge.Player;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur d'inventaire pour ChronoForge
    /// </summary>
    public class InventoryController : MonoBehaviour
    {
        [Header("UI Elements")]
        public Transform inventoryGrid;
        public GameObject itemSlotPrefab;
        public ScrollRect scrollRect;
        
        [Header("Item Details")]
        public GameObject itemDetailsPanel;
        public Text itemNameText;
        public Text itemDescriptionText;
        public Image itemIcon;
        public Button useButton;
        public Button dropButton;
        public Button sellButton;
        
        [Header("Inventory Settings")]
        public int inventorySize = 30;
        public bool autoSort = false;
        
        // Inventory data
        private List<ItemInstance> inventory = new List<ItemInstance>();
        private List<InventorySlot> inventorySlots = new List<InventorySlot>();
        private ItemInstance selectedItem = null;
        
        // Components
        private PlayerController player;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeInventory();
        }
        
        private void Start()
        {
            player = FindFirstObjectByType<PlayerController>();
            SetupUI();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeInventory()
        {
            // Initialize inventory list
            for (int i = 0; i < inventorySize; i++)
            {
                inventory.Add(null);
            }
            
            UnityEngine.Debug.Log("🎒 InventoryController initialized");
        }
        
        private void SetupUI()
        {
            CreateInventorySlots();
            SetupButtons();
            HideItemDetails();
        }
        
        private void CreateInventorySlots()
        {
            if (inventoryGrid == null || itemSlotPrefab == null) return;
            
            for (int i = 0; i < inventorySize; i++)
            {
                GameObject slotObj = Instantiate(itemSlotPrefab, inventoryGrid);
                InventorySlot slot = slotObj.GetComponent<InventorySlot>();
                
                if (slot == null)
                    slot = slotObj.AddComponent<InventorySlot>();
                
                slot.Initialize(i, this);
                inventorySlots.Add(slot);
            }
        }
        
        private void SetupButtons()
        {
            if (useButton != null)
                useButton.onClick.AddListener(UseSelectedItem);
            
            if (dropButton != null)
                dropButton.onClick.AddListener(DropSelectedItem);
            
            if (sellButton != null)
                sellButton.onClick.AddListener(SellSelectedItem);
        }
        
        #endregion
        
        #region Item Management
        
        public bool AddItem(ItemData itemData, int quantity = 1)
        {
            if (itemData == null) return false;
            
            // Try to stack with existing items first
            for (int i = 0; i < inventory.Count; i++)
            {
                if (inventory[i] != null && inventory[i].itemData == itemData)
                {
                    int canAdd = Mathf.Min(quantity, itemData.maxStackSize - inventory[i].quantity);
                    if (canAdd > 0)
                    {
                        inventory[i].quantity += canAdd;
                        quantity -= canAdd;
                        UpdateSlot(i);
                        
                        if (quantity <= 0)
                            return true;
                    }
                }
            }
            
            // Add to empty slots
            while (quantity > 0)
            {
                int emptySlot = FindEmptySlot();
                if (emptySlot == -1)
                    return false; // Inventory full
                
                int addAmount = Mathf.Min(quantity, itemData.maxStackSize);
                inventory[emptySlot] = new ItemInstance(itemData, addAmount);
                quantity -= addAmount;
                UpdateSlot(emptySlot);
            }
            
            if (autoSort)
                SortInventory();
            
            return true;
        }
        
        public bool RemoveItem(ItemData itemData, int quantity = 1)
        {
            if (itemData == null) return false;
            
            int remaining = quantity;
            
            for (int i = 0; i < inventory.Count && remaining > 0; i++)
            {
                if (inventory[i] != null && inventory[i].itemData == itemData)
                {
                    int removeAmount = Mathf.Min(remaining, inventory[i].quantity);
                    inventory[i].quantity -= removeAmount;
                    remaining -= removeAmount;
                    
                    if (inventory[i].quantity <= 0)
                    {
                        inventory[i] = null;
                    }
                    
                    UpdateSlot(i);
                }
            }
            
            return remaining == 0;
        }
        
        public int GetItemCount(ItemData itemData)
        {
            if (itemData == null) return 0;
            
            int count = 0;
            foreach (var item in inventory)
            {
                if (item != null && item.itemData == itemData)
                {
                    count += item.quantity;
                }
            }
            
            return count;
        }
        
        public bool HasItem(ItemData itemData, int quantity = 1)
        {
            return GetItemCount(itemData) >= quantity;
        }
        
        #endregion
        
        #region Slot Management
        
        private int FindEmptySlot()
        {
            for (int i = 0; i < inventory.Count; i++)
            {
                if (inventory[i] == null)
                    return i;
            }
            return -1;
        }
        
        private void UpdateSlot(int index)
        {
            if (index >= 0 && index < inventorySlots.Count)
            {
                inventorySlots[index].UpdateDisplay(inventory[index]);
            }
        }
        
        private void UpdateAllSlots()
        {
            for (int i = 0; i < inventorySlots.Count; i++)
            {
                UpdateSlot(i);
            }
        }
        
        #endregion
        
        #region Item Selection
        
        public void SelectItem(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < inventory.Count)
            {
                selectedItem = inventory[slotIndex];
                ShowItemDetails(selectedItem);
            }
        }
        
        private void ShowItemDetails(ItemInstance item)
        {
            if (itemDetailsPanel == null) return;
            
            if (item == null)
            {
                HideItemDetails();
                return;
            }
            
            itemDetailsPanel.SetActive(true);
            
            if (itemNameText != null)
                itemNameText.text = item.itemData.itemName;
            
            if (itemDescriptionText != null)
                itemDescriptionText.text = item.itemData.GetFullDescription();
            
            if (itemIcon != null)
                itemIcon.sprite = item.itemData.icon;
            
            // Update button states
            if (useButton != null)
                useButton.interactable = item.itemData.isConsumable;
            
            if (dropButton != null)
                dropButton.interactable = item.itemData.isDroppable;
            
            if (sellButton != null)
                sellButton.interactable = item.itemData.canBeSold;
        }
        
        private void HideItemDetails()
        {
            if (itemDetailsPanel != null)
                itemDetailsPanel.SetActive(false);
            
            selectedItem = null;
        }
        
        #endregion
        
        #region Item Actions
        
        private void UseSelectedItem()
        {
            if (selectedItem == null || !selectedItem.itemData.isConsumable) return;
            
            // Use the item
            UseItem(selectedItem);
            
            // Remove one from inventory
            RemoveItem(selectedItem.itemData, 1);
            
            // Update selection
            if (GetItemCount(selectedItem.itemData) == 0)
            {
                HideItemDetails();
            }
        }
        
        private void DropSelectedItem()
        {
            if (selectedItem == null || !selectedItem.itemData.isDroppable) return;
            
            // Drop the item in the world
            DropItem(selectedItem);
            
            // Remove from inventory
            RemoveItem(selectedItem.itemData, 1);
            
            // Update selection
            if (GetItemCount(selectedItem.itemData) == 0)
            {
                HideItemDetails();
            }
        }
        
        private void SellSelectedItem()
        {
            if (selectedItem == null || !selectedItem.itemData.canBeSold) return;
            
            // Sell the item
            SellItem(selectedItem);
            
            // Remove from inventory
            RemoveItem(selectedItem.itemData, 1);
            
            // Update selection
            if (GetItemCount(selectedItem.itemData) == 0)
            {
                HideItemDetails();
            }
        }
        
        private void UseItem(ItemInstance item)
        {
            // Apply item effects to player
            if (player != null)
            {
                PlayerStats stats = player.GetComponent<PlayerStats>();
                if (stats != null)
                {
                    // Apply stat bonuses temporarily or permanently
                    UnityEngine.Debug.Log($"Used {item.itemData.itemName}");
                }
            }
        }
        
        private void DropItem(ItemInstance item)
        {
            // Create item in world
            if (item.itemData.worldPrefab != null && player != null)
            {
                Vector3 dropPosition = player.transform.position + Vector3.right * 1f;
                Instantiate(item.itemData.worldPrefab, dropPosition, Quaternion.identity);
            }
            
            UnityEngine.Debug.Log($"Dropped {item.itemData.itemName}");
        }
        
        private void SellItem(ItemInstance item)
        {
            // Add currency to player
            int sellValue = item.itemData.GetSellValue();
            
            // This would add currency to the player's resources
            UnityEngine.Debug.Log($"Sold {item.itemData.itemName} for {sellValue} credits");
        }
        
        #endregion
        
        #region Utility
        
        private void SortInventory()
        {
            // Simple sort by item type and rarity
            var nonNullItems = new List<ItemInstance>();
            
            foreach (var item in inventory)
            {
                if (item != null)
                    nonNullItems.Add(item);
            }
            
            nonNullItems.Sort((a, b) =>
            {
                int typeCompare = a.itemData.itemType.CompareTo(b.itemData.itemType);
                if (typeCompare != 0) return typeCompare;
                
                return b.itemData.rarity.CompareTo(a.itemData.rarity);
            });
            
            // Clear inventory
            for (int i = 0; i < inventory.Count; i++)
            {
                inventory[i] = null;
            }
            
            // Add sorted items back
            for (int i = 0; i < nonNullItems.Count && i < inventory.Count; i++)
            {
                inventory[i] = nonNullItems[i];
            }
            
            UpdateAllSlots();
        }
        
        #endregion
        
        #region Public Methods
        
        public void ToggleInventory()
        {
            gameObject.SetActive(!gameObject.activeSelf);
        }
        
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        public List<ItemInstance> GetInventory()
        {
            return new List<ItemInstance>(inventory);
        }
        
        public bool IsInventoryFull()
        {
            return FindEmptySlot() == -1;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Slot d'inventaire individuel
    /// </summary>
    public class InventorySlot : MonoBehaviour
    {
        [Header("UI Components")]
        public Image itemIcon;
        public Text quantityText;
        public Button slotButton;
        
        private int slotIndex;
        private InventoryController inventoryController;
        private ItemInstance currentItem;
        
        public void Initialize(int index, InventoryController controller)
        {
            slotIndex = index;
            inventoryController = controller;
            
            if (slotButton == null)
                slotButton = GetComponent<Button>();
            
            if (slotButton != null)
                slotButton.onClick.AddListener(OnSlotClicked);
        }
        
        public void UpdateDisplay(ItemInstance item)
        {
            currentItem = item;
            
            if (item == null)
            {
                // Empty slot
                if (itemIcon != null)
                {
                    itemIcon.sprite = null;
                    itemIcon.color = Color.clear;
                }
                
                if (quantityText != null)
                    quantityText.text = "";
            }
            else
            {
                // Item in slot
                if (itemIcon != null)
                {
                    itemIcon.sprite = item.itemData.icon;
                    itemIcon.color = Color.white;
                }
                
                if (quantityText != null)
                {
                    quantityText.text = item.quantity > 1 ? item.quantity.ToString() : "";
                }
            }
        }
        
        private void OnSlotClicked()
        {
            if (inventoryController != null)
            {
                inventoryController.SelectItem(slotIndex);
            }
        }
    }
}
