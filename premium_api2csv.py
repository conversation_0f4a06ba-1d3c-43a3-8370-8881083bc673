import os
import csv
import io
import json
from flask import Flask, request, jsonify, send_file
from functools import wraps
from datetime import datetime, timedelta

app = Flask(__name__)

# Exemple de clés (à stocker dans une base ou fichier sécurisé en prod)
FREE_KEYS = {"free-demo-key"}
PREMIUM_KEYS = {"premium-demo-key"}

# Limite gratuite : 5 conversions/jour/clé
FREE_LIMIT = 5
USAGE_TRACKER = {}

# Décorateur d'authentification
def require_api_key(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        key = request.headers.get('X-API-KEY')
        if not key:
            return jsonify({"error": "Clé API requise"}), 401
        if key in PREMIUM_KEYS:
            request.is_premium = True
        elif key in FREE_KEYS:
            request.is_premium = False
        else:
            return jsonify({"error": "Clé API invalide"}), 403
        return f(*args, **kwargs)
    return decorated

# Limiteur d'usage pour les clés gratuites
@app.before_request
def check_free_limit():
    key = request.headers.get('X-API-KEY')
    if key in FREE_KEYS:
        today = datetime.utcnow().date()
        usage = USAGE_TRACKER.get((key, today), 0)
        if usage >= FREE_LIMIT:
            return jsonify({"error": "Limite gratuite atteinte. Passez Premium pour plus de conversions."}), 429
        USAGE_TRACKER[(key, today)] = usage + 1

@app.route('/convert', methods=['POST'])
@require_api_key
def convert():
    data = request.get_json()
    if not data or 'json' not in data:
        return jsonify({"error": "Données JSON manquantes"}), 400
    try:
        json_data = data['json']
        if isinstance(json_data, str):
            json_data = json.loads(json_data)
        # Premium : supporte l'export Excel/XML, gros fichiers
        if request.is_premium and data.get('format') == 'excel':
            # Placeholder : à remplacer par export Excel réel
            return jsonify({"message": "Export Excel disponible en version finale."})
        # Conversion JSON -> CSV
        output = io.StringIO()
        if isinstance(json_data, list):
            writer = csv.DictWriter(output, fieldnames=json_data[0].keys())
            writer.writeheader()
            writer.writerows(json_data)
        else:
            writer = csv.DictWriter(output, fieldnames=json_data.keys())
            writer.writeheader()
            writer.writerow(json_data)
        output.seek(0)
        return send_file(io.BytesIO(output.getvalue().encode()), mimetype='text/csv', as_attachment=True, download_name='result.csv')
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/status')
def status():
    return jsonify({"status": "ok", "premium_keys": len(PREMIUM_KEYS)})

if __name__ == '__main__':
    app.run(debug=True, port=5001)
