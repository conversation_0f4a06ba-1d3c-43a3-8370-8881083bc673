using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Collections;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur de notifications pour ChronoForge
    /// </summary>
    public class NotificationController : MonoBehaviour
    {
        [Header("Notification Settings")]
        public GameObject notificationPrefab;
        public Transform notificationParent;
        public int maxNotifications = 5;
        public float defaultDuration = 3f;
        
        [Header("Achievement Settings")]
        public GameObject achievementPrefab;
        public Transform achievementParent;
        public float achievementDuration = 5f;
        
        [Header("Animation Settings")]
        public float slideInDuration = 0.3f;
        public float slideOutDuration = 0.2f;
        public AnimationCurve slideInCurve = AnimationCurve.EaseOut(0, 0, 1, 1);
        public AnimationCurve slideOutCurve = AnimationCurve.EaseIn(0, 0, 1, 1);
        
        [Header("Positioning")]
        public Vector2 notificationOffset = new Vector2(0, -60);
        public Vector2 achievementPosition = new Vector2(0, 100);
        
        // Notification queue
        private Queue<NotificationData> notificationQueue = new Queue<NotificationData>();
        private List<GameObject> activeNotifications = new List<GameObject>();
        private List<GameObject> activeAchievements = new List<GameObject>();
        
        // State
        private bool isProcessingQueue = false;
        
        #region Unity Lifecycle
        
        private void Update()
        {
            ProcessNotificationQueue();
        }
        
        #endregion
        
        #region Initialization
        
        public void Initialize()
        {
            // Create default notification prefab if none assigned
            if (notificationPrefab == null)
            {
                CreateDefaultNotificationPrefab();
            }
            
            // Create default achievement prefab if none assigned
            if (achievementPrefab == null)
            {
                CreateDefaultAchievementPrefab();
            }
            
            // Setup parent containers
            SetupParentContainers();
            
            Debug.Log("📢 NotificationController initialized");
        }
        
        private void CreateDefaultNotificationPrefab()
        {
            // Create a simple notification prefab
            GameObject prefab = new GameObject("NotificationPrefab");
            
            // Add Image background
            Image background = prefab.AddComponent<Image>();
            background.color = new Color(0, 0, 0, 0.8f);
            
            // Add RectTransform
            RectTransform rectTransform = prefab.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(300, 60);
            
            // Add text
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(prefab.transform);
            
            TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
            text.text = "Notification";
            text.fontSize = 16;
            text.color = Color.white;
            text.alignment = TextAlignmentOptions.Center;
            
            RectTransform textRect = textObj.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            // Add CanvasGroup for fading
            prefab.AddComponent<CanvasGroup>();
            
            // Add NotificationUI component
            prefab.AddComponent<NotificationUI>();
            
            notificationPrefab = prefab;
        }
        
        private void CreateDefaultAchievementPrefab()
        {
            // Create a more elaborate achievement prefab
            GameObject prefab = new GameObject("AchievementPrefab");
            
            // Add Image background
            Image background = prefab.AddComponent<Image>();
            background.color = new Color(1, 0.8f, 0, 0.9f); // Golden color
            
            // Add RectTransform
            RectTransform rectTransform = prefab.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(400, 80);
            
            // Add title text
            GameObject titleObj = new GameObject("Title");
            titleObj.transform.SetParent(prefab.transform);
            
            TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = "Achievement Unlocked!";
            titleText.fontSize = 18;
            titleText.fontStyle = FontStyles.Bold;
            titleText.color = Color.black;
            titleText.alignment = TextAlignmentOptions.Center;
            
            RectTransform titleRect = titleObj.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.5f);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;
            
            // Add description text
            GameObject descObj = new GameObject("Description");
            descObj.transform.SetParent(prefab.transform);
            
            TextMeshProUGUI descText = descObj.AddComponent<TextMeshProUGUI>();
            descText.text = "Achievement Description";
            descText.fontSize = 14;
            descText.color = Color.black;
            descText.alignment = TextAlignmentOptions.Center;
            
            RectTransform descRect = descObj.GetComponent<RectTransform>();
            descRect.anchorMin = new Vector2(0, 0);
            descRect.anchorMax = new Vector2(1, 0.5f);
            descRect.offsetMin = Vector2.zero;
            descRect.offsetMax = Vector2.zero;
            
            // Add CanvasGroup for fading
            prefab.AddComponent<CanvasGroup>();
            
            // Add AchievementUI component
            prefab.AddComponent<AchievementUI>();
            
            achievementPrefab = prefab;
        }
        
        private void SetupParentContainers()
        {
            // Setup notification parent
            if (notificationParent == null)
            {
                GameObject notifParent = new GameObject("NotificationContainer");
                notifParent.transform.SetParent(transform);
                
                RectTransform rect = notifParent.AddComponent<RectTransform>();
                rect.anchorMin = new Vector2(1, 1);
                rect.anchorMax = new Vector2(1, 1);
                rect.pivot = new Vector2(1, 1);
                rect.anchoredPosition = new Vector2(-20, -20);
                
                notificationParent = notifParent.transform;
            }
            
            // Setup achievement parent
            if (achievementParent == null)
            {
                GameObject achParent = new GameObject("AchievementContainer");
                achParent.transform.SetParent(transform);
                
                RectTransform rect = achParent.AddComponent<RectTransform>();
                rect.anchorMin = new Vector2(0.5f, 0.8f);
                rect.anchorMax = new Vector2(0.5f, 0.8f);
                rect.pivot = new Vector2(0.5f, 0.5f);
                rect.anchoredPosition = Vector2.zero;
                
                achievementParent = achParent.transform;
            }
        }
        
        #endregion
        
        #region Notification Management
        
        public void ShowNotification(string message, NotificationType type = NotificationType.Info, float duration = -1f)
        {
            if (duration < 0)
                duration = defaultDuration;
            
            NotificationData notification = new NotificationData
            {
                message = message,
                type = type,
                duration = duration,
                timestamp = Time.time
            };
            
            notificationQueue.Enqueue(notification);
        }
        
        private void ProcessNotificationQueue()
        {
            if (isProcessingQueue || notificationQueue.Count == 0) return;
            
            // Remove expired notifications
            RemoveExpiredNotifications();
            
            // Check if we can show more notifications
            if (activeNotifications.Count >= maxNotifications) return;
            
            // Process next notification
            if (notificationQueue.Count > 0)
            {
                NotificationData notification = notificationQueue.Dequeue();
                StartCoroutine(ShowNotificationCoroutine(notification));
            }
        }
        
        private IEnumerator ShowNotificationCoroutine(NotificationData notification)
        {
            isProcessingQueue = true;
            
            // Create notification object
            GameObject notificationObj = Instantiate(notificationPrefab, notificationParent);
            activeNotifications.Add(notificationObj);
            
            // Setup notification
            SetupNotification(notificationObj, notification);
            
            // Position notification
            PositionNotification(notificationObj);
            
            // Animate in
            yield return StartCoroutine(AnimateNotificationIn(notificationObj));
            
            // Wait for duration
            yield return new WaitForSeconds(notification.duration);
            
            // Animate out
            yield return StartCoroutine(AnimateNotificationOut(notificationObj));
            
            // Remove from active list
            activeNotifications.Remove(notificationObj);
            Destroy(notificationObj);
            
            // Reposition remaining notifications
            RepositionNotifications();
            
            isProcessingQueue = false;
        }
        
        private void SetupNotification(GameObject notificationObj, NotificationData notification)
        {
            NotificationUI notificationUI = notificationObj.GetComponent<NotificationUI>();
            if (notificationUI != null)
            {
                notificationUI.Setup(notification);
            }
            else
            {
                // Fallback setup
                TextMeshProUGUI text = notificationObj.GetComponentInChildren<TextMeshProUGUI>();
                if (text != null)
                {
                    text.text = notification.message;
                }
                
                Image background = notificationObj.GetComponent<Image>();
                if (background != null)
                {
                    background.color = GetNotificationColor(notification.type);
                }
            }
        }
        
        private Color GetNotificationColor(NotificationType type)
        {
            switch (type)
            {
                case NotificationType.Success:
                    return new Color(0, 0.8f, 0, 0.8f); // Green
                case NotificationType.Warning:
                    return new Color(1, 0.8f, 0, 0.8f); // Orange
                case NotificationType.Error:
                    return new Color(0.8f, 0, 0, 0.8f); // Red
                case NotificationType.Achievement:
                    return new Color(1, 0.8f, 0, 0.8f); // Gold
                case NotificationType.Quest:
                    return new Color(0, 0.6f, 1, 0.8f); // Blue
                case NotificationType.Combat:
                    return new Color(0.8f, 0.2f, 0.2f, 0.8f); // Dark Red
                default:
                    return new Color(0.2f, 0.2f, 0.2f, 0.8f); // Gray
            }
        }
        
        #endregion
        
        #region Achievement Management
        
        public void ShowAchievement(string title, string description, Sprite icon = null)
        {
            StartCoroutine(ShowAchievementCoroutine(title, description, icon));
        }
        
        private IEnumerator ShowAchievementCoroutine(string title, string description, Sprite icon)
        {
            // Create achievement object
            GameObject achievementObj = Instantiate(achievementPrefab, achievementParent);
            activeAchievements.Add(achievementObj);
            
            // Setup achievement
            SetupAchievement(achievementObj, title, description, icon);
            
            // Position achievement
            RectTransform rect = achievementObj.GetComponent<RectTransform>();
            rect.anchoredPosition = achievementPosition;
            
            // Animate in
            yield return StartCoroutine(AnimateAchievementIn(achievementObj));
            
            // Wait for duration
            yield return new WaitForSeconds(achievementDuration);
            
            // Animate out
            yield return StartCoroutine(AnimateAchievementOut(achievementObj));
            
            // Remove from active list
            activeAchievements.Remove(achievementObj);
            Destroy(achievementObj);
        }
        
        private void SetupAchievement(GameObject achievementObj, string title, string description, Sprite icon)
        {
            AchievementUI achievementUI = achievementObj.GetComponent<AchievementUI>();
            if (achievementUI != null)
            {
                achievementUI.Setup(title, description, icon);
            }
            else
            {
                // Fallback setup
                TextMeshProUGUI[] texts = achievementObj.GetComponentsInChildren<TextMeshProUGUI>();
                if (texts.Length >= 2)
                {
                    texts[0].text = title;
                    texts[1].text = description;
                }
            }
        }
        
        #endregion
        
        #region Animation
        
        private IEnumerator AnimateNotificationIn(GameObject notification)
        {
            CanvasGroup canvasGroup = notification.GetComponent<CanvasGroup>();
            RectTransform rect = notification.GetComponent<RectTransform>();
            
            Vector2 startPos = rect.anchoredPosition + Vector2.right * 400f;
            Vector2 endPos = rect.anchoredPosition;
            
            float elapsed = 0f;
            canvasGroup.alpha = 0f;
            rect.anchoredPosition = startPos;
            
            while (elapsed < slideInDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / slideInDuration;
                float curveValue = slideInCurve.Evaluate(progress);
                
                canvasGroup.alpha = curveValue;
                rect.anchoredPosition = Vector2.Lerp(startPos, endPos, curveValue);
                
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
            rect.anchoredPosition = endPos;
        }
        
        private IEnumerator AnimateNotificationOut(GameObject notification)
        {
            CanvasGroup canvasGroup = notification.GetComponent<CanvasGroup>();
            RectTransform rect = notification.GetComponent<RectTransform>();
            
            Vector2 startPos = rect.anchoredPosition;
            Vector2 endPos = startPos + Vector2.right * 400f;
            
            float elapsed = 0f;
            
            while (elapsed < slideOutDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / slideOutDuration;
                float curveValue = slideOutCurve.Evaluate(progress);
                
                canvasGroup.alpha = 1f - curveValue;
                rect.anchoredPosition = Vector2.Lerp(startPos, endPos, curveValue);
                
                yield return null;
            }
            
            canvasGroup.alpha = 0f;
            rect.anchoredPosition = endPos;
        }
        
        private IEnumerator AnimateAchievementIn(GameObject achievement)
        {
            CanvasGroup canvasGroup = achievement.GetComponent<CanvasGroup>();
            RectTransform rect = achievement.GetComponent<RectTransform>();
            
            Vector3 startScale = Vector3.zero;
            Vector3 endScale = Vector3.one;
            
            float elapsed = 0f;
            canvasGroup.alpha = 0f;
            rect.localScale = startScale;
            
            while (elapsed < slideInDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / slideInDuration;
                float curveValue = slideInCurve.Evaluate(progress);
                
                canvasGroup.alpha = curveValue;
                rect.localScale = Vector3.Lerp(startScale, endScale, curveValue);
                
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
            rect.localScale = endScale;
        }
        
        private IEnumerator AnimateAchievementOut(GameObject achievement)
        {
            CanvasGroup canvasGroup = achievement.GetComponent<CanvasGroup>();
            RectTransform rect = achievement.GetComponent<RectTransform>();
            
            Vector3 startScale = rect.localScale;
            Vector3 endScale = Vector3.zero;
            
            float elapsed = 0f;
            
            while (elapsed < slideOutDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / slideOutDuration;
                float curveValue = slideOutCurve.Evaluate(progress);
                
                canvasGroup.alpha = 1f - curveValue;
                rect.localScale = Vector3.Lerp(startScale, endScale, curveValue);
                
                yield return null;
            }
            
            canvasGroup.alpha = 0f;
            rect.localScale = endScale;
        }
        
        #endregion
        
        #region Positioning
        
        private void PositionNotification(GameObject notification)
        {
            RectTransform rect = notification.GetComponent<RectTransform>();
            
            // Position based on number of active notifications
            int index = activeNotifications.IndexOf(notification);
            Vector2 position = notificationOffset * index;
            
            rect.anchoredPosition = position;
        }
        
        private void RepositionNotifications()
        {
            for (int i = 0; i < activeNotifications.Count; i++)
            {
                if (activeNotifications[i] != null)
                {
                    RectTransform rect = activeNotifications[i].GetComponent<RectTransform>();
                    Vector2 targetPosition = notificationOffset * i;
                    
                    StartCoroutine(SmoothMoveToPosition(rect, targetPosition, 0.2f));
                }
            }
        }
        
        private IEnumerator SmoothMoveToPosition(RectTransform rect, Vector2 targetPosition, float duration)
        {
            Vector2 startPosition = rect.anchoredPosition;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                
                rect.anchoredPosition = Vector2.Lerp(startPosition, targetPosition, progress);
                
                yield return null;
            }
            
            rect.anchoredPosition = targetPosition;
        }
        
        #endregion
        
        #region Utility
        
        private void RemoveExpiredNotifications()
        {
            // This is handled by individual coroutines
            // Could add manual expiration logic here if needed
        }
        
        public void UpdateNotifications()
        {
            // Called from UIManager update
            // Could add additional update logic here
        }
        
        public void ClearAllNotifications()
        {
            // Clear queue
            notificationQueue.Clear();
            
            // Remove active notifications
            foreach (GameObject notification in activeNotifications)
            {
                if (notification != null)
                    Destroy(notification);
            }
            activeNotifications.Clear();
            
            // Remove active achievements
            foreach (GameObject achievement in activeAchievements)
            {
                if (achievement != null)
                    Destroy(achievement);
            }
            activeAchievements.Clear();
        }
        
        #endregion
    }
    
    /// <summary>
    /// Données d'une notification
    /// </summary>
    [System.Serializable]
    public class NotificationData
    {
        public string message;
        public NotificationType type;
        public float duration;
        public float timestamp;
        public Sprite icon;
    }
    
    /// <summary>
    /// Composant UI pour les notifications
    /// </summary>
    public class NotificationUI : MonoBehaviour
    {
        public TextMeshProUGUI messageText;
        public Image backgroundImage;
        public Image iconImage;
        
        public void Setup(NotificationData data)
        {
            if (messageText != null)
                messageText.text = data.message;
            
            if (iconImage != null && data.icon != null)
            {
                iconImage.sprite = data.icon;
                iconImage.gameObject.SetActive(true);
            }
            else if (iconImage != null)
            {
                iconImage.gameObject.SetActive(false);
            }
        }
    }
    
    /// <summary>
    /// Composant UI pour les achievements
    /// </summary>
    public class AchievementUI : MonoBehaviour
    {
        public TextMeshProUGUI titleText;
        public TextMeshProUGUI descriptionText;
        public Image iconImage;
        
        public void Setup(string title, string description, Sprite icon)
        {
            if (titleText != null)
                titleText.text = title;
            
            if (descriptionText != null)
                descriptionText.text = description;
            
            if (iconImage != null && icon != null)
            {
                iconImage.sprite = icon;
                iconImage.gameObject.SetActive(true);
            }
            else if (iconImage != null)
            {
                iconImage.gameObject.SetActive(false);
            }
        }
    }
}
