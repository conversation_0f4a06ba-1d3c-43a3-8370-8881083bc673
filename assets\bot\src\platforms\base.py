"""
Classe de base pour tous les connecteurs de plateformes.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class PlatformConnector(ABC):
    """Classe de base pour tous les connecteurs de plateformes."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__.replace('Connector', '').lower()
        self.authenticated = False
        
    @abstractmethod
    def authenticate(self) -> bool:
        """Authentifie l'utilisateur sur la plateforme."""
        pass
    
    @abstractmethod
    def publish_post(self, title: str, body: str, **kwargs) -> Dict[str, Any]:
        """Publie un post sur la plateforme."""
        pass
    
    @abstractmethod
    def validate_post(self, title: str, body: str, **kwargs) -> bool:
        """Valide qu'un post respecte les contraintes de la plateforme."""
        pass
    
    def get_rate_limit_delay(self) -> int:
        """Retourne le délai entre publications en secondes."""
        return self.config.get('rate_limit_delay', 60) * 60  # Convertir minutes en secondes
    
    def truncate_text(self, text: str, max_length: int) -> str:
        """Tronque le texte si nécessaire."""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."
    
    def format_hashtags(self, tags: list) -> str:
        """Formate les hashtags pour la plateforme."""
        if not tags:
            return ""
        return " " + " ".join([f"#{tag}" for tag in tags])
    
    def log_success(self, post_id: str, platform_response: Dict[str, Any]):
        """Log une publication réussie."""
        logger.info(f"✅ Post {post_id} publié avec succès sur {self.name}")
        
    def log_error(self, post_id: str, error: Exception):
        """Log une erreur de publication."""
        logger.error(f"❌ Erreur lors de la publication du post {post_id} sur {self.name}: {error}")

class PublicationResult:
    """Résultat d'une publication."""
    
    def __init__(self, success: bool, platform: str, post_id: str = None, 
                 error: str = None, response_data: Dict = None):
        self.success = success
        self.platform = platform
        self.post_id = post_id
        self.error = error
        self.response_data = response_data or {}
        
    def __str__(self):
        if self.success:
            return f"✅ {self.platform}: Publication réussie"
        else:
            return f"❌ {self.platform}: {self.error}"
