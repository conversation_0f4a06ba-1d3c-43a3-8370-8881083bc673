# Configuration du bot de publication automatisé

# Configuration générale
general:
  timezone: "Europe/Paris"
  default_retry_attempts: 3
  default_retry_delay: 60  # secondes
  log_file: "bot.log"
  posts_directory: "posts"
  
# Configuration par plateforme
platforms:
  twitter:
    enabled: true
    max_length: 280
    hashtags: ["#dev", "#opensource", "#webdev"]
    rate_limit_delay: 15  # minutes entre posts
    
  reddit:
    enabled: true
    default_subreddit: "u_Next_Bandicoot287"  # Profil utilisateur
    rate_limit_delay: 60  # minutes entre posts
    subreddits:
      - "u_Next_Bandicoot287"  # Posts sur le profil utilisateur
      
  linkedin:
    enabled: false
    max_length: 3000
    rate_limit_delay: 30  # minutes entre posts
    
  devto:
    enabled: true
    default_tags: ["webdev", "javascript", "opensource"]
    rate_limit_delay: 120  # minutes entre posts

# Planification
scheduler:
  enabled: true
  default_interval_hours: 24
  working_hours:
    start: "09:00"
    end: "18:00"
  working_days: [1, 2, 3, 4, 5]  # <PERSON><PERSON> à Vendredi
  
# Recyclage de contenu
recycling:
  enabled: true
  min_days_before_repost: 30
  max_reposts: 3
  variation_enabled: true
