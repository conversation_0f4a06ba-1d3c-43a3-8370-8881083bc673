# Exemple de déploiement avec GitHub Actions
# Placez ce fichier dans .github/workflows/bot-publication.yml

name: Bot de Publication Automatisé

on:
  schedule:
    # Exécuter toutes les heures pendant les heures de travail (UTC)
    - cron: '0 8-17 * * 1-5'  # 9h-18h Paris time, Lun-Ven
  workflow_dispatch:  # Permettre l'exécution manuelle

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        cd assets/bot
        pip install -r requirements.txt
        
    - name: Create .env file
      run: |
        cd assets/bot
        cat > .env << EOF
        TWITTER_API_KEY=${{ secrets.TWITTER_API_KEY }}
        TWITTER_API_SECRET=${{ secrets.TWITTER_API_SECRET }}
        TWITTER_ACCESS_TOKEN=${{ secrets.TWITTER_ACCESS_TOKEN }}
        TWITTER_ACCESS_TOKEN_SECRET=${{ secrets.TWITTER_ACCESS_TOKEN_SECRET }}
        TWITTER_BEARER_TOKEN=${{ secrets.TWITTER_BEARER_TOKEN }}
        
        REDDIT_CLIENT_ID=${{ secrets.REDDIT_CLIENT_ID }}
        REDDIT_CLIENT_SECRET=${{ secrets.REDDIT_CLIENT_SECRET }}
        REDDIT_USERNAME=${{ secrets.REDDIT_USERNAME }}
        REDDIT_PASSWORD=${{ secrets.REDDIT_PASSWORD }}
        
        LINKEDIN_ACCESS_TOKEN=${{ secrets.LINKEDIN_ACCESS_TOKEN }}
        
        DEVTO_API_KEY=${{ secrets.DEVTO_API_KEY }}
        
        LOG_LEVEL=INFO
        EOF
        
    - name: Check status
      run: |
        cd assets/bot
        python cli.py status
        
    - name: Run scheduled publications
      run: |
        cd assets/bot
        # Exécuter une vérification unique des posts programmés
        python -c "
        import sys
        sys.path.insert(0, 'src')
        from src.config import ConfigManager, setup_logging
        from src.content_manager import ContentManager
        from src.publisher import Publisher
        from src.scheduler import BotScheduler
        from datetime import datetime
        
        # Configuration
        config = ConfigManager()
        setup_logging(config)
        
        # Initialisation
        content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
        publisher = Publisher(config.config)
        scheduler = BotScheduler(config.config, content_manager, publisher)
        
        # Authentification
        auth_results = publisher.authenticate_all()
        print(f'Authentification: {auth_results}')
        
        # Vérification et publication
        scheduler._check_and_publish_posts()
        print('Vérification terminée')
        "
        
    - name: Upload logs
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: bot-logs
        path: assets/bot/*.log
        retention-days: 7

# Configuration des secrets GitHub :
# 1. Allez dans Settings > Secrets and variables > Actions
# 2. Ajoutez tous les secrets listés dans le fichier .env.example
# 3. Activez les workflows dans Actions

# Notes :
# - Ajustez le cron selon votre timezone
# - Testez d'abord avec workflow_dispatch
# - Surveillez les logs dans Actions
