using UnityEngine;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Constantes globales pour SpaceClicker
    /// </summary>
    public static class GameConstants
    {
        #region Game Version
        
        public const string GAME_VERSION = "1.0.0";
        public const string BUILD_DATE = "2024-01-01";
        
        #endregion
        
        #region Click System
        
        public const float BASE_CLICK_POWER = 1f;
        public const float CLICK_ANIMATION_DURATION = 0.1f;
        public const float CRITICAL_CLICK_CHANCE = 0.05f;
        public const float CRITICAL_CLICK_MULTIPLIER = 2f;
        
        #endregion
        
        #region Production System
        
        public const float PRODUCTION_TICK_RATE = 0.1f; // 10 times per second
        public const float BASE_ENERGY_PRODUCTION = 1f;
        public const float BASE_MATTER_PRODUCTION = 0.1f;
        public const float BASE_CREDITS_PRODUCTION = 0.5f;
        
        #endregion
        
        #region Upgrade System
        
        public const float BASE_UPGRADE_COST_MULTIPLIER = 1.15f;
        public const int MAX_UPGRADE_LEVEL = 1000;
        public const float UPGRADE_EFFECT_BASE = 1f;
        
        #endregion
        
        #region UI Constants
        
        public const float UI_ANIMATION_SPEED = 2f;
        public const float NOTIFICATION_DURATION = 3f;
        public const int MAX_NOTIFICATIONS = 5;
        
        #endregion
        
        #region Colors
        
        public static readonly Color ENERGY_COLOR = new Color(0f, 1f, 1f, 1f); // Cyan
        public static readonly Color MATTER_COLOR = new Color(1f, 0.5f, 0f, 1f); // Orange
        public static readonly Color CREDITS_COLOR = new Color(1f, 1f, 0f, 1f); // Yellow
        public static readonly Color RESEARCH_COLOR = new Color(0.5f, 0f, 1f, 1f); // Purple
        public static readonly Color ANTIMATTER_COLOR = new Color(1f, 0f, 0.5f, 1f); // Pink
        
        // Rarity colors
        public static readonly Color COMMON_COLOR = Color.white;
        public static readonly Color UNCOMMON_COLOR = Color.green;
        public static readonly Color RARE_COLOR = Color.blue;
        public static readonly Color EPIC_COLOR = Color.magenta;
        public static readonly Color LEGENDARY_COLOR = Color.yellow;
        public static readonly Color MYTHIC_COLOR = Color.red;
        
        #endregion
        
        #region Audio
        
        public const float DEFAULT_SFX_VOLUME = 0.8f;
        public const float DEFAULT_MUSIC_VOLUME = 0.6f;
        public const float DEFAULT_UI_VOLUME = 1f;
        
        #endregion
        
        #region Save System
        
        public const string SAVE_FILE_NAME = "spaceclicker_save.json";
        public const float AUTO_SAVE_INTERVAL = 30f; // seconds
        public const int MAX_SAVE_SLOTS = 3;
        
        #endregion
        
        #region Performance
        
        public const int MAX_PARTICLES = 100;
        public const int OBJECT_POOL_SIZE = 50;
        public const float GARBAGE_COLLECTION_INTERVAL = 60f; // seconds
        
        #endregion
        
        #region Gameplay Balance
        
        public const float OFFLINE_PRODUCTION_MULTIPLIER = 0.5f;
        public const float MAX_OFFLINE_HOURS = 24f;
        public const float PRESTIGE_MULTIPLIER_BASE = 1.1f;
        
        #endregion
        
        #region Resource Limits
        
        public static readonly System.Numerics.BigInteger MAX_RESOURCE_VALUE = 
            System.Numerics.BigInteger.Parse("999999999999999999999999999999");
        
        public const float RESOURCE_DISPLAY_UPDATE_RATE = 0.1f;
        
        #endregion
        
        #region Achievements
        
        public const int TOTAL_ACHIEVEMENTS = 50;
        public const float ACHIEVEMENT_NOTIFICATION_DURATION = 5f;
        
        #endregion
        
        #region Debug
        
        public const bool ENABLE_DEBUG_LOGS = true;
        public const bool ENABLE_CHEATS = false;
        public const bool SHOW_FPS_COUNTER = false;
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Obtient la couleur d'une ressource
        /// </summary>
        public static Color GetResourceColor(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return ENERGY_COLOR;
                case ResourceType.Matter:
                    return MATTER_COLOR;
                case ResourceType.Credits:
                    return CREDITS_COLOR;
                case ResourceType.Research:
                    return RESEARCH_COLOR;
                case ResourceType.Antimatter:
                    return ANTIMATTER_COLOR;
                default:
                    return Color.white;
            }
        }
        
        /// <summary>
        /// Obtient le nom d'affichage d'une ressource
        /// </summary>
        public static string GetResourceDisplayName(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return "Energy";
                case ResourceType.Matter:
                    return "Matter";
                case ResourceType.Credits:
                    return "Credits";
                case ResourceType.Research:
                    return "Research";
                case ResourceType.Antimatter:
                    return "Antimatter";
                default:
                    return resourceType.ToString();
            }
        }
        
        /// <summary>
        /// Calcule le coût d'amélioration
        /// </summary>
        public static System.Numerics.BigInteger CalculateUpgradeCost(float baseCost, int level, float multiplier = BASE_UPGRADE_COST_MULTIPLIER)
        {
            double cost = baseCost * System.Math.Pow(multiplier, level);
            return new System.Numerics.BigInteger(cost);
        }
        
        /// <summary>
        /// Calcule l'effet d'amélioration
        /// </summary>
        public static float CalculateUpgradeEffect(float baseEffect, int level, bool isMultiplier = false)
        {
            if (isMultiplier)
            {
                return baseEffect * level;
            }
            else
            {
                return baseEffect + (baseEffect * level * 0.1f);
            }
        }
        
        /// <summary>
        /// Vérifie si une valeur est dans les limites
        /// </summary>
        public static bool IsWithinResourceLimits(System.Numerics.BigInteger value)
        {
            return value >= 0 && value <= MAX_RESOURCE_VALUE;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types d'améliorations pour les tests
    /// </summary>
    public enum UpgradeType
    {
        SolarPanelEfficiency,
        MatterConverter,
        ClickMultiplier,
        ProductionBoost,
        AutoClicker,
        QuantumProcessor,
        EnergyStorage,
        MatterStorage
    }
}
