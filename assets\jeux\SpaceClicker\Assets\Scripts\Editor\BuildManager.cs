using UnityEngine;
using UnityEditor;
using UnityEditor.Build.Reporting;
using System.IO;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Gestionnaire de build pour Space Clicker
    /// </summary>
    public class BuildManager : EditorWindow
    {
        private string buildPath = "Builds/";
        private string appName = "SpaceClicker";
        private string version = "1.0.0";
        
        private bool buildWindows = true;
        private bool buildMac = false;
        private bool buildLinux = false;
        private bool buildAndroid = false;
        private bool buildWebGL = true;
        
        private bool developmentBuild = false;
        private bool autoRunBuild = false;
        
        [MenuItem("Space Clicker/Build Manager")]
        public static void ShowWindow()
        {
            GetWindow<BuildManager>("Build Manager");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker Build Manager", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Build settings
            GUILayout.Label("Build Settings:", EditorStyles.boldLabel);
            buildPath = EditorGUILayout.TextField("Build Path:", buildPath);
            appName = EditorGUILayout.TextField("App Name:", appName);
            version = EditorGUILayout.TextField("Version:", version);
            
            GUILayout.Space(10);
            
            // Platform selection
            GUILayout.Label("Target Platforms:", EditorStyles.boldLabel);
            buildWindows = EditorGUILayout.Toggle("Windows (x64)", buildWindows);
            buildMac = EditorGUILayout.Toggle("macOS", buildMac);
            buildLinux = EditorGUILayout.Toggle("Linux", buildLinux);
            buildAndroid = EditorGUILayout.Toggle("Android", buildAndroid);
            buildWebGL = EditorGUILayout.Toggle("WebGL", buildWebGL);
            
            GUILayout.Space(10);
            
            // Build options
            GUILayout.Label("Build Options:", EditorStyles.boldLabel);
            developmentBuild = EditorGUILayout.Toggle("Development Build", developmentBuild);
            autoRunBuild = EditorGUILayout.Toggle("Auto Run After Build", autoRunBuild);
            
            GUILayout.Space(20);
            
            // Build buttons
            if (GUILayout.Button("Build All Selected Platforms", GUILayout.Height(30)))
            {
                BuildAllPlatforms();
            }
            
            GUILayout.Space(10);
            
            // Individual platform builds
            if (buildWindows && GUILayout.Button("Build Windows"))
            {
                BuildForPlatform(BuildTarget.StandaloneWindows64);
            }
            
            if (buildMac && GUILayout.Button("Build macOS"))
            {
                BuildForPlatform(BuildTarget.StandaloneOSX);
            }
            
            if (buildLinux && GUILayout.Button("Build Linux"))
            {
                BuildForPlatform(BuildTarget.StandaloneLinux64);
            }
            
            if (buildAndroid && GUILayout.Button("Build Android"))
            {
                BuildForPlatform(BuildTarget.Android);
            }
            
            if (buildWebGL && GUILayout.Button("Build WebGL"))
            {
                BuildForPlatform(BuildTarget.WebGL);
            }
            
            GUILayout.Space(20);
            
            // Utility buttons
            if (GUILayout.Button("Open Build Folder"))
            {
                OpenBuildFolder();
            }
            
            if (GUILayout.Button("Clean Build Folder"))
            {
                CleanBuildFolder();
            }
            
            GUILayout.Space(10);
            
            // Project setup
            if (GUILayout.Button("Setup Project for Build"))
            {
                SetupProjectForBuild();
            }
        }
        
        private void BuildAllPlatforms()
        {
            Debug.Log("🚀 Starting build process for all selected platforms...");
            
            SetupProjectForBuild();
            
            if (buildWindows) BuildForPlatform(BuildTarget.StandaloneWindows64);
            if (buildMac) BuildForPlatform(BuildTarget.StandaloneOSX);
            if (buildLinux) BuildForPlatform(BuildTarget.StandaloneLinux64);
            if (buildAndroid) BuildForPlatform(BuildTarget.Android);
            if (buildWebGL) BuildForPlatform(BuildTarget.WebGL);
            
            Debug.Log("✅ All builds completed!");
            
            if (autoRunBuild)
            {
                OpenBuildFolder();
            }
        }
        
        private void BuildForPlatform(BuildTarget target)
        {
            Debug.Log($"Building for {target}...");
            
            // Setup build options
            BuildPlayerOptions buildOptions = new BuildPlayerOptions();
            buildOptions.scenes = GetScenePaths();
            buildOptions.target = target;
            buildOptions.options = developmentBuild ? BuildOptions.Development : BuildOptions.None;
            
            // Set platform-specific settings
            string platformName = GetPlatformName(target);
            string extension = GetPlatformExtension(target);
            
            buildOptions.locationPathName = Path.Combine(buildPath, platformName, appName + extension);
            
            // Ensure directory exists
            string directory = Path.GetDirectoryName(buildOptions.locationPathName);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            // Switch platform if needed
            if (EditorUserBuildSettings.activeBuildTarget != target)
            {
                Debug.Log($"Switching to {target} platform...");
                EditorUserBuildSettings.SwitchActiveBuildTarget(GetBuildTargetGroup(target), target);
            }
            
            // Configure platform-specific settings
            ConfigurePlatformSettings(target);
            
            // Build
            BuildReport report = BuildPipeline.BuildPlayer(buildOptions);
            
            // Check result
            if (report.summary.result == BuildResult.Succeeded)
            {
                Debug.Log($"✅ {platformName} build succeeded!");
                Debug.Log($"Build size: {report.summary.totalSize} bytes");
                Debug.Log($"Build time: {report.summary.totalTime}");
            }
            else
            {
                Debug.LogError($"❌ {platformName} build failed!");
                foreach (var step in report.steps)
                {
                    if (step.messages.Length > 0)
                    {
                        foreach (var message in step.messages)
                        {
                            if (message.type == LogType.Error)
                            {
                                Debug.LogError($"Build Error: {message.content}");
                            }
                        }
                    }
                }
            }
        }
        
        private void SetupProjectForBuild()
        {
            Debug.Log("Setting up project for build...");
            
            // Set version
            PlayerSettings.bundleVersion = version;
            
            // Set app name
            PlayerSettings.productName = appName;
            
            // Set company name
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                PlayerSettings.companyName = "Space Clicker Studio";
            }
            
            // Set bundle identifier for mobile
            if (string.IsNullOrEmpty(PlayerSettings.applicationIdentifier))
            {
                PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, $"com.{PlayerSettings.companyName.ToLower().Replace(" ", "")}.{appName.ToLower()}");
                PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, $"com.{PlayerSettings.companyName.ToLower().Replace(" ", "")}.{appName.ToLower()}");
            }
            
            // Set default icon if not set
            Texture2D[] icons = PlayerSettings.GetIconsForTargetGroup(BuildTargetGroup.Unknown);
            if (icons == null || icons.Length == 0 || icons[0] == null)
            {
                // Try to find a default icon
                Texture2D defaultIcon = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/Sprites/Icons/AppIcon.png");
                if (defaultIcon == null)
                {
                    // Create a simple default icon
                    defaultIcon = CreateDefaultIcon();
                }
                
                if (defaultIcon != null)
                {
                    PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Unknown, new Texture2D[] { defaultIcon });
                }
            }
            
            Debug.Log("✅ Project setup completed");
        }
        
        private void ConfigurePlatformSettings(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.WebGL:
                    // WebGL specific settings
                    PlayerSettings.WebGL.compressionFormat = WebGLCompressionFormat.Gzip;
                    PlayerSettings.WebGL.memorySize = 512; // MB
                    PlayerSettings.WebGL.exceptionSupport = WebGLExceptionSupport.None;
                    break;
                    
                case BuildTarget.Android:
                    // Android specific settings
                    PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevelAuto;
                    PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel21;
                    EditorUserBuildSettings.buildAppBundle = false; // Build APK
                    break;
                    
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneOSX:
                case BuildTarget.StandaloneLinux64:
                    // Desktop specific settings
                    PlayerSettings.defaultScreenWidth = 1280;
                    PlayerSettings.defaultScreenHeight = 720;
                    PlayerSettings.resizableWindow = true;
                    break;
            }
        }
        
        private string[] GetScenePaths()
        {
            // Get all scenes in build settings
            string[] scenes = new string[EditorBuildSettings.scenes.Length];
            for (int i = 0; i < scenes.Length; i++)
            {
                scenes[i] = EditorBuildSettings.scenes[i].path;
            }
            
            // If no scenes in build settings, add current scene
            if (scenes.Length == 0)
            {
                scenes = new string[] { UnityEngine.SceneManagement.SceneManager.GetActiveScene().path };
            }
            
            return scenes;
        }
        
        private string GetPlatformName(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.StandaloneWindows64: return "Windows";
                case BuildTarget.StandaloneOSX: return "macOS";
                case BuildTarget.StandaloneLinux64: return "Linux";
                case BuildTarget.Android: return "Android";
                case BuildTarget.WebGL: return "WebGL";
                default: return target.ToString();
            }
        }
        
        private string GetPlatformExtension(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.StandaloneWindows64: return ".exe";
                case BuildTarget.StandaloneOSX: return ".app";
                case BuildTarget.StandaloneLinux64: return "";
                case BuildTarget.Android: return ".apk";
                case BuildTarget.WebGL: return "";
                default: return "";
            }
        }
        
        private BuildTargetGroup GetBuildTargetGroup(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneOSX:
                case BuildTarget.StandaloneLinux64:
                    return BuildTargetGroup.Standalone;
                case BuildTarget.Android:
                    return BuildTargetGroup.Android;
                case BuildTarget.WebGL:
                    return BuildTargetGroup.WebGL;
                default:
                    return BuildTargetGroup.Unknown;
            }
        }
        
        private Texture2D CreateDefaultIcon()
        {
            int size = 128;
            Texture2D icon = new Texture2D(size, size);
            
            // Create a simple space-themed icon
            Vector2 center = new Vector2(size / 2f, size / 2f);
            
            for (int x = 0; x < size; x++)
            {
                for (int y = 0; y < size; y++)
                {
                    Vector2 pos = new Vector2(x, y);
                    float distance = Vector2.Distance(pos, center);
                    
                    if (distance < size / 2f - 10)
                    {
                        // Space background
                        Color spaceColor = new Color(0.1f, 0.05f, 0.2f, 1f);
                        icon.SetPixel(x, y, spaceColor);
                    }
                    else
                    {
                        icon.SetPixel(x, y, Color.clear);
                    }
                }
            }
            
            // Add some stars
            for (int i = 0; i < 20; i++)
            {
                int starX = Random.Range(10, size - 10);
                int starY = Random.Range(10, size - 10);
                icon.SetPixel(starX, starY, Color.white);
            }
            
            icon.Apply();
            
            // Save the icon
            string iconPath = "Assets/Sprites/Icons/DefaultAppIcon.png";
            Directory.CreateDirectory(Path.GetDirectoryName(iconPath));
            File.WriteAllBytes(iconPath, icon.EncodeToPNG());
            AssetDatabase.ImportAsset(iconPath);
            
            return AssetDatabase.LoadAssetAtPath<Texture2D>(iconPath);
        }
        
        private void OpenBuildFolder()
        {
            if (Directory.Exists(buildPath))
            {
                EditorUtility.RevealInFinder(buildPath);
            }
            else
            {
                Debug.LogWarning("Build folder does not exist yet!");
            }
        }
        
        private void CleanBuildFolder()
        {
            if (EditorUtility.DisplayDialog("Clean Build Folder", 
                "Are you sure you want to delete all builds? This cannot be undone.", 
                "Yes", "Cancel"))
            {
                if (Directory.Exists(buildPath))
                {
                    Directory.Delete(buildPath, true);
                    Debug.Log("🧹 Build folder cleaned!");
                }
            }
        }
    }
}
