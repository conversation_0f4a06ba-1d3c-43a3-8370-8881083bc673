Metadata-Version: 2.1
Name: prawcore
Version: 2.4.0
Summary: "Low-level communication layer for PRAW 4+.
Keywords: praw,reddit,api
Author-email: <PERSON> <bbz<PERSON><PERSON><PERSON>@gmail.com>
Maintainer-email: <PERSON> <bbz<PERSON><PERSON><PERSON>@gmail.com>
Requires-Python: ~=3.8
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Natural Language :: English
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Dist: requests >=2.6.0, <3.0
Requires-Dist: coveralls ; extra == "ci"
Requires-Dist: packaging ; extra == "dev"
Requires-Dist: prawcore[lint] ; extra == "dev"
Requires-Dist: prawcore[test] ; extra == "dev"
Requires-Dist: pre-commit ; extra == "lint"
Requires-Dist: ruff >=0.0.291 ; extra == "lint"
Requires-Dist: betamax >=0.8, <0.9 ; extra == "test"
Requires-Dist: pytest >=2.7.3 ; extra == "test"
Requires-Dist: urllib3 ==1.26.*, <2 ; extra == "test"
Project-URL: Issue Tracker, https://github.com/praw-dev/prawcore/issues
Project-URL: Source Code, https://github.com/praw-dev/prawcore
Provides-Extra: ci
Provides-Extra: dev
Provides-Extra: lint
Provides-Extra: test

.. _main_page:

prawcore
========

.. image:: https://img.shields.io/pypi/v/prawcore.svg
    :alt: Latest prawcore Version
    :target: https://pypi.python.org/pypi/prawcore

.. image:: https://img.shields.io/pypi/pyversions/prawcore
    :alt: Supported Python Versions
    :target: https://pypi.python.org/pypi/prawcore

.. image:: https://img.shields.io/pypi/dm/prawcore
    :alt: PyPI - Downloads - Monthly
    :target: https://pypi.python.org/pypi/prawcore

.. image:: https://github.com/praw-dev/prawcore/actions/workflows/ci.yml/badge.svg?event=push
    :alt: GitHub Actions Status
    :target: https://github.com/praw-dev/prawcore/actions/workflows/ci.yml

.. image:: https://coveralls.io/repos/github/praw-dev/prawcore/badge.svg
    :alt: Coveralls Coverage
    :target: https://coveralls.io/github/praw-dev/prawcore

.. image:: https://api.securityscorecards.dev/projects/github.com/praw-dev/prawcore/badge
    :alt: OpenSSF Scorecard
    :target: https://api.securityscorecards.dev/projects/github.com/praw-dev/prawcore

.. image:: https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg
    :alt: Contributor Covenant
    :target: https://github.com/praw-dev/.github/blob/main/CODE_OF_CONDUCT.md

.. image:: https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white
    :alt: pre-commit
    :target: https://github.com/pre-commit/pre-commit

.. image:: https://img.shields.io/badge/code%20style-black-000000.svg
    :alt: Black code style
    :target: https://github.com/psf/black

prawcore is a low-level communication layer used by PRAW 4+.

Installation
------------

Install prawcore using ``pip`` via:

.. code-block:: console

    pip install prawcore

Execution Example
-----------------

The following example demonstrates how to use prawcore to obtain the list of trophies
for a given user using the script-app type. This example assumes you have the
environment variables ``PRAWCORE_CLIENT_ID`` and ``PRAWCORE_CLIENT_SECRET`` set to the
appropriate values for your application.

.. code-block:: python

    #!/usr/bin/env python
    import os
    import pprint
    import prawcore

    authenticator = prawcore.TrustedAuthenticator(
        prawcore.Requestor("YOUR_VALID_USER_AGENT"),
        os.environ["PRAWCORE_CLIENT_ID"],
        os.environ["PRAWCORE_CLIENT_SECRET"],
    )
    authorizer = prawcore.ReadOnlyAuthorizer(authenticator)
    authorizer.refresh()

    with prawcore.session(authorizer) as session:
        pprint.pprint(session.request("GET", "/api/v1/user/bboe/trophies"))

Save the above as ``trophies.py`` and then execute via:

.. code-block:: console

    python trophies.py

Additional examples can be found at:
https://github.com/praw-dev/prawcore/tree/main/examples

Depending on prawcore
---------------------

prawcore follows `semantic versioning <http://semver.org/>`_ with the exception that
deprecations will not be preceded by a minor release. In essence, expect only major
versions to introduce breaking changes to prawcore's public interface. As a result, if
you depend on prawcore then it is a good idea to specify not only the minimum version of
prawcore your package requires, but to also limit the major version.

Below are two examples of how you may want to specify your prawcore dependency:

setup.py
~~~~~~~~

.. code-block:: python

    setup(..., install_requires=["prawcore >=0.1, <1"], ...)

requirements.txt
~~~~~~~~~~~~~~~~

.. code-block:: text

    prawcore >=1.5.1, <2

