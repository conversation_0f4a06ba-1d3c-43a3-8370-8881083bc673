using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using ChronoForge.Player;
using ChronoForge.Procedural;
using ChronoForge.UI;
using ChronoForge.Audio;

namespace ChronoForge.Core
{
    /// <summary>
    /// Gestionnaire principal du jeu ChronoForge
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game State")]
        public GameState currentState = GameState.MainMenu;
        public bool isPaused = false;
        
        [Header("Managers")]
        public RunManager runManager;
        public ProgressionManager progressionManager;
        public SaveSystem saveSystem;
        
        [Header("Scene References")]
        public string mainMenuScene = "MainMenu";
        public string gameScene = "GameScene";
        public string hubScene = "Hub";
        
        [Header("Game Settings")]
        public float gameTimeScale = 1f;
        public bool debugMode = false;
        
        // Events
        public static System.Action<GameState> OnGameStateChanged;
        public static System.Action OnGamePaused;
        public static System.Action OnGameResumed;
        public static System.Action OnPlayerDeath;
        public static System.Action OnRunCompleted;
        
        // Singleton
        public static GameManager Instance { get; private set; }
        
        // Private fields
        private PlayerController player;
        private LevelGenerator levelGenerator;
        private UIManager uiManager;
        private AudioManager audioManager;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartCoroutine(InitializeGameSystems());
        }
        
        private void Update()
        {
            HandleInput();
            UpdateGameSystems();
        }
        
        private void OnDestroy()
        {
            CleanupEvents();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeGame()
        {
            // Setup basic game settings
            Application.targetFrameRate = 60;
            Time.timeScale = gameTimeScale;
            
            // Initialize core systems
            if (saveSystem == null)
                saveSystem = gameObject.AddComponent<SaveSystem>();
            
            if (progressionManager == null)
                progressionManager = gameObject.AddComponent<ProgressionManager>();
            
            if (runManager == null)
                runManager = gameObject.AddComponent<RunManager>();
            
            // Subscribe to events
            SubscribeToEvents();
            
            UnityEngine.Debug.Log("🎮 ChronoForge GameManager initialized");
        }
        
        private IEnumerator InitializeGameSystems()
        {
            yield return new WaitForSeconds(0.1f);
            
            // Find and initialize other managers
            FindManagers();
            
            // Load saved data
            if (saveSystem != null)
            {
                saveSystem.LoadGame();
            }
            
            // Set initial state
            ChangeGameState(GameState.MainMenu);
            
            UnityEngine.Debug.Log("✅ All game systems initialized");
        }
        
        private void FindManagers()
        {
            // Find managers in scene
            if (uiManager == null)
                uiManager = FindFirstObjectByType<UIManager>();
            
            if (audioManager == null)
                audioManager = FindFirstObjectByType<AudioManager>();
            
            if (levelGenerator == null)
                levelGenerator = FindFirstObjectByType<LevelGenerator>();
        }
        
        #endregion
        
        #region Game State Management
        
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            UnityEngine.Debug.Log($"🔄 Game state changed: {previousState} → {newState}");
            
            // Handle state transitions
            HandleStateTransition(previousState, newState);
            
            // Notify listeners
            OnGameStateChanged?.Invoke(newState);
        }
        
        private void HandleStateTransition(GameState from, GameState to)
        {
            switch (to)
            {
                case GameState.MainMenu:
                    HandleMainMenuState();
                    break;
                    
                case GameState.Hub:
                    HandleHubState();
                    break;
                    
                case GameState.InRun:
                    HandleInRunState();
                    break;
                    
                case GameState.Paused:
                    HandlePausedState();
                    break;
                    
                case GameState.GameOver:
                    HandleGameOverState();
                    break;
                    
                case GameState.Victory:
                    HandleVictoryState();
                    break;
            }
        }
        
        private void HandleMainMenuState()
        {
            Time.timeScale = 1f;
            if (audioManager != null)
                audioManager.PlayMusic("MainMenuTheme");
        }
        
        private void HandleHubState()
        {
            Time.timeScale = 1f;
            if (audioManager != null)
                audioManager.PlayMusic("HubTheme");
        }
        
        private void HandleInRunState()
        {
            Time.timeScale = gameTimeScale;
            if (audioManager != null)
                audioManager.PlayMusic("GameplayTheme");
        }
        
        private void HandlePausedState()
        {
            Time.timeScale = 0f;
            isPaused = true;
            OnGamePaused?.Invoke();
        }
        
        private void HandleGameOverState()
        {
            Time.timeScale = 0.5f; // Slow motion effect
            if (audioManager != null)
                audioManager.PlaySFX("PlayerDeath");
            
            OnPlayerDeath?.Invoke();
            
            // Return to normal time after a delay
            StartCoroutine(ReturnToNormalTimeAfterDelay(2f));
        }
        
        private void HandleVictoryState()
        {
            Time.timeScale = 1f;
            if (audioManager != null)
                audioManager.PlaySFX("Victory");
            
            OnRunCompleted?.Invoke();
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleInput()
        {
            // Pause/Resume
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (currentState == GameState.InRun)
                {
                    TogglePause();
                }
                else if (currentState == GameState.Paused)
                {
                    ResumeGame();
                }
            }
            
            // Debug commands
            if (debugMode)
            {
                HandleDebugInput();
            }
        }
        
        private void HandleDebugInput()
        {
            if (Input.GetKeyDown(KeyCode.F1))
            {
                // Toggle debug UI
                if (uiManager != null)
                    uiManager.ToggleDebugUI();
            }
            
            if (Input.GetKeyDown(KeyCode.F2))
            {
                // Restart current run
                RestartRun();
            }
            
            if (Input.GetKeyDown(KeyCode.F3))
            {
                // Complete current run
                CompleteRun();
            }
        }
        
        #endregion
        
        #region Game Control
        
        public void StartNewRun()
        {
            UnityEngine.Debug.Log("🚀 Starting new run...");
            
            if (runManager != null)
            {
                runManager.StartNewRun();
            }
            
            ChangeGameState(GameState.InRun);
            LoadGameScene();
        }
        
        public void RestartRun()
        {
            UnityEngine.Debug.Log("🔄 Restarting run...");
            
            if (runManager != null)
            {
                runManager.RestartCurrentRun();
            }
            
            ChangeGameState(GameState.InRun);
            LoadGameScene();
        }
        
        public void CompleteRun()
        {
            UnityEngine.Debug.Log("🏆 Run completed!");
            
            if (runManager != null)
            {
                runManager.CompleteRun();
            }
            
            ChangeGameState(GameState.Victory);
            
            // Save progress
            if (saveSystem != null)
            {
                saveSystem.SaveGame();
            }
        }
        
        public void EndRun(bool victory = false)
        {
            UnityEngine.Debug.Log($"🔚 Run ended - Victory: {victory}");
            
            if (runManager != null)
            {
                runManager.EndRun(victory);
            }
            
            ChangeGameState(victory ? GameState.Victory : GameState.GameOver);
            
            // Return to hub after delay
            StartCoroutine(ReturnToHubAfterDelay(3f));
        }
        
        public void TogglePause()
        {
            if (isPaused)
            {
                ResumeGame();
            }
            else
            {
                PauseGame();
            }
        }
        
        public void PauseGame()
        {
            if (currentState == GameState.InRun)
            {
                ChangeGameState(GameState.Paused);
            }
        }
        
        public void ResumeGame()
        {
            if (currentState == GameState.Paused)
            {
                isPaused = false;
                Time.timeScale = gameTimeScale;
                ChangeGameState(GameState.InRun);
                OnGameResumed?.Invoke();
            }
        }
        
        #endregion
        
        #region Scene Management
        
        public void LoadMainMenu()
        {
            ChangeGameState(GameState.MainMenu);
            SceneManager.LoadScene(mainMenuScene);
        }
        
        public void LoadHub()
        {
            ChangeGameState(GameState.Hub);
            SceneManager.LoadScene(hubScene);
        }
        
        public void LoadGameScene()
        {
            SceneManager.LoadScene(gameScene);
        }
        
        #endregion
        
        #region System Updates
        
        private void UpdateGameSystems()
        {
            // Update managers based on current state
            if (currentState == GameState.InRun && !isPaused)
            {
                if (runManager != null)
                    runManager.UpdateRun();
            }
        }
        
        #endregion
        
        #region Event Management
        
        private void SubscribeToEvents()
        {
            // Subscribe to player events
            PlayerController.OnPlayerDeath += HandlePlayerDeath;
            
            // Subscribe to run events
            RunManager.OnRunStarted += HandleRunStarted;
            RunManager.OnRunCompleted += HandleRunCompleted;
            RunManager.OnRunFailed += HandleRunFailed;
        }
        
        private void CleanupEvents()
        {
            // Unsubscribe from events
            PlayerController.OnPlayerDeath -= HandlePlayerDeath;
            RunManager.OnRunStarted -= HandleRunStarted;
            RunManager.OnRunCompleted -= HandleRunCompleted;
            RunManager.OnRunFailed -= HandleRunFailed;
        }
        
        private void HandlePlayerDeath()
        {
            EndRun(false);
        }
        
        private void HandleRunStarted()
        {
            UnityEngine.Debug.Log("🎯 Run started");
        }
        
        private void HandleRunCompleted()
        {
            CompleteRun();
        }
        
        private void HandleRunFailed()
        {
            EndRun(false);
        }
        
        #endregion
        
        #region Coroutines
        
        private IEnumerator ReturnToNormalTimeAfterDelay(float delay)
        {
            yield return new WaitForSecondsRealtime(delay);
            Time.timeScale = 1f;
        }
        
        private IEnumerator ReturnToHubAfterDelay(float delay)
        {
            yield return new WaitForSecondsRealtime(delay);
            LoadHub();
        }
        
        #endregion
        
        #region Public Getters
        
        public bool IsInRun()
        {
            return currentState == GameState.InRun;
        }
        
        public bool IsGamePaused()
        {
            return isPaused;
        }
        
        public PlayerController GetPlayer()
        {
            if (player == null)
                player = FindFirstObjectByType<PlayerController>();
            return player;
        }
        
        public RunManager GetRunManager()
        {
            return runManager;
        }
        
        public ProgressionManager GetProgressionManager()
        {
            return progressionManager;
        }

        public void SaveGame()
        {
            if (saveSystem != null)
            {
                saveSystem.SaveGame();
            }
        }

        public void ReturnToMainMenu()
        {
            Time.timeScale = 1f;
            UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
        }

        public void RestartGame()
        {
            RestartRun();
        }
        
        #endregion
    }
    
    /// <summary>
    /// États possibles du jeu
    /// </summary>
    public enum GameState
    {
        MainMenu,
        Hub,
        InRun,
        Paused,
        GameOver,
        Victory,
        Loading
    }
}
