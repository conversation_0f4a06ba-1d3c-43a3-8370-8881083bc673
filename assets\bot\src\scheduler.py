"""
Planificateur pour automatiser les publications du bot.
Utilise APScheduler pour gérer les tâches programmées.
"""

from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from datetime import datetime, timedelta
import logging
import pytz
from typing import Dict, Any, List, Optional

from .content_manager import ContentManager, Post
from .publisher import Publisher

logger = logging.getLogger(__name__)

class BotScheduler:
    """Planificateur principal du bot de publication."""
    
    def __init__(self, config: Dict[str, Any], content_manager: ContentManager, publisher: Publisher):
        self.config = config
        self.content_manager = content_manager
        self.publisher = publisher
        
        # Configuration du scheduler
        self.timezone = pytz.timezone(config.get('general', {}).get('timezone', 'Europe/Paris'))
        self.scheduler = None
        self.is_running = False
        
        # Heures de travail
        working_hours = config.get('scheduler', {}).get('working_hours', {})
        self.work_start = working_hours.get('start', '09:00')
        self.work_end = working_hours.get('end', '18:00')
        self.working_days = config.get('scheduler', {}).get('working_days', [1, 2, 3, 4, 5])  # Lun-Ven
        
    def start(self, blocking: bool = True):
        """Démarre le planificateur."""
        if self.is_running:
            logger.warning("Le planificateur est déjà en cours d'exécution")
            return
        
        # Choisir le type de scheduler
        if blocking:
            self.scheduler = BlockingScheduler(timezone=self.timezone)
        else:
            self.scheduler = BackgroundScheduler(timezone=self.timezone)
        
        # Ajouter les tâches programmées
        self._setup_scheduled_tasks()
        
        # Démarrer le scheduler
        try:
            self.scheduler.start()
            self.is_running = True
            logger.info("✅ Planificateur démarré")
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage du planificateur: {e}")
    
    def stop(self):
        """Arrête le planificateur."""
        if self.scheduler and self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("⏹️ Planificateur arrêté")
    
    def _setup_scheduled_tasks(self):
        """Configure toutes les tâches programmées."""
        # Tâche principale: vérifier les posts à publier
        self.scheduler.add_job(
            func=self._check_and_publish_posts,
            trigger=IntervalTrigger(minutes=5),  # Vérifier toutes les 5 minutes
            id='check_posts',
            name='Vérification des posts programmés',
            max_instances=1
        )
        
        # Tâche de maintenance: nettoyer les logs anciens
        self.scheduler.add_job(
            func=self._cleanup_old_logs,
            trigger=CronTrigger(hour=2, minute=0),  # Tous les jours à 2h
            id='cleanup_logs',
            name='Nettoyage des logs',
            max_instances=1
        )
        
        # Tâche de sauvegarde: sauvegarder les logs de publication
        self.scheduler.add_job(
            func=self._save_publication_logs,
            trigger=IntervalTrigger(hours=1),  # Toutes les heures
            id='save_logs',
            name='Sauvegarde des logs',
            max_instances=1
        )
        
        logger.info("Tâches programmées configurées")
    
    def _check_and_publish_posts(self):
        """Vérifie et publie les posts programmés."""
        try:
            # Vérifier si on est dans les heures de travail
            if not self._is_working_time():
                logger.debug("En dehors des heures de travail, pas de publication")
                return
            
            # Recharger les posts
            self.content_manager.load_posts()
            
            # Récupérer les posts à publier maintenant
            current_time = datetime.now(self.timezone)
            scheduled_posts = self.content_manager.get_scheduled_posts(current_time)
            
            if not scheduled_posts:
                logger.debug("Aucun post programmé pour le moment")
                return
            
            logger.info(f"📅 {len(scheduled_posts)} post(s) programmé(s) trouvé(s)")
            
            # Publier chaque post
            for post in scheduled_posts:
                try:
                    self._publish_single_post(post)
                except Exception as e:
                    logger.error(f"Erreur lors de la publication du post {post.id}: {e}")
                    
        except Exception as e:
            logger.error(f"Erreur dans _check_and_publish_posts: {e}")
    
    def _publish_single_post(self, post: Post):
        """Publie un post unique sur toutes ses plateformes."""
        logger.info(f"🚀 Publication du post: {post.title}")
        
        results = self.publisher.publish_to_all_platforms(post)
        
        # Traiter les résultats
        success_count = sum(1 for result in results if result.success)
        total_count = len(results)
        
        if success_count > 0:
            logger.info(f"✅ Post {post.id} publié sur {success_count}/{total_count} plateformes")
        else:
            logger.error(f"❌ Échec de publication du post {post.id} sur toutes les plateformes")
        
        # Marquer comme publié dans le content manager
        for result in results:
            self.content_manager.mark_as_published(post, result.platform, result.success)
    
    def _is_working_time(self) -> bool:
        """Vérifie si on est dans les heures de travail."""
        now = datetime.now(self.timezone)
        
        # Vérifier le jour de la semaine (1=Lundi, 7=Dimanche)
        if now.isoweekday() not in self.working_days:
            return False
        
        # Vérifier l'heure
        work_start_time = datetime.strptime(self.work_start, '%H:%M').time()
        work_end_time = datetime.strptime(self.work_end, '%H:%M').time()
        current_time = now.time()
        
        return work_start_time <= current_time <= work_end_time
    
    def _cleanup_old_logs(self):
        """Nettoie les anciens logs."""
        try:
            import os
            import glob
            from pathlib import Path
            
            log_dir = Path(".")
            cutoff_date = datetime.now() - timedelta(days=30)  # Garder 30 jours
            
            for log_file in glob.glob("*.log"):
                file_path = Path(log_file)
                if file_path.exists():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        logger.info(f"🗑️ Log supprimé: {log_file}")
                        
        except Exception as e:
            logger.error(f"Erreur lors du nettoyage des logs: {e}")
    
    def _save_publication_logs(self):
        """Sauvegarde les logs de publication."""
        try:
            self.content_manager.save_published_log()
            logger.debug("💾 Logs de publication sauvegardés")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des logs: {e}")
    
    def add_one_time_post(self, post: Post, publish_time: datetime):
        """Ajoute une publication ponctuelle."""
        job_id = f"oneshot_{post.id}_{publish_time.strftime('%Y%m%d_%H%M%S')}"
        
        self.scheduler.add_job(
            func=self._publish_single_post,
            trigger=DateTrigger(run_date=publish_time),
            args=[post],
            id=job_id,
            name=f"Publication ponctuelle: {post.title}",
            max_instances=1
        )
        
        logger.info(f"📅 Publication programmée pour {publish_time}: {post.title}")
    
    def list_scheduled_jobs(self) -> List[Dict[str, Any]]:
        """Liste toutes les tâches programmées."""
        if not self.scheduler:
            return []
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return jobs
    
    def remove_job(self, job_id: str) -> bool:
        """Supprime une tâche programmée."""
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"🗑️ Tâche supprimée: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de la tâche {job_id}: {e}")
            return False
