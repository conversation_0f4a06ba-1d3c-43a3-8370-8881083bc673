using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Contrôleur pour les animations d'interface utilisateur
    /// </summary>
    public class UIAnimationController : MonoBehaviour
    {
        [Header("Startup Animation")]
        public CanvasGroup mainCanvas;
        public float startupFadeDuration = 2f;
        public AnimationCurve startupCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Module Entrance")]
        public Transform[] modules;
        public float moduleEntranceDelay = 0.2f;
        public float moduleEntranceDuration = 0.5f;
        
        [Header("Resource Counter Animations")]
        public float counterUpdateDuration = 0.3f;
        public AnimationCurve counterCurve = AnimationCurve.EaseOut(0, 0, 1, 1);
        
        [Header("Notification System")]
        public Transform notificationParent;
        public GameObject notificationPrefab;
        public float notificationDuration = 3f;
        public int maxNotifications = 5;
        
        [Header("Screen Transitions")]
        public Image transitionOverlay;
        public float transitionDuration = 0.5f;
        
        // Private fields
        private Queue<GameObject> notificationPool = new Queue<GameObject>();
        private List<GameObject> activeNotifications = new List<GameObject>();
        private bool isTransitioning = false;
        
        // Singleton pattern
        public static UIAnimationController Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeNotificationPool();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            PlayStartupAnimation();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeNotificationPool()
        {
            if (notificationPrefab == null || notificationParent == null) return;
            
            // Pre-instantiate notification objects
            for (int i = 0; i < maxNotifications; i++)
            {
                GameObject notification = Instantiate(notificationPrefab, notificationParent);
                notification.SetActive(false);
                notificationPool.Enqueue(notification);
            }
        }
        
        #endregion
        
        #region Startup Animation
        
        private void PlayStartupAnimation()
        {
            StartCoroutine(StartupSequence());
        }
        
        private IEnumerator StartupSequence()
        {
            // Start with everything invisible
            if (mainCanvas != null)
            {
                mainCanvas.alpha = 0f;
            }
            
            // Hide all modules initially
            foreach (Transform module in modules)
            {
                if (module != null)
                {
                    module.localScale = Vector3.zero;
                }
            }
            
            // Fade in main canvas
            if (mainCanvas != null)
            {
                yield return StartCoroutine(FadeInCanvas());
            }
            
            // Animate modules entrance
            yield return StartCoroutine(AnimateModulesEntrance());
        }
        
        private IEnumerator FadeInCanvas()
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < startupFadeDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / startupFadeDuration;
                float curveValue = startupCurve.Evaluate(progress);
                
                mainCanvas.alpha = curveValue;
                
                yield return null;
            }
            
            mainCanvas.alpha = 1f;
        }
        
        private IEnumerator AnimateModulesEntrance()
        {
            foreach (Transform module in modules)
            {
                if (module != null)
                {
                    StartCoroutine(AnimateModuleEntrance(module));
                    yield return new WaitForSeconds(moduleEntranceDelay);
                }
            }
        }
        
        private IEnumerator AnimateModuleEntrance(Transform module)
        {
            Vector3 targetScale = Vector3.one;
            float elapsedTime = 0f;
            
            while (elapsedTime < moduleEntranceDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / moduleEntranceDuration;
                
                // Bounce effect
                float scale = Mathf.Sin(progress * Mathf.PI);
                if (progress > 0.5f)
                {
                    scale = 1f + (1f - progress) * 0.2f;
                }
                
                module.localScale = targetScale * scale;
                
                yield return null;
            }
            
            module.localScale = targetScale;
        }
        
        #endregion
        
        #region Counter Animations
        
        public void AnimateCounterUpdate(TextMeshProUGUI counterText, string fromValue, string toValue)
        {
            if (counterText == null) return;
            
            StartCoroutine(CounterUpdateCoroutine(counterText, fromValue, toValue));
        }
        
        private IEnumerator CounterUpdateCoroutine(TextMeshProUGUI counterText, string fromValue, string toValue)
        {
            float elapsedTime = 0f;
            
            // Parse numeric values for interpolation if possible
            bool canInterpolate = float.TryParse(fromValue.Replace(",", ""), out float fromNum) && 
                                 float.TryParse(toValue.Replace(",", ""), out float toNum);
            
            while (elapsedTime < counterUpdateDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / counterUpdateDuration;
                float curveValue = counterCurve.Evaluate(progress);
                
                if (canInterpolate)
                {
                    // Interpolate numeric values
                    float currentValue = Mathf.Lerp(fromNum, toNum, curveValue);
                    counterText.text = FormatNumber(currentValue);
                }
                else
                {
                    // Just show the target value
                    counterText.text = toValue;
                }
                
                // Scale effect
                float scale = 1f + Mathf.Sin(progress * Mathf.PI) * 0.1f;
                counterText.transform.localScale = Vector3.one * scale;
                
                yield return null;
            }
            
            // Ensure final values
            counterText.text = toValue;
            counterText.transform.localScale = Vector3.one;
        }
        
        private string FormatNumber(float number)
        {
            if (number >= 1000000)
                return (number / 1000000f).ToString("F1") + "M";
            else if (number >= 1000)
                return (number / 1000f).ToString("F1") + "K";
            else
                return number.ToString("F0");
        }
        
        #endregion
        
        #region Notification System
        
        public void ShowNotification(string message, Color color, float duration = -1f)
        {
            if (duration < 0) duration = notificationDuration;
            
            GameObject notification = GetPooledNotification();
            if (notification == null) return;
            
            // Setup notification
            TextMeshProUGUI text = notification.GetComponentInChildren<TextMeshProUGUI>();
            Image background = notification.GetComponent<Image>();
            
            if (text != null) text.text = message;
            if (background != null) background.color = color;
            
            // Position notification
            PositionNotification(notification);
            
            // Animate notification
            StartCoroutine(AnimateNotification(notification, duration));
        }
        
        private GameObject GetPooledNotification()
        {
            // Remove expired notifications first
            CleanupNotifications();
            
            if (notificationPool.Count > 0)
            {
                GameObject notification = notificationPool.Dequeue();
                notification.SetActive(true);
                activeNotifications.Add(notification);
                return notification;
            }
            
            return null;
        }
        
        private void PositionNotification(GameObject notification)
        {
            // Stack notifications vertically
            float yOffset = activeNotifications.Count * 60f; // 60 pixels per notification
            
            RectTransform rectTransform = notification.GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                rectTransform.anchoredPosition = new Vector2(0, -yOffset);
            }
        }
        
        private IEnumerator AnimateNotification(GameObject notification, float duration)
        {
            RectTransform rectTransform = notification.GetComponent<RectTransform>();
            CanvasGroup canvasGroup = notification.GetComponent<CanvasGroup>();
            
            if (canvasGroup == null)
            {
                canvasGroup = notification.AddComponent<CanvasGroup>();
            }
            
            // Slide in from right
            Vector2 startPos = rectTransform.anchoredPosition + Vector2.right * 300f;
            Vector2 targetPos = rectTransform.anchoredPosition;
            
            rectTransform.anchoredPosition = startPos;
            canvasGroup.alpha = 0f;
            
            // Animate in
            float slideInDuration = 0.3f;
            float elapsedTime = 0f;
            
            while (elapsedTime < slideInDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / slideInDuration;
                
                rectTransform.anchoredPosition = Vector2.Lerp(startPos, targetPos, progress);
                canvasGroup.alpha = progress;
                
                yield return null;
            }
            
            rectTransform.anchoredPosition = targetPos;
            canvasGroup.alpha = 1f;
            
            // Wait for display duration
            yield return new WaitForSeconds(duration - slideInDuration - 0.3f);
            
            // Animate out
            elapsedTime = 0f;
            while (elapsedTime < 0.3f)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / 0.3f;
                
                canvasGroup.alpha = 1f - progress;
                
                yield return null;
            }
            
            // Return to pool
            ReturnNotificationToPool(notification);
        }
        
        private void ReturnNotificationToPool(GameObject notification)
        {
            notification.SetActive(false);
            activeNotifications.Remove(notification);
            notificationPool.Enqueue(notification);
            
            // Reposition remaining notifications
            RepositionNotifications();
        }
        
        private void RepositionNotifications()
        {
            for (int i = 0; i < activeNotifications.Count; i++)
            {
                RectTransform rectTransform = activeNotifications[i].GetComponent<RectTransform>();
                if (rectTransform != null)
                {
                    Vector2 targetPos = new Vector2(0, -i * 60f);
                    LeanTween.move(rectTransform, targetPos, 0.2f).setEase(LeanTweenType.easeOutQuad);
                }
            }
        }
        
        private void CleanupNotifications()
        {
            // Remove any null references
            activeNotifications.RemoveAll(n => n == null);
        }
        
        #endregion
        
        #region Screen Transitions
        
        public void TransitionToScene(string sceneName)
        {
            if (isTransitioning) return;
            
            StartCoroutine(SceneTransitionCoroutine(sceneName));
        }
        
        private IEnumerator SceneTransitionCoroutine(string sceneName)
        {
            isTransitioning = true;
            
            // Fade to black
            if (transitionOverlay != null)
            {
                transitionOverlay.gameObject.SetActive(true);
                
                float elapsedTime = 0f;
                Color startColor = transitionOverlay.color;
                Color targetColor = new Color(startColor.r, startColor.g, startColor.b, 1f);
                
                while (elapsedTime < transitionDuration)
                {
                    elapsedTime += Time.deltaTime;
                    float progress = elapsedTime / transitionDuration;
                    
                    transitionOverlay.color = Color.Lerp(startColor, targetColor, progress);
                    
                    yield return null;
                }
                
                transitionOverlay.color = targetColor;
            }
            
            // Load new scene
            UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Play a button click animation
        /// </summary>
        public void AnimateButtonClick(Button button)
        {
            if (button == null) return;
            
            Transform buttonTransform = button.transform;
            Vector3 originalScale = buttonTransform.localScale;
            
            LeanTween.scale(buttonTransform.gameObject, originalScale * 0.95f, 0.05f)
                .setEase(LeanTweenType.easeOutQuad)
                .setOnComplete(() => {
                    LeanTween.scale(buttonTransform.gameObject, originalScale, 0.1f)
                        .setEase(LeanTweenType.easeOutBounce);
                });
        }
        
        /// <summary>
        /// Show a success notification
        /// </summary>
        public void ShowSuccessNotification(string message)
        {
            ShowNotification(message, Color.green);
        }
        
        /// <summary>
        /// Show an error notification
        /// </summary>
        public void ShowErrorNotification(string message)
        {
            ShowNotification(message, Color.red);
        }
        
        /// <summary>
        /// Show an info notification
        /// </summary>
        public void ShowInfoNotification(string message)
        {
            ShowNotification(message, Color.blue);
        }
        
        #endregion
    }
}
