using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using ChronoForge.Combat;
using ChronoForge.Core;

namespace ChronoForge.Player
{
    /// <summary>
    /// Système de combat du joueur pour ChronoForge
    /// </summary>
    public class PlayerCombat : MonoBehaviour
    {
        [Header("Combat Settings")]
        public float attackRange = 2f;
        public float attackCooldown = 0.5f;
        public LayerMask enemyLayer = -1;
        
        [Header("Combo System")]
        public int maxComboCount = 3;
        public float comboWindow = 1f;
        public float comboMultiplier = 1.2f;
        
        [Header("Special Abilities")]
        public float specialCooldown = 5f;
        public float ultimateCooldown = 20f;
        
        [Header("Input")]
        public KeyCode attackKey = KeyCode.Mouse0;
        public KeyCode specialKey = KeyCode.Mouse1;
        public KeyCode ultimateKey = KeyCode.Q;
        
        [Header("Visual Effects")]
        public Transform attackPoint;
        public GameObject attackEffect;
        public GameObject specialEffect;
        public GameObject ultimateEffect;
        
        [Header("Audio")]
        public AudioClip[] attackSounds;
        public AudioClip specialSound;
        public AudioClip ultimateSound;
        
        // Events
        public static System.Action<float> OnAttack;
        public static System.Action<int> OnComboChanged;
        public static System.Action OnSpecialUsed;
        public static System.Action OnUltimateUsed;
        
        // Components
        private PlayerController playerController;
        private PlayerStats playerStats;
        private WeaponSystem weaponSystem;
        private AudioSource audioSource;
        
        // Combat state
        private bool canAttack = true;
        private float lastAttackTime = 0f;
        private bool isAttacking = false;
        
        // Combo system
        private int currentCombo = 0;
        private float lastComboTime = 0f;
        
        // Abilities
        private bool canUseSpecial = true;
        private bool canUseUltimate = true;
        private float specialCooldownTimer = 0f;
        private float ultimateCooldownTimer = 0f;
        
        // Targeting
        private List<Collider2D> enemiesInRange = new List<Collider2D>();
        private Transform currentTarget;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            InitializeCombat();
        }
        
        private void Update()
        {
            if (!playerController.IsAlive()) return;
            
            HandleCombatInput();
            UpdateCombat();
            UpdateTargeting();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeComponents()
        {
            playerController = GetComponent<PlayerController>();
            playerStats = GetComponent<PlayerStats>();
            weaponSystem = GetComponent<WeaponSystem>();
            audioSource = GetComponent<AudioSource>();
            
            if (attackPoint == null)
            {
                // Create attack point if not assigned
                GameObject attackPointObj = new GameObject("AttackPoint");
                attackPointObj.transform.SetParent(transform);
                attackPointObj.transform.localPosition = Vector3.forward;
                attackPoint = attackPointObj.transform;
            }
        }
        
        private void InitializeCombat()
        {
            // Reset combat state
            ResetCombat();
            
            UnityEngine.Debug.Log("⚔️ PlayerCombat initialized");
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleCombatInput()
        {
            // Basic attack
            if (Input.GetKeyDown(attackKey) && canAttack)
            {
                PerformAttack();
            }
            
            // Special ability
            if (Input.GetKeyDown(specialKey) && canUseSpecial)
            {
                PerformSpecialAbility();
            }
            
            // Ultimate ability
            if (Input.GetKeyDown(ultimateKey) && canUseUltimate)
            {
                PerformUltimateAbility();
            }
        }
        
        #endregion
        
        #region Basic Attack
        
        private void PerformAttack()
        {
            if (!canAttack || isAttacking) return;
            
            StartCoroutine(AttackCoroutine());
        }
        
        private IEnumerator AttackCoroutine()
        {
            isAttacking = true;
            canAttack = false;
            
            // Update combo
            UpdateCombo();
            
            // Calculate damage
            float baseDamage = playerStats != null ? playerStats.GetAttackDamage() : 10f;
            float comboDamage = baseDamage * (1f + (currentCombo - 1) * (comboMultiplier - 1f));
            
            // Apply weapon modifiers
            if (weaponSystem != null)
            {
                comboDamage = weaponSystem.ModifyDamage(comboDamage);
            }
            
            // Perform attack
            ExecuteAttack(comboDamage);
            
            // Visual and audio effects
            PlayAttackEffects();
            
            // Attack animation duration
            yield return new WaitForSeconds(0.2f);
            
            isAttacking = false;
            
            // Cooldown
            yield return new WaitForSeconds(attackCooldown);
            
            canAttack = true;
        }
        
        private void ExecuteAttack(float damage)
        {
            Vector2 attackDirection = playerController.GetMoveDirection();
            Vector3 attackPosition = attackPoint.position + (Vector3)attackDirection * attackRange * 0.5f;
            
            // Find enemies in attack range
            Collider2D[] hitEnemies = Physics2D.OverlapCircleAll(attackPosition, attackRange, enemyLayer);
            
            foreach (var enemy in hitEnemies)
            {
                // Apply damage
                HealthSystem enemyHealth = enemy.GetComponent<HealthSystem>();
                if (enemyHealth != null)
                {
                    enemyHealth.TakeDamage(damage);
                    
                    // Apply knockback
                    ApplyKnockback(enemy, attackDirection);
                }
                
                // Weapon-specific effects
                if (weaponSystem != null)
                {
                    weaponSystem.OnHitEnemy(enemy.gameObject);
                }
            }
            
            // Notify attack
            OnAttack?.Invoke(damage);
            
            UnityEngine.Debug.Log($"⚔️ Attack performed - Damage: {damage:F1}, Combo: {currentCombo}");
        }
        
        private void ApplyKnockback(Collider2D enemy, Vector2 direction)
        {
            Rigidbody2D enemyRb = enemy.GetComponent<Rigidbody2D>();
            if (enemyRb != null)
            {
                float knockbackForce = 500f;
                enemyRb.AddForce(direction * knockbackForce);
            }
        }
        
        #endregion
        
        #region Combo System
        
        private void UpdateCombo()
        {
            float timeSinceLastCombo = Time.time - lastComboTime;
            
            if (timeSinceLastCombo <= comboWindow && currentCombo < maxComboCount)
            {
                // Continue combo
                currentCombo++;
            }
            else
            {
                // Reset combo
                currentCombo = 1;
            }
            
            lastComboTime = Time.time;
            OnComboChanged?.Invoke(currentCombo);
            
            UnityEngine.Debug.Log($"🔥 Combo: {currentCombo}");
        }
        
        private void ResetCombo()
        {
            currentCombo = 0;
            OnComboChanged?.Invoke(currentCombo);
        }
        
        #endregion
        
        #region Special Abilities
        
        private void PerformSpecialAbility()
        {
            if (!canUseSpecial) return;
            
            // Get special ability from weapon or class
            if (weaponSystem != null)
            {
                weaponSystem.UseSpecialAbility();
            }
            else
            {
                // Default special ability
                DefaultSpecialAbility();
            }
            
            // Start cooldown
            canUseSpecial = false;
            specialCooldownTimer = specialCooldown;
            
            OnSpecialUsed?.Invoke();
            
            UnityEngine.Debug.Log("✨ Special ability used");
        }
        
        private void DefaultSpecialAbility()
        {
            // Default: Area damage around player
            Collider2D[] enemies = Physics2D.OverlapCircleAll(transform.position, 3f, enemyLayer);
            
            float specialDamage = playerStats != null ? playerStats.GetAttackDamage() * 2f : 20f;
            
            foreach (var enemy in enemies)
            {
                HealthSystem enemyHealth = enemy.GetComponent<HealthSystem>();
                if (enemyHealth != null)
                {
                    enemyHealth.TakeDamage(specialDamage);
                }
            }
            
            // Visual effect
            if (specialEffect != null)
            {
                Instantiate(specialEffect, transform.position, Quaternion.identity);
            }
            
            // Audio
            if (audioSource != null && specialSound != null)
            {
                audioSource.PlayOneShot(specialSound);
            }
        }
        
        private void PerformUltimateAbility()
        {
            if (!canUseUltimate) return;
            
            // Get ultimate ability from class
            if (playerStats != null)
            {
                playerStats.UseUltimateAbility();
            }
            else
            {
                // Default ultimate ability
                DefaultUltimateAbility();
            }
            
            // Start cooldown
            canUseUltimate = false;
            ultimateCooldownTimer = ultimateCooldown;
            
            OnUltimateUsed?.Invoke();
            
            UnityEngine.Debug.Log("💥 Ultimate ability used");
        }
        
        private void DefaultUltimateAbility()
        {
            // Default: Massive area damage with time slow
            StartCoroutine(UltimateCoroutine());
        }
        
        private IEnumerator UltimateCoroutine()
        {
            // Slow time effect
            Time.timeScale = 0.3f;
            
            // Massive damage in large area
            Collider2D[] enemies = Physics2D.OverlapCircleAll(transform.position, 5f, enemyLayer);
            
            float ultimateDamage = playerStats != null ? playerStats.GetAttackDamage() * 5f : 50f;
            
            foreach (var enemy in enemies)
            {
                HealthSystem enemyHealth = enemy.GetComponent<HealthSystem>();
                if (enemyHealth != null)
                {
                    enemyHealth.TakeDamage(ultimateDamage);
                }
            }
            
            // Visual effect
            if (ultimateEffect != null)
            {
                Instantiate(ultimateEffect, transform.position, Quaternion.identity);
            }
            
            // Audio
            if (audioSource != null && ultimateSound != null)
            {
                audioSource.PlayOneShot(ultimateSound);
            }
            
            // Wait for effect duration
            yield return new WaitForSecondsRealtime(1f);
            
            // Restore time
            Time.timeScale = 1f;
        }
        
        #endregion
        
        #region Targeting
        
        private void UpdateTargeting()
        {
            // Find nearest enemy for auto-targeting
            enemiesInRange.Clear();
            
            Collider2D[] nearbyEnemies = Physics2D.OverlapCircleAll(transform.position, attackRange * 2f, enemyLayer);
            
            foreach (var enemy in nearbyEnemies)
            {
                if (enemy.GetComponent<HealthSystem>() != null)
                {
                    enemiesInRange.Add(enemy);
                }
            }
            
            // Update current target
            UpdateCurrentTarget();
        }
        
        private void UpdateCurrentTarget()
        {
            if (enemiesInRange.Count == 0)
            {
                currentTarget = null;
                return;
            }
            
            // Find closest enemy
            float closestDistance = float.MaxValue;
            Transform closestEnemy = null;
            
            foreach (var enemy in enemiesInRange)
            {
                if (enemy == null) continue;
                
                float distance = Vector2.Distance(transform.position, enemy.transform.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy.transform;
                }
            }
            
            currentTarget = closestEnemy;
        }
        
        #endregion
        
        #region Combat Updates
        
        private void UpdateCombat()
        {
            // Update combo timer
            if (currentCombo > 0 && Time.time - lastComboTime > comboWindow)
            {
                ResetCombo();
            }
            
            // Update ability cooldowns
            UpdateAbilityCooldowns();
        }
        
        private void UpdateAbilityCooldowns()
        {
            // Special ability cooldown
            if (!canUseSpecial)
            {
                specialCooldownTimer -= Time.deltaTime;
                if (specialCooldownTimer <= 0f)
                {
                    canUseSpecial = true;
                    specialCooldownTimer = 0f;
                }
            }
            
            // Ultimate ability cooldown
            if (!canUseUltimate)
            {
                ultimateCooldownTimer -= Time.deltaTime;
                if (ultimateCooldownTimer <= 0f)
                {
                    canUseUltimate = true;
                    ultimateCooldownTimer = 0f;
                }
            }
        }
        
        #endregion
        
        #region Effects
        
        private void PlayAttackEffects()
        {
            // Visual effect
            if (attackEffect != null)
            {
                Vector3 effectPosition = attackPoint.position + (Vector3)playerController.GetMoveDirection() * attackRange * 0.5f;
                Instantiate(attackEffect, effectPosition, Quaternion.identity);
            }
            
            // Audio effect
            if (audioSource != null && attackSounds.Length > 0)
            {
                AudioClip randomAttackSound = attackSounds[Random.Range(0, attackSounds.Length)];
                audioSource.PlayOneShot(randomAttackSound);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void ResetForNewRun()
        {
            ResetCombat();
        }
        
        private void ResetCombat()
        {
            canAttack = true;
            isAttacking = false;
            lastAttackTime = 0f;
            
            ResetCombo();
            
            canUseSpecial = true;
            canUseUltimate = true;
            specialCooldownTimer = 0f;
            ultimateCooldownTimer = 0f;
            
            currentTarget = null;
            enemiesInRange.Clear();
        }
        
        public bool CanAttack()
        {
            return canAttack && !isAttacking;
        }
        
        public bool IsAttacking()
        {
            return isAttacking;
        }
        
        public int GetCurrentCombo()
        {
            return currentCombo;
        }
        
        public float GetSpecialCooldownProgress()
        {
            return canUseSpecial ? 1f : 1f - (specialCooldownTimer / specialCooldown);
        }
        
        public float GetUltimateCooldownProgress()
        {
            return canUseUltimate ? 1f : 1f - (ultimateCooldownTimer / ultimateCooldown);
        }
        
        public Transform GetCurrentTarget()
        {
            return currentTarget;
        }

        public void SetCanAttack(bool canAttack)
        {
            this.canAttack = canAttack;
        }

        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            // Draw attack range
            Gizmos.material.color = Color.red;
            Vector3 attackPos = attackPoint != null ? attackPoint.position : transform.position;
            Vector2 direction = playerController != null ? playerController.GetMoveDirection() : Vector2.down;
            Vector3 attackCenter = attackPos + (Vector3)direction * attackRange * 0.5f;
            Gizmos.DrawWireSphere(attackCenter, attackRange);
            
            // Draw targeting range
            Gizmos.material.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, attackRange * 2f);
            
            // Draw current target
            if (currentTarget != null)
            {
                Gizmos.material.color = Color.green;
                Gizmos.DrawLine(transform.position, currentTarget.position);
            }
        }
        
        #endregion
    }
}
