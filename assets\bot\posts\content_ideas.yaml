# Idées de contenu pour le bot de publication
# Exemples de posts pour différents types de contenu

posts:
  # Promotion de projet
  - id: "api2csv_security"
    title: "API2CSV - Sécurité et confidentialité"
    body: |
      🔒 Pourquoi API2CSV est sécurisé ?

      ✅ Traitement 100% local dans votre navigateur
      ✅ Aucune donnée envoyée sur nos serveurs
      ✅ Vos fichiers ne quittent jamais votre ordinateur
      ✅ Code open source auditable

      Parfait pour traiter des données sensibles en toute sécurité !

      👉 https://neethdseven.github.io/api2csv/

      #security #privacy #webdev #opensource #tools
    platforms: ["twitter", "reddit"]
    schedule: "2025-07-01 10:00"
    tags: ["security", "privacy", "webdev", "opensource"]

    
  # Astuce technique
  - id: "json_to_csv_tips"
    title: "Astuce : Convertir JSON complexe en CSV"
    body: |
      💡 Astuce du jour : Gérer les JSON complexes

      Problème : JSON avec des objets imbriqués ?
      Solution avec API2CSV :

      1️⃣ Collez votre JSON complexe
      2️⃣ L'outil détecte automatiquement la structure
      3️⃣ Aplatit les objets imbriqués
      4️⃣ Génère un CSV propre

      Exemple :
      `{"user": {"name": "<PERSON>", "age": 30}}`
      → `user.name, user.age`

      Testez maintenant 👉 https://neethdseven.github.io/api2csv/
    platforms: ["twitter", "devto"]
    schedule: "recurring"
    frequency: "weekly"
    tags: ["json", "csv", "webdev", "tips"]
    
  # Personal branding avec focus API2CSV
  - id: "api2csv_creation_story"
    title: "Comment j'ai créé API2CSV"
    body: |
      🚀 L'histoire derrière API2CSV

      **Le problème** : Convertir des JSON d'API en CSV pour Excel
      **Les solutions existantes** : Upload sur serveur = risque sécurité
      **Ma solution** : Traitement 100% local dans le navigateur

      **Stack choisie** :
      ✅ JavaScript vanilla (simplicité)
      ✅ PapaParse (performance CSV)
      ✅ GitHub Pages (hébergement gratuit)

      **Résultat** : Outil gratuit, sécurisé et open source

      👉 https://neethdseven.github.io/api2csv/

      Et vous, quel outil avez-vous créé récemment ?

      #webdev #opensource #json #csv #javascript
    platforms: ["devto"]
    schedule: "2025-07-02 16:00"
    tags: ["webdev", "opensource", "json", "csv"]
    
  # Engagement communauté
  - id: "open_source_benefits"
    title: "Pourquoi contribuer à l'open source ?"
    body: |
      🌟 5 raisons de contribuer à l'open source :
      
      1️⃣ Apprendre de vrais projets
      2️⃣ Construire un portfolio visible
      3️⃣ Rencontrer d'autres développeurs
      4️⃣ Améliorer ses compétences
      5️⃣ Aider la communauté
      
      Mon conseil : Commencez petit !
      - Corriger une typo dans la doc
      - Ajouter des tests
      - Proposer une petite feature
      
      Mes contributions récentes sur GitHub 👉 https://github.com/NeethDseven
      
      Et vous, quelle a été votre première contribution ?
    platforms: ["twitter", "reddit"]
    schedule: "recurring"
    frequency: "monthly"
    tags: ["opensource", "github", "community", "webdev"]
    subreddit: "opensource"
    
  # Tutoriel technique avec API2CSV
  - id: "api2csv_papaparse_integration"
    title: "Comment API2CSV utilise PapaParse"
    body: |
      🔧 Sous le capot d'API2CSV : PapaParse

      **Pourquoi PapaParse ?**
      ✅ Performance optimale
      ✅ Gestion des caractères spéciaux
      ✅ Support des gros fichiers
      ✅ Streaming pour la mémoire

      **Code simplifié d'API2CSV** :
      ```javascript
      // Conversion JSON → CSV
      const csv = Papa.unparse(jsonData, {
        header: true,
        delimiter: ",",
        quotes: true
      });

      // Téléchargement automatique
      downloadCSV(csv, 'converted.csv');
      ```

      Testez le résultat 👉 https://neethdseven.github.io/api2csv/

      #javascript #csv #papaparse #api2csv #webdev
    platforms: ["devto", "reddit"]
    schedule: "2025-07-03 14:00"
    tags: ["javascript", "csv", "papaparse", "webdev"]

    
  # Veille technologique
  - id: "web_trends_2025"
    title: "Tendances web dev 2025"
    body: |
      🔮 Tendances web dev à surveiller en 2025 :
      
      🚀 **Frameworks**
      - Astro pour les sites statiques
      - SvelteKit en croissance
      - Next.js toujours dominant
      
      🎨 **CSS**
      - Container queries mainstream
      - CSS Grid subgrid
      - Nouvelles unités (dvh, lvh)
      
      ⚡ **Performance**
      - Edge computing
      - Streaming SSR
      - Web Assembly plus accessible
      
      🛠️ **Outils**
      - Vite partout
      - TypeScript par défaut
      - AI-assisted coding
      
      Qu'est-ce qui vous excite le plus ?
      
      #webdev #trends2025 #javascript #css #performance
    platforms: ["twitter", "devto"]
    schedule: "2025-07-04 11:00"
    tags: ["webdev", "trends", "javascript", "css", "performance"]
