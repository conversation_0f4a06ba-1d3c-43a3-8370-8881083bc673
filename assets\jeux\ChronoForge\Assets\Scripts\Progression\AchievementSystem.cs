using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using ChronoForge.UI;
using ChronoForge.Audio;

namespace ChronoForge.Progression
{
    /// <summary>
    /// Système d'achievements pour ChronoForge
    /// </summary>
    public class AchievementSystem : MonoBehaviour
    {
        [Header("Achievement Settings")]
        public List<Achievement> allAchievements = new List<Achievement>();
        public bool showHiddenAchievements = false;
        
        [Header("Notification Settings")]
        public float achievementDisplayDuration = 5f;
        public AudioClip achievementUnlockSound;
        
        // Events
        public static System.Action<Achievement> OnAchievementUnlocked;
        public static System.Action<Achievement, int> OnAchievementProgress;
        
        // Components
        private ProgressionManager progressionManager;
        private UIManager uiManager;
        private AudioManager audioManager;
        
        // Achievement tracking
        private Dictionary<string, Achievement> achievementLookup = new Dictionary<string, Achievement>();
        private List<Achievement> unlockedAchievements = new List<Achievement>();
        
        // Singleton
        public static AchievementSystem Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAchievementSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            FindComponents();
            LoadAchievementData();
            SubscribeToEvents();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeAchievementSystem()
        {
            // Create default achievements if none exist
            if (allAchievements.Count == 0)
            {
                CreateDefaultAchievements();
            }
            
            // Build achievement lookup
            BuildAchievementLookup();
            
            Debug.Log("🏆 AchievementSystem initialized");
        }
        
        private void CreateDefaultAchievements()
        {
            // Combat achievements
            allAchievements.Add(new Achievement
            {
                id = "first_kill",
                name = "First Blood",
                description = "Kill your first enemy",
                category = AchievementCategory.Combat,
                condition = AchievementCondition.KillEnemies,
                targetValue = 1,
                metaCurrencyReward = 10,
                rarity = AchievementRarity.Common
            });
            
            allAchievements.Add(new Achievement
            {
                id = "enemy_slayer",
                name = "Enemy Slayer",
                description = "Kill 100 enemies",
                category = AchievementCategory.Combat,
                condition = AchievementCondition.KillEnemies,
                targetValue = 100,
                metaCurrencyReward = 50,
                rarity = AchievementRarity.Uncommon
            });
            
            allAchievements.Add(new Achievement
            {
                id = "genocide",
                name = "Genocide",
                description = "Kill 1000 enemies",
                category = AchievementCategory.Combat,
                condition = AchievementCondition.KillEnemies,
                targetValue = 1000,
                metaCurrencyReward = 200,
                rarity = AchievementRarity.Rare
            });
            
            // Progression achievements
            allAchievements.Add(new Achievement
            {
                id = "level_up",
                name = "Growing Stronger",
                description = "Reach level 5",
                category = AchievementCategory.Progression,
                condition = AchievementCondition.ReachLevel,
                targetValue = 5,
                metaCurrencyReward = 25,
                unlockContentId = "techno_mage_class",
                rarity = AchievementRarity.Common
            });
            
            allAchievements.Add(new Achievement
            {
                id = "veteran",
                name = "Veteran",
                description = "Reach level 25",
                category = AchievementCategory.Progression,
                condition = AchievementCondition.ReachLevel,
                targetValue = 25,
                metaCurrencyReward = 100,
                rarity = AchievementRarity.Epic
            });
            
            // Run completion achievements
            allAchievements.Add(new Achievement
            {
                id = "first_run",
                name = "First Steps",
                description = "Complete your first run",
                category = AchievementCategory.Progression,
                condition = AchievementCondition.CompleteRuns,
                targetValue = 1,
                metaCurrencyReward = 50,
                rarity = AchievementRarity.Common
            });
            
            allAchievements.Add(new Achievement
            {
                id = "run_master",
                name = "Run Master",
                description = "Complete 10 runs",
                category = AchievementCategory.Progression,
                condition = AchievementCondition.CompleteRuns,
                targetValue = 10,
                metaCurrencyReward = 150,
                unlockContentId = "hardcore_mode",
                rarity = AchievementRarity.Rare
            });
            
            // Collection achievements
            allAchievements.Add(new Achievement
            {
                id = "collector",
                name = "Collector",
                description = "Find 50 different items",
                category = AchievementCategory.Collection,
                condition = AchievementCondition.CollectItems,
                targetValue = 50,
                metaCurrencyReward = 75,
                rarity = AchievementRarity.Uncommon
            });
            
            // Challenge achievements
            allAchievements.Add(new Achievement
            {
                id = "speed_runner",
                name = "Speed Runner",
                description = "Complete a run in under 30 minutes",
                category = AchievementCategory.Challenge,
                condition = AchievementCondition.Custom,
                specificRequirement = "complete_run_under_30_minutes",
                metaCurrencyReward = 100,
                rarity = AchievementRarity.Epic
            });
            
            // Hidden achievements
            allAchievements.Add(new Achievement
            {
                id = "secret_discovery",
                name = "???",
                description = "Discover a hidden secret",
                category = AchievementCategory.Hidden,
                condition = AchievementCondition.Custom,
                specificRequirement = "find_secret_room",
                metaCurrencyReward = 200,
                isHidden = true,
                rarity = AchievementRarity.Legendary
            });
        }
        
        private void BuildAchievementLookup()
        {
            achievementLookup.Clear();
            
            foreach (var achievement in allAchievements)
            {
                if (!achievementLookup.ContainsKey(achievement.id))
                {
                    achievementLookup[achievement.id] = achievement;
                }
                else
                {
                    Debug.LogWarning($"Duplicate achievement ID: {achievement.id}");
                }
            }
        }
        
        private void FindComponents()
        {
            progressionManager = ProgressionManager.Instance;
            uiManager = FindObjectOfType<UIManager>();
            audioManager = AudioManager.Instance;
        }
        
        #endregion
        
        #region Event Subscription
        
        private void SubscribeToEvents()
        {
            // Subscribe to progression events
            if (progressionManager != null)
            {
                ProgressionManager.OnLevelUp += HandleLevelUp;
                ProgressionManager.OnMetaCurrencyChanged += HandleCurrencyChanged;
            }
            
            // Subscribe to game events
            GameManager.OnRunCompleted += HandleRunCompleted;
            GameManager.OnEnemyKilled += HandleEnemyKilled;
            GameManager.OnItemCollected += HandleItemCollected;
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            ProgressionManager.OnLevelUp -= HandleLevelUp;
            ProgressionManager.OnMetaCurrencyChanged -= HandleCurrencyChanged;
            GameManager.OnRunCompleted -= HandleRunCompleted;
            GameManager.OnEnemyKilled -= HandleEnemyKilled;
            GameManager.OnItemCollected -= HandleItemCollected;
        }
        
        #endregion
        
        #region Achievement Progress Tracking
        
        public void UpdateAchievementProgress(string achievementId, int newProgress)
        {
            if (!achievementLookup.ContainsKey(achievementId)) return;
            
            Achievement achievement = achievementLookup[achievementId];
            
            if (achievement.isUnlocked) return;
            
            int oldProgress = achievement.currentProgress;
            bool wasUnlocked = achievement.UpdateProgress(newProgress);
            
            if (achievement.currentProgress != oldProgress)
            {
                OnAchievementProgress?.Invoke(achievement, achievement.currentProgress);
            }
            
            if (wasUnlocked)
            {
                UnlockAchievement(achievement);
            }
        }
        
        public void IncrementAchievementProgress(string achievementId, int increment = 1)
        {
            if (!achievementLookup.ContainsKey(achievementId)) return;
            
            Achievement achievement = achievementLookup[achievementId];
            UpdateAchievementProgress(achievementId, achievement.currentProgress + increment);
        }
        
        private void UnlockAchievement(Achievement achievement)
        {
            if (unlockedAchievements.Contains(achievement)) return;
            
            unlockedAchievements.Add(achievement);
            
            // Award meta currency
            if (achievement.metaCurrencyReward > 0 && progressionManager != null)
            {
                progressionManager.AddMetaCurrency(achievement.metaCurrencyReward);
            }
            
            // Unlock content if specified
            if (!string.IsNullOrEmpty(achievement.unlockContentId) && progressionManager != null)
            {
                // This would trigger content unlock in ProgressionManager
                Debug.Log($"🔓 Achievement unlocked content: {achievement.unlockContentId}");
            }
            
            // Show notification
            ShowAchievementNotification(achievement);
            
            // Play sound
            PlayAchievementSound();
            
            // Save progress
            SaveAchievementData();
            
            OnAchievementUnlocked?.Invoke(achievement);
            
            Debug.Log($"🏆 Achievement unlocked: {achievement.name}");
        }
        
        #endregion
        
        #region Event Handlers
        
        private void HandleLevelUp(int newLevel)
        {
            UpdateAchievementProgress("level_up", newLevel);
            UpdateAchievementProgress("veteran", newLevel);
        }
        
        private void HandleCurrencyChanged(int newAmount)
        {
            // Could track currency spending achievements here
        }
        
        private void HandleRunCompleted()
        {
            IncrementAchievementProgress("first_run");
            IncrementAchievementProgress("run_master");
        }
        
        private void HandleEnemyKilled()
        {
            IncrementAchievementProgress("first_kill");
            IncrementAchievementProgress("enemy_slayer");
            IncrementAchievementProgress("genocide");
        }
        
        private void HandleItemCollected(string itemId)
        {
            IncrementAchievementProgress("collector");
        }
        
        #endregion
        
        #region Custom Achievement Conditions
        
        public void TriggerCustomAchievement(string customCondition)
        {
            foreach (var achievement in allAchievements)
            {
                if (achievement.condition == AchievementCondition.Custom && 
                    achievement.specificRequirement == customCondition)
                {
                    UpdateAchievementProgress(achievement.id, achievement.targetValue);
                }
            }
        }
        
        public void CheckSpeedRunAchievement(float runTime)
        {
            if (runTime < 1800f) // 30 minutes
            {
                TriggerCustomAchievement("complete_run_under_30_minutes");
            }
        }
        
        public void CheckSecretDiscovery()
        {
            TriggerCustomAchievement("find_secret_room");
        }
        
        #endregion
        
        #region UI Integration
        
        private void ShowAchievementNotification(Achievement achievement)
        {
            if (uiManager != null)
            {
                string title = achievement.isHidden ? "Secret Achievement Unlocked!" : "Achievement Unlocked!";
                string description = $"{achievement.name}\n{achievement.description}";
                
                if (achievement.metaCurrencyReward > 0)
                {
                    description += $"\n+{achievement.metaCurrencyReward} Meta Currency";
                }
                
                uiManager.ShowAchievement(title, description, achievement.icon);
            }
        }
        
        private void PlayAchievementSound()
        {
            if (audioManager != null)
            {
                if (achievementUnlockSound != null)
                {
                    audioManager.PlaySFX("achievement_unlock");
                }
                else
                {
                    audioManager.PlayUI("achievement_unlock");
                }
            }
        }
        
        #endregion
        
        #region Data Persistence
        
        private void SaveAchievementData()
        {
            AchievementSaveData saveData = new AchievementSaveData();
            
            foreach (var achievement in allAchievements)
            {
                saveData.achievementProgress[achievement.id] = new AchievementProgressData
                {
                    currentProgress = achievement.currentProgress,
                    isUnlocked = achievement.isUnlocked
                };
            }
            
            string json = JsonUtility.ToJson(saveData, true);
            PlayerPrefs.SetString("ChronoForge_Achievements", json);
            PlayerPrefs.Save();
        }
        
        private void LoadAchievementData()
        {
            if (PlayerPrefs.HasKey("ChronoForge_Achievements"))
            {
                string json = PlayerPrefs.GetString("ChronoForge_Achievements");
                AchievementSaveData saveData = JsonUtility.FromJson<AchievementSaveData>(json);
                
                foreach (var achievement in allAchievements)
                {
                    if (saveData.achievementProgress.ContainsKey(achievement.id))
                    {
                        var progressData = saveData.achievementProgress[achievement.id];
                        achievement.currentProgress = progressData.currentProgress;
                        achievement.isUnlocked = progressData.isUnlocked;
                        
                        if (achievement.isUnlocked && !unlockedAchievements.Contains(achievement))
                        {
                            unlockedAchievements.Add(achievement);
                        }
                    }
                }
            }
            
            Debug.Log($"🏆 Loaded {unlockedAchievements.Count} unlocked achievements");
        }
        
        #endregion
        
        #region Public Methods
        
        public List<Achievement> GetAllAchievements()
        {
            if (showHiddenAchievements)
            {
                return allAchievements;
            }
            else
            {
                return allAchievements.Where(a => !a.isHidden || a.isUnlocked).ToList();
            }
        }
        
        public List<Achievement> GetUnlockedAchievements()
        {
            return unlockedAchievements;
        }
        
        public List<Achievement> GetAchievementsByCategory(AchievementCategory category)
        {
            return GetAllAchievements().Where(a => a.category == category).ToList();
        }
        
        public Achievement GetAchievement(string achievementId)
        {
            return achievementLookup.ContainsKey(achievementId) ? achievementLookup[achievementId] : null;
        }
        
        public bool IsAchievementUnlocked(string achievementId)
        {
            Achievement achievement = GetAchievement(achievementId);
            return achievement != null && achievement.isUnlocked;
        }
        
        public float GetAchievementProgress(string achievementId)
        {
            Achievement achievement = GetAchievement(achievementId);
            return achievement?.GetProgressPercentage() ?? 0f;
        }
        
        public int GetUnlockedAchievementCount()
        {
            return unlockedAchievements.Count;
        }
        
        public int GetTotalAchievementCount()
        {
            return allAchievements.Count(a => !a.isHidden || a.isUnlocked);
        }
        
        public float GetCompletionPercentage()
        {
            int total = GetTotalAchievementCount();
            return total > 0 ? (float)GetUnlockedAchievementCount() / total : 0f;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Données de sauvegarde des achievements
    /// </summary>
    [System.Serializable]
    public class AchievementSaveData
    {
        public SerializableDictionary<string, AchievementProgressData> achievementProgress = 
            new SerializableDictionary<string, AchievementProgressData>();
    }
    
    /// <summary>
    /// Données de progression d'un achievement
    /// </summary>
    [System.Serializable]
    public class AchievementProgressData
    {
        public int currentProgress;
        public bool isUnlocked;
    }
}
