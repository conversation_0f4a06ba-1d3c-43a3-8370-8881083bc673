using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using System.Numerics;
using SpaceClicker.Core;

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Tests unitaires pour ResourceManager
    /// </summary>
    public class ResourceManagerTests
    {
        private GameObject testObject;
        private ResourceManager resourceManager;
        
        [SetUp]
        public void Setup()
        {
            // Créer un GameObject de test avec ResourceManager
            testObject = new GameObject("TestResourceManager");
            resourceManager = testObject.AddComponent<ResourceManager>();
        }
        
        [TearDown]
        public void TearDown()
        {
            // Nettoyer après chaque test
            if (testObject != null)
            {
                Object.DestroyImmediate(testObject);
            }
        }
        
        [Test]
        public void ResourceManager_InitialResources_ShouldBeZero()
        {
            // Arrange & Act
            BigInteger energy = resourceManager.GetResource(ResourceType.Energy);
            BigInteger minerals = resourceManager.GetResource(ResourceType.Minerals);
            BigInteger research = resourceManager.GetResource(ResourceType.ResearchData);
            BigInteger currency = resourceManager.GetResource(ResourceType.SpaceCurrency);
            
            // Assert
            Assert.AreEqual(BigInteger.Zero, energy, "Energy should start at 0");
            Assert.AreEqual(BigInteger.Zero, minerals, "Minerals should start at 0");
            Assert.AreEqual(BigInteger.Zero, research, "Research should start at 0");
            Assert.AreEqual(BigInteger.Zero, currency, "Currency should start at 0");
        }
        
        [Test]
        public void AddResource_ValidAmount_ShouldIncreaseResource()
        {
            // Arrange
            BigInteger initialAmount = new BigInteger(100);
            BigInteger addAmount = new BigInteger(50);
            
            // Act
            resourceManager.AddResource(ResourceType.Energy, initialAmount);
            resourceManager.AddResource(ResourceType.Energy, addAmount);
            
            // Assert
            BigInteger result = resourceManager.GetResource(ResourceType.Energy);
            Assert.AreEqual(new BigInteger(150), result, "Energy should be 150 after adding 100 + 50");
        }
        
        [Test]
        public void SpendResource_SufficientAmount_ShouldReturnTrueAndDecrease()
        {
            // Arrange
            BigInteger initialAmount = new BigInteger(100);
            BigInteger spendAmount = new BigInteger(30);
            resourceManager.AddResource(ResourceType.Minerals, initialAmount);
            
            // Act
            bool result = resourceManager.SpendResource(ResourceType.Minerals, spendAmount);
            
            // Assert
            Assert.IsTrue(result, "Should be able to spend 30 minerals");
            BigInteger remaining = resourceManager.GetResource(ResourceType.Minerals);
            Assert.AreEqual(new BigInteger(70), remaining, "Should have 70 minerals remaining");
        }
        
        [Test]
        public void SpendResource_InsufficientAmount_ShouldReturnFalseAndNotChange()
        {
            // Arrange
            BigInteger initialAmount = new BigInteger(50);
            BigInteger spendAmount = new BigInteger(100);
            resourceManager.AddResource(ResourceType.ResearchData, initialAmount);
            
            // Act
            bool result = resourceManager.SpendResource(ResourceType.ResearchData, spendAmount);
            
            // Assert
            Assert.IsFalse(result, "Should not be able to spend 100 research when only having 50");
            BigInteger remaining = resourceManager.GetResource(ResourceType.ResearchData);
            Assert.AreEqual(initialAmount, remaining, "Research amount should remain unchanged");
        }
        
        [Test]
        public void CanAfford_SufficientAmount_ShouldReturnTrue()
        {
            // Arrange
            BigInteger amount = new BigInteger(75);
            BigInteger checkAmount = new BigInteger(50);
            resourceManager.AddResource(ResourceType.SpaceCurrency, amount);
            
            // Act
            bool result = resourceManager.CanAfford(ResourceType.SpaceCurrency, checkAmount);
            
            // Assert
            Assert.IsTrue(result, "Should be able to afford 50 currency when having 75");
        }
        
        [Test]
        public void CanAfford_InsufficientAmount_ShouldReturnFalse()
        {
            // Arrange
            BigInteger amount = new BigInteger(25);
            BigInteger checkAmount = new BigInteger(50);
            resourceManager.AddResource(ResourceType.SpaceCurrency, amount);
            
            // Act
            bool result = resourceManager.CanAfford(ResourceType.SpaceCurrency, checkAmount);
            
            // Assert
            Assert.IsFalse(result, "Should not be able to afford 50 currency when having only 25");
        }
        
        [Test]
        public void GetProductionRate_InitialState_ShouldBeZero()
        {
            // Act
            float energyRate = resourceManager.GetProductionRate(ResourceType.Energy);
            float mineralsRate = resourceManager.GetProductionRate(ResourceType.Minerals);
            
            // Assert
            Assert.AreEqual(0f, energyRate, "Initial energy production rate should be 0");
            Assert.AreEqual(0f, mineralsRate, "Initial minerals production rate should be 0");
        }
        
        [Test]
        public void SetProductionRate_ValidRate_ShouldUpdateRate()
        {
            // Arrange
            float newRate = 5.5f;
            
            // Act
            resourceManager.SetProductionRate(ResourceType.Energy, newRate);
            
            // Assert
            float result = resourceManager.GetProductionRate(ResourceType.Energy);
            Assert.AreEqual(newRate, result, 0.01f, "Production rate should be updated to 5.5");
        }
        
        [Test]
        public void AddResource_LargeNumbers_ShouldHandleCorrectly()
        {
            // Arrange
            BigInteger largeNumber1 = BigInteger.Parse("999999999999999999999");
            BigInteger largeNumber2 = BigInteger.Parse("111111111111111111111");
            BigInteger expected = BigInteger.Parse("1111111111111111111110");
            
            // Act
            resourceManager.AddResource(ResourceType.Energy, largeNumber1);
            resourceManager.AddResource(ResourceType.Energy, largeNumber2);
            
            // Assert
            BigInteger result = resourceManager.GetResource(ResourceType.Energy);
            Assert.AreEqual(expected, result, "Should handle very large numbers correctly");
        }
        
        [Test]
        public void ResetResources_ShouldSetAllToZero()
        {
            // Arrange
            resourceManager.AddResource(ResourceType.Energy, new BigInteger(100));
            resourceManager.AddResource(ResourceType.Minerals, new BigInteger(200));
            resourceManager.AddResource(ResourceType.ResearchData, new BigInteger(300));
            resourceManager.AddResource(ResourceType.SpaceCurrency, new BigInteger(400));
            
            // Act
            resourceManager.ResetResources();
            
            // Assert
            Assert.AreEqual(BigInteger.Zero, resourceManager.GetResource(ResourceType.Energy));
            Assert.AreEqual(BigInteger.Zero, resourceManager.GetResource(ResourceType.Minerals));
            Assert.AreEqual(BigInteger.Zero, resourceManager.GetResource(ResourceType.ResearchData));
            Assert.AreEqual(BigInteger.Zero, resourceManager.GetResource(ResourceType.SpaceCurrency));
        }
        
        [UnityTest]
        public IEnumerator ResourceProduction_OverTime_ShouldIncreaseResources()
        {
            // Arrange
            float productionRate = 10f; // 10 per second
            resourceManager.SetProductionRate(ResourceType.Energy, productionRate);
            BigInteger initialAmount = resourceManager.GetResource(ResourceType.Energy);
            
            // Act - Wait for 1 second
            yield return new WaitForSeconds(1f);
            
            // Assert
            BigInteger finalAmount = resourceManager.GetResource(ResourceType.Energy);
            BigInteger produced = finalAmount - initialAmount;
            
            // Allow some tolerance for timing
            Assert.IsTrue(produced >= new BigInteger(8), "Should produce at least 8 energy in 1 second");
            Assert.IsTrue(produced <= new BigInteger(12), "Should produce at most 12 energy in 1 second");
        }
        
        [Test]
        public void OnResourceClicked_ValidResourceType_ShouldTriggerEvent()
        {
            // Arrange
            bool eventTriggered = false;
            ResourceType triggeredType = ResourceType.Energy;
            BigInteger triggeredAmount = BigInteger.Zero;
            
            ResourceManager.OnResourceGained += (type, amount) => {
                eventTriggered = true;
                triggeredType = type;
                triggeredAmount = amount;
            };
            
            // Act
            resourceManager.OnResourceClicked(ResourceType.Minerals);
            
            // Assert
            Assert.IsTrue(eventTriggered, "Resource gained event should be triggered");
            Assert.AreEqual(ResourceType.Minerals, triggeredType, "Event should contain correct resource type");
            Assert.IsTrue(triggeredAmount > BigInteger.Zero, "Event should contain positive amount");
            
            // Cleanup
            ResourceManager.OnResourceGained = null;
        }
    }
}
