using NUnit.Framework;
using UnityEngine;
using System.Collections.Generic;
using System.Numerics;
using SpaceClicker.Core;

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Tests unitaires pour UpgradeManager
    /// </summary>
    public class UpgradeManagerTests
    {
        private GameObject testObject;
        private UpgradeManager upgradeManager;
        private ResourceManager resourceManager;
        
        [SetUp]
        public void Setup()
        {
            // Créer les GameObjects de test
            testObject = new GameObject("TestUpgradeManager");
            upgradeManager = testObject.AddComponent<UpgradeManager>();
            
            GameObject resourceObject = new GameObject("TestResourceManager");
            resourceManager = resourceObject.AddComponent<ResourceManager>();
            
            // Simuler la référence
            var resourceField = typeof(UpgradeManager).GetField("resourceManager", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            resourceField?.SetValue(upgradeManager, resourceManager);
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testObject != null)
                Object.DestroyImmediate(testObject);
            
            if (resourceManager != null)
                Object.DestroyImmediate(resourceManager.gameObject);
        }
        
        [Test]
        public void GetAvailableUpgrades_InitialState_ShouldReturnUpgrades()
        {
            // Act
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            
            // Assert
            Assert.IsNotNull(upgrades, "Available upgrades should not be null");
            Assert.IsTrue(upgrades.Count > 0, "Should have at least one upgrade available");
        }
        
        [Test]
        public void GetUpgradeById_ValidId_ShouldReturnUpgrade()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            string testId = upgrades[0].id;
            
            // Act
            UpgradeData result = upgradeManager.GetUpgradeById(testId);
            
            // Assert
            Assert.IsNotNull(result, "Should return upgrade for valid ID");
            Assert.AreEqual(testId, result.id, "Returned upgrade should have correct ID");
        }
        
        [Test]
        public void GetUpgradeById_InvalidId_ShouldReturnNull()
        {
            // Act
            UpgradeData result = upgradeManager.GetUpgradeById("invalid_id_12345");
            
            // Assert
            Assert.IsNull(result, "Should return null for invalid ID");
        }
        
        [Test]
        public void CanPurchaseUpgrade_SufficientResources_ShouldReturnTrue()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            // Give enough resources
            BigInteger cost = testUpgrade.GetNextLevelCost();
            resourceManager.AddResource(testUpgrade.costType, cost);
            
            // Act
            bool result = upgradeManager.CanPurchaseUpgrade(testUpgrade.id);
            
            // Assert
            Assert.IsTrue(result, "Should be able to purchase upgrade with sufficient resources");
        }
        
        [Test]
        public void CanPurchaseUpgrade_InsufficientResources_ShouldReturnFalse()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            // Don't give enough resources (start with 0)
            
            // Act
            bool result = upgradeManager.CanPurchaseUpgrade(testUpgrade.id);
            
            // Assert
            Assert.IsFalse(result, "Should not be able to purchase upgrade without sufficient resources");
        }
        
        [Test]
        public void CanPurchaseUpgrade_MaxLevel_ShouldReturnFalse()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            // Set upgrade to max level
            testUpgrade.currentLevel = testUpgrade.maxLevel;
            
            // Give enough resources
            BigInteger largeCost = new BigInteger(999999);
            resourceManager.AddResource(testUpgrade.costType, largeCost);
            
            // Act
            bool result = upgradeManager.CanPurchaseUpgrade(testUpgrade.id);
            
            // Assert
            Assert.IsFalse(result, "Should not be able to purchase upgrade at max level");
        }
        
        [Test]
        public void PurchaseUpgrade_ValidPurchase_ShouldReturnTrueAndUpgrade()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            int initialLevel = testUpgrade.currentLevel;
            
            // Give enough resources
            BigInteger cost = testUpgrade.GetNextLevelCost();
            resourceManager.AddResource(testUpgrade.costType, cost * 2); // Give extra
            
            BigInteger initialResources = resourceManager.GetResource(testUpgrade.costType);
            
            // Act
            bool result = upgradeManager.PurchaseUpgrade(testUpgrade.id);
            
            // Assert
            Assert.IsTrue(result, "Purchase should succeed");
            Assert.AreEqual(initialLevel + 1, testUpgrade.currentLevel, "Upgrade level should increase");
            
            BigInteger finalResources = resourceManager.GetResource(testUpgrade.costType);
            Assert.AreEqual(initialResources - cost, finalResources, "Resources should be spent");
        }
        
        [Test]
        public void PurchaseUpgrade_InsufficientResources_ShouldReturnFalseAndNotUpgrade()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            int initialLevel = testUpgrade.currentLevel;
            
            // Don't give enough resources
            BigInteger initialResources = resourceManager.GetResource(testUpgrade.costType);
            
            // Act
            bool result = upgradeManager.PurchaseUpgrade(testUpgrade.id);
            
            // Assert
            Assert.IsFalse(result, "Purchase should fail");
            Assert.AreEqual(initialLevel, testUpgrade.currentLevel, "Upgrade level should not change");
            
            BigInteger finalResources = resourceManager.GetResource(testUpgrade.costType);
            Assert.AreEqual(initialResources, finalResources, "Resources should not be spent");
        }
        
        [Test]
        public void GetProductionMultiplier_NoUpgrades_ShouldReturnOne()
        {
            // Act
            float multiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
            
            // Assert
            Assert.AreEqual(1f, multiplier, 0.01f, "Initial production multiplier should be 1.0");
        }
        
        [Test]
        public void GetProductionMultiplier_WithUpgrades_ShouldReturnHigherValue()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData energyUpgrade = upgrades.Find(u => u.type == UpgradeType.SolarPanelEfficiency);
            
            if (energyUpgrade != null)
            {
                // Give resources and purchase upgrade
                BigInteger cost = energyUpgrade.GetNextLevelCost();
                resourceManager.AddResource(energyUpgrade.costType, cost);
                upgradeManager.PurchaseUpgrade(energyUpgrade.id);
                
                // Act
                float multiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
                
                // Assert
                Assert.IsTrue(multiplier > 1f, "Production multiplier should be greater than 1 after upgrade");
            }
            else
            {
                Assert.Inconclusive("No energy upgrade found for testing");
            }
        }
        
        [Test]
        public void ResetUpgrades_ShouldResetAllUpgradesToInitialState()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            
            // Purchase some upgrades
            foreach (var upgrade in upgrades)
            {
                BigInteger cost = upgrade.GetNextLevelCost();
                resourceManager.AddResource(upgrade.costType, cost);
                upgradeManager.PurchaseUpgrade(upgrade.id);
            }
            
            // Act
            upgradeManager.ResetUpgrades();
            
            // Assert
            foreach (var upgrade in upgrades)
            {
                Assert.AreEqual(0, upgrade.currentLevel, $"Upgrade {upgrade.id} should be reset to level 0");
            }
        }
        
        [Test]
        public void UpgradeData_GetNextLevelCost_ShouldIncreaseWithLevel()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            BigInteger initialCost = testUpgrade.GetNextLevelCost();
            
            // Simulate level increase
            testUpgrade.currentLevel++;
            BigInteger nextCost = testUpgrade.GetNextLevelCost();
            
            // Assert
            Assert.IsTrue(nextCost > initialCost, "Cost should increase with level");
            
            // Reset for cleanup
            testUpgrade.currentLevel--;
        }
        
        [Test]
        public void UpgradeData_GetEffectValue_ShouldIncreaseWithLevel()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            float initialEffect = testUpgrade.GetEffectValue();
            
            // Simulate level increase
            testUpgrade.currentLevel++;
            float nextEffect = testUpgrade.GetEffectValue();
            
            // Assert
            Assert.IsTrue(nextEffect > initialEffect, "Effect should increase with level");
            
            // Reset for cleanup
            testUpgrade.currentLevel--;
        }
        
        [Test]
        public void UpgradeData_IsMaxLevel_ShouldReturnCorrectValue()
        {
            // Arrange
            List<UpgradeData> upgrades = upgradeManager.GetAvailableUpgrades();
            UpgradeData testUpgrade = upgrades[0];
            
            // Test not at max level
            testUpgrade.currentLevel = 0;
            Assert.IsFalse(testUpgrade.IsMaxLevel(), "Should not be at max level when current level is 0");
            
            // Test at max level
            testUpgrade.currentLevel = testUpgrade.maxLevel;
            Assert.IsTrue(testUpgrade.IsMaxLevel(), "Should be at max level when current level equals max level");
            
            // Reset for cleanup
            testUpgrade.currentLevel = 0;
        }
    }
}
