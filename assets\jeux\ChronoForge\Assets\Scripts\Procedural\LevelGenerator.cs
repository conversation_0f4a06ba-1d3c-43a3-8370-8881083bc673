using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using ChronoForge.Core;

namespace ChronoForge.Procedural
{
    /// <summary>
    /// Générateur de niveaux procéduraux pour ChronoForge
    /// </summary>
    public class LevelGenerator : MonoBehaviour
    {
        [Header("Generation Settings")]
        public int roomCount = 15;
        public Vector2Int gridSize = new Vector2Int(5, 5);
        public float roomSize = 20f;
        public int minPathLength = 8;
        
        [Header("Room Templates")]
        public List<RoomTemplate> roomTemplates = new List<RoomTemplate>();
        public RoomTemplate startRoomTemplate;
        public RoomTemplate bossRoomTemplate;
        public List<RoomTemplate> specialRoomTemplates = new List<RoomTemplate>();
        
        [Header("Biome Settings")]
        public BiomeData currentBiome;
        public List<BiomeData> availableBiomes = new List<BiomeData>();
        
        [Header("Debug")]
        public bool showDebugGizmos = true;
        public bool generateOnStart = false;
        
        // Events
        public static System.Action<LevelData> OnLevelGenerated;
        public static System.Action<RoomData> OnRoomEntered;
        public static System.Action<RoomData> OnRoomCleared;
        
        // Generated level data
        private LevelData currentLevel;
        private Dictionary<Vector2Int, RoomData> roomGrid = new Dictionary<Vector2Int, RoomData>();
        private List<Vector2Int> mainPath = new List<Vector2Int>();
        private List<Vector2Int> sidePaths = new List<Vector2Int>();
        
        // Current state
        private Vector2Int currentRoomPosition;
        private RoomData currentRoom;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeLevelGenerator();
        }
        
        private void Start()
        {
            if (generateOnStart)
            {
                GenerateLevel();
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeLevelGenerator()
        {
            // Initialize default biome if none set
            if (currentBiome == null && availableBiomes.Count > 0)
            {
                currentBiome = availableBiomes[0];
            }
            
            // Initialize room templates if empty
            if (roomTemplates.Count == 0)
            {
                CreateDefaultRoomTemplates();
            }
            
            Debug.Log("🏗️ LevelGenerator initialized");
        }
        
        private void CreateDefaultRoomTemplates()
        {
            // Create basic room templates
            roomTemplates.Add(CreateRoomTemplate(RoomType.Combat, "Combat Room"));
            roomTemplates.Add(CreateRoomTemplate(RoomType.Treasure, "Treasure Room"));
            roomTemplates.Add(CreateRoomTemplate(RoomType.Shop, "Shop Room"));
            roomTemplates.Add(CreateRoomTemplate(RoomType.Rest, "Rest Room"));
            
            if (startRoomTemplate == null)
                startRoomTemplate = CreateRoomTemplate(RoomType.Start, "Start Room");
            
            if (bossRoomTemplate == null)
                bossRoomTemplate = CreateRoomTemplate(RoomType.Boss, "Boss Room");
        }
        
        private RoomTemplate CreateRoomTemplate(RoomType type, string name)
        {
            RoomTemplate template = ScriptableObject.CreateInstance<RoomTemplate>();
            template.roomType = type;
            template.roomName = name;
            template.weight = 1f;
            return template;
        }
        
        #endregion
        
        #region Level Generation
        
        public void GenerateLevel(int seed = -1)
        {
            if (seed == -1)
                seed = Random.Range(0, int.MaxValue);
            
            Random.InitState(seed);
            
            Debug.Log($"🌍 Generating level with seed: {seed}");
            
            // Clear previous data
            ClearLevel();
            
            // Create new level data
            currentLevel = new LevelData
            {
                seed = seed,
                biome = currentBiome,
                roomCount = roomCount,
                gridSize = gridSize
            };
            
            // Generate level structure
            GenerateLevelStructure();
            
            // Place rooms
            PlaceRooms();
            
            // Generate room contents
            GenerateRoomContents();
            
            // Set starting position
            SetStartingPosition();
            
            // Notify generation complete
            OnLevelGenerated?.Invoke(currentLevel);
            
            Debug.Log($"✅ Level generated: {roomGrid.Count} rooms created");
        }
        
        private void ClearLevel()
        {
            roomGrid.Clear();
            mainPath.Clear();
            sidePaths.Clear();
            currentRoom = null;
            
            // Clear existing room objects
            foreach (Transform child in transform)
            {
                if (Application.isPlaying)
                    Destroy(child.gameObject);
                else
                    DestroyImmediate(child.gameObject);
            }
        }
        
        #endregion
        
        #region Structure Generation
        
        private void GenerateLevelStructure()
        {
            // Generate main path
            GenerateMainPath();
            
            // Generate side paths
            GenerateSidePaths();
            
            // Ensure minimum room count
            EnsureMinimumRooms();
        }
        
        private void GenerateMainPath()
        {
            mainPath.Clear();
            
            // Start at bottom center
            Vector2Int startPos = new Vector2Int(gridSize.x / 2, 0);
            Vector2Int currentPos = startPos;
            
            mainPath.Add(currentPos);
            
            // Generate path to top
            while (currentPos.y < gridSize.y - 1 && mainPath.Count < minPathLength)
            {
                List<Vector2Int> possibleMoves = GetPossibleMoves(currentPos);
                
                if (possibleMoves.Count == 0) break;
                
                // Prefer moving up, but allow some horizontal movement
                Vector2Int nextPos = ChooseNextPosition(currentPos, possibleMoves);
                
                if (!mainPath.Contains(nextPos))
                {
                    mainPath.Add(nextPos);
                    currentPos = nextPos;
                }
                else
                {
                    break; // Avoid loops in main path
                }
            }
            
            Debug.Log($"🛤️ Main path generated: {mainPath.Count} rooms");
        }
        
        private List<Vector2Int> GetPossibleMoves(Vector2Int from)
        {
            List<Vector2Int> moves = new List<Vector2Int>();
            
            Vector2Int[] directions = {
                Vector2Int.up,
                Vector2Int.right,
                Vector2Int.left,
                Vector2Int.down
            };
            
            foreach (var dir in directions)
            {
                Vector2Int newPos = from + dir;
                
                if (IsValidPosition(newPos))
                {
                    moves.Add(newPos);
                }
            }
            
            return moves;
        }
        
        private Vector2Int ChooseNextPosition(Vector2Int current, List<Vector2Int> possible)
        {
            // Prefer upward movement (70% chance)
            var upwardMoves = possible.Where(p => p.y > current.y).ToList();
            if (upwardMoves.Count > 0 && Random.value < 0.7f)
            {
                return upwardMoves[Random.Range(0, upwardMoves.Count)];
            }
            
            // Otherwise choose randomly
            return possible[Random.Range(0, possible.Count)];
        }
        
        private void GenerateSidePaths()
        {
            sidePaths.Clear();
            
            int sideRoomsToGenerate = Mathf.Max(0, roomCount - mainPath.Count);
            int attempts = 0;
            int maxAttempts = 100;
            
            while (sidePaths.Count < sideRoomsToGenerate && attempts < maxAttempts)
            {
                attempts++;
                
                // Choose a random position adjacent to main path
                Vector2Int mainPathPos = mainPath[Random.Range(0, mainPath.Count)];
                List<Vector2Int> adjacentPositions = GetPossibleMoves(mainPathPos);
                
                foreach (var pos in adjacentPositions)
                {
                    if (!mainPath.Contains(pos) && !sidePaths.Contains(pos))
                    {
                        sidePaths.Add(pos);
                        
                        // Small chance to extend side path
                        if (Random.value < 0.3f && sidePaths.Count < sideRoomsToGenerate)
                        {
                            ExtendSidePath(pos);
                        }
                        
                        break;
                    }
                }
            }
            
            Debug.Log($"🌿 Side paths generated: {sidePaths.Count} rooms");
        }
        
        private void ExtendSidePath(Vector2Int from)
        {
            List<Vector2Int> possibleExtensions = GetPossibleMoves(from);
            
            foreach (var pos in possibleExtensions)
            {
                if (!mainPath.Contains(pos) && !sidePaths.Contains(pos))
                {
                    sidePaths.Add(pos);
                    break; // Only extend by one room
                }
            }
        }
        
        private void EnsureMinimumRooms()
        {
            int totalRooms = mainPath.Count + sidePaths.Count;
            
            if (totalRooms < roomCount)
            {
                // Add more side rooms if needed
                int roomsNeeded = roomCount - totalRooms;
                
                for (int i = 0; i < roomsNeeded; i++)
                {
                    Vector2Int newPos = FindEmptyAdjacentPosition();
                    if (newPos != Vector2Int.one * -1) // Valid position found
                    {
                        sidePaths.Add(newPos);
                    }
                }
            }
        }
        
        private Vector2Int FindEmptyAdjacentPosition()
        {
            List<Vector2Int> allRooms = new List<Vector2Int>(mainPath);
            allRooms.AddRange(sidePaths);
            
            foreach (var roomPos in allRooms)
            {
                List<Vector2Int> adjacent = GetPossibleMoves(roomPos);
                
                foreach (var pos in adjacent)
                {
                    if (!allRooms.Contains(pos))
                    {
                        return pos;
                    }
                }
            }
            
            return Vector2Int.one * -1; // No valid position found
        }
        
        #endregion
        
        #region Room Placement
        
        private void PlaceRooms()
        {
            // Place start room
            Vector2Int startPos = mainPath[0];
            PlaceRoom(startPos, RoomType.Start);
            
            // Place boss room
            Vector2Int bossPos = mainPath[mainPath.Count - 1];
            PlaceRoom(bossPos, RoomType.Boss);
            
            // Place main path rooms (excluding start and boss)
            for (int i = 1; i < mainPath.Count - 1; i++)
            {
                RoomType roomType = ChooseMainPathRoomType();
                PlaceRoom(mainPath[i], roomType);
            }
            
            // Place side path rooms
            foreach (var pos in sidePaths)
            {
                RoomType roomType = ChooseSidePathRoomType();
                PlaceRoom(pos, roomType);
            }
        }
        
        private void PlaceRoom(Vector2Int gridPos, RoomType roomType)
        {
            RoomTemplate template = GetRoomTemplate(roomType);
            
            if (template == null)
            {
                Debug.LogWarning($"No template found for room type: {roomType}");
                return;
            }
            
            // Create room data
            RoomData roomData = new RoomData
            {
                gridPosition = gridPos,
                worldPosition = GridToWorldPosition(gridPos),
                roomType = roomType,
                template = template,
                isCleared = false,
                isVisited = false,
                connections = GetRoomConnections(gridPos)
            };
            
            roomGrid[gridPos] = roomData;
            
            // Instantiate room object
            InstantiateRoom(roomData);
        }
        
        private RoomTemplate GetRoomTemplate(RoomType roomType)
        {
            switch (roomType)
            {
                case RoomType.Start:
                    return startRoomTemplate;
                case RoomType.Boss:
                    return bossRoomTemplate;
                default:
                    var templates = roomTemplates.Where(t => t.roomType == roomType).ToList();
                    if (templates.Count > 0)
                    {
                        return WeightedRandomSelection(templates);
                    }
                    return roomTemplates.Count > 0 ? roomTemplates[0] : null;
            }
        }
        
        private RoomTemplate WeightedRandomSelection(List<RoomTemplate> templates)
        {
            float totalWeight = templates.Sum(t => t.weight);
            float randomValue = Random.Range(0f, totalWeight);
            float currentWeight = 0f;
            
            foreach (var template in templates)
            {
                currentWeight += template.weight;
                if (randomValue <= currentWeight)
                {
                    return template;
                }
            }
            
            return templates[templates.Count - 1];
        }
        
        #endregion
        
        #region Room Types
        
        private RoomType ChooseMainPathRoomType()
        {
            float random = Random.value;
            
            if (random < 0.7f) return RoomType.Combat;
            if (random < 0.85f) return RoomType.Treasure;
            if (random < 0.95f) return RoomType.Shop;
            return RoomType.Rest;
        }
        
        private RoomType ChooseSidePathRoomType()
        {
            float random = Random.value;
            
            if (random < 0.4f) return RoomType.Combat;
            if (random < 0.7f) return RoomType.Treasure;
            if (random < 0.85f) return RoomType.Shop;
            if (random < 0.95f) return RoomType.Rest;
            return RoomType.Secret;
        }
        
        #endregion
        
        #region Utility Methods
        
        private bool IsValidPosition(Vector2Int pos)
        {
            return pos.x >= 0 && pos.x < gridSize.x && pos.y >= 0 && pos.y < gridSize.y;
        }
        
        private Vector3 GridToWorldPosition(Vector2Int gridPos)
        {
            return new Vector3(gridPos.x * roomSize, gridPos.y * roomSize, 0f);
        }
        
        private List<Vector2Int> GetRoomConnections(Vector2Int roomPos)
        {
            List<Vector2Int> connections = new List<Vector2Int>();
            List<Vector2Int> allRooms = new List<Vector2Int>(mainPath);
            allRooms.AddRange(sidePaths);
            
            Vector2Int[] directions = { Vector2Int.up, Vector2Int.down, Vector2Int.left, Vector2Int.right };
            
            foreach (var dir in directions)
            {
                Vector2Int adjacentPos = roomPos + dir;
                if (allRooms.Contains(adjacentPos))
                {
                    connections.Add(dir);
                }
            }
            
            return connections;
        }
        
        private void InstantiateRoom(RoomData roomData)
        {
            if (roomData.template == null || roomData.template.roomPrefab == null)
                return;
            
            GameObject roomObject = Instantiate(roomData.template.roomPrefab, transform);
            roomObject.transform.position = roomData.worldPosition;
            roomObject.name = $"Room_{roomData.gridPosition.x}_{roomData.gridPosition.y}_{roomData.roomType}";
            
            // Setup room component
            Room roomComponent = roomObject.GetComponent<Room>();
            if (roomComponent == null)
                roomComponent = roomObject.AddComponent<Room>();
            
            roomComponent.Initialize(roomData);
        }
        
        private void GenerateRoomContents()
        {
            foreach (var room in roomGrid.Values)
            {
                GenerateRoomContent(room);
            }
        }
        
        private void GenerateRoomContent(RoomData room)
        {
            // This will be expanded when we create the Room class
            // For now, just log the room generation
            Debug.Log($"🏠 Generated content for {room.roomType} room at {room.gridPosition}");
        }
        
        private void SetStartingPosition()
        {
            if (mainPath.Count > 0)
            {
                currentRoomPosition = mainPath[0];
                currentRoom = roomGrid[currentRoomPosition];
                currentRoom.isVisited = true;
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void EnterRoom(Vector2Int gridPosition)
        {
            if (roomGrid.ContainsKey(gridPosition))
            {
                currentRoomPosition = gridPosition;
                currentRoom = roomGrid[gridPosition];
                currentRoom.isVisited = true;
                
                OnRoomEntered?.Invoke(currentRoom);
                
                Debug.Log($"🚪 Entered room: {currentRoom.roomType} at {gridPosition}");
            }
        }
        
        public void ClearCurrentRoom()
        {
            if (currentRoom != null)
            {
                currentRoom.isCleared = true;
                OnRoomCleared?.Invoke(currentRoom);
                
                Debug.Log($"✅ Cleared room: {currentRoom.roomType}");
            }
        }
        
        public RoomData GetCurrentRoom()
        {
            return currentRoom;
        }
        
        public List<RoomData> GetAdjacentRooms()
        {
            List<RoomData> adjacent = new List<RoomData>();
            
            if (currentRoom != null)
            {
                foreach (var connection in currentRoom.connections)
                {
                    Vector2Int adjacentPos = currentRoomPosition + connection;
                    if (roomGrid.ContainsKey(adjacentPos))
                    {
                        adjacent.Add(roomGrid[adjacentPos]);
                    }
                }
            }
            
            return adjacent;
        }
        
        public LevelData GetCurrentLevel()
        {
            return currentLevel;
        }
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmos()
        {
            if (!showDebugGizmos) return;
            
            // Draw grid
            Gizmos.color = Color.gray;
            for (int x = 0; x <= gridSize.x; x++)
            {
                Vector3 start = new Vector3(x * roomSize, 0, 0);
                Vector3 end = new Vector3(x * roomSize, gridSize.y * roomSize, 0);
                Gizmos.DrawLine(start, end);
            }
            
            for (int y = 0; y <= gridSize.y; y++)
            {
                Vector3 start = new Vector3(0, y * roomSize, 0);
                Vector3 end = new Vector3(gridSize.x * roomSize, y * roomSize, 0);
                Gizmos.DrawLine(start, end);
            }
            
            // Draw rooms
            foreach (var room in roomGrid.Values)
            {
                Vector3 center = room.worldPosition;
                
                // Color based on room type
                switch (room.roomType)
                {
                    case RoomType.Start:
                        Gizmos.color = Color.green;
                        break;
                    case RoomType.Boss:
                        Gizmos.color = Color.red;
                        break;
                    case RoomType.Combat:
                        Gizmos.color = Color.yellow;
                        break;
                    case RoomType.Treasure:
                        Gizmos.color = Color.cyan;
                        break;
                    case RoomType.Shop:
                        Gizmos.color = Color.magenta;
                        break;
                    case RoomType.Rest:
                        Gizmos.color = Color.blue;
                        break;
                    default:
                        Gizmos.color = Color.white;
                        break;
                }
                
                if (room.isCleared)
                    Gizmos.color = Color.Lerp(Gizmos.color, Color.gray, 0.5f);
                
                Gizmos.DrawCube(center, Vector3.one * (roomSize * 0.8f));
                
                // Draw connections
                Gizmos.color = Color.white;
                foreach (var connection in room.connections)
                {
                    Vector3 connectionEnd = center + (Vector3)(Vector2)connection * roomSize * 0.4f;
                    Gizmos.DrawLine(center, connectionEnd);
                }
            }
            
            // Highlight current room
            if (currentRoom != null)
            {
                Gizmos.color = Color.white;
                Gizmos.DrawWireCube(currentRoom.worldPosition, Vector3.one * roomSize);
            }
        }
        
        #endregion
    }
}
