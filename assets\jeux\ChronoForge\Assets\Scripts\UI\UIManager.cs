using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using ChronoForge.Core;
using ChronoForge.Player;

namespace ChronoForge.UI
{
    /// <summary>
    /// Gestionnaire principal de l'interface utilisateur pour ChronoForge
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("UI Panels")]
        public GameObject hudPanel;
        public GameObject pausePanel;
        public GameObject inventoryPanel;
        public GameObject settingsPanel;
        public GameObject gameOverPanel;
        public GameObject victoryPanel;
        public GameObject debugPanel;
        
        [Header("HUD Elements")]
        public HUDController hudController;
        public MinimapController minimapController;
        public NotificationController notificationController;
        
        [Header("Menu Controllers")]
        public PauseMenuController pauseMenuController;
        public InventoryController inventoryController;
        public SettingsController settingsController;
        
        [Head<PERSON>("Transition Settings")]
        public float transitionDuration = 0.3f;
        public AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        // Events
        public static System.Action<UIPanel> OnPanelOpened;
        public static System.Action<UIPanel> OnPanelClosed;
        public static System.Action<string> OnNotificationShown;
        
        // State
        private UIPanel currentPanel = UIPanel.HUD;
        private Stack<UIPanel> panelHistory = new Stack<UIPanel>();
        private bool isTransitioning = false;
        private Dictionary<UIPanel, GameObject> panelObjects = new Dictionary<UIPanel, GameObject>();
        
        // Components
        private GameManager gameManager;
        private PlayerController player;
        private Canvas mainCanvas;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeUIManager();
        }
        
        private void Start()
        {
            SetupUI();
        }
        
        private void Update()
        {
            HandleInput();
            UpdateUI();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeUIManager()
        {
            // Get main canvas
            mainCanvas = GetComponent<Canvas>();
            if (mainCanvas == null)
                mainCanvas = GetComponentInParent<Canvas>();
            
            // Initialize panel dictionary
            InitializePanelDictionary();
            
            // Subscribe to events
            SubscribeToEvents();
            
            Debug.Log("🎨 UIManager initialized");
        }
        
        private void InitializePanelDictionary()
        {
            panelObjects[UIPanel.HUD] = hudPanel;
            panelObjects[UIPanel.Pause] = pausePanel;
            panelObjects[UIPanel.Inventory] = inventoryPanel;
            panelObjects[UIPanel.Settings] = settingsPanel;
            panelObjects[UIPanel.GameOver] = gameOverPanel;
            panelObjects[UIPanel.Victory] = victoryPanel;
            panelObjects[UIPanel.Debug] = debugPanel;
        }
        
        private void SetupUI()
        {
            // Find managers
            gameManager = GameManager.Instance;
            player = FindObjectOfType<PlayerController>();
            
            // Initialize controllers
            InitializeControllers();
            
            // Show initial panel
            ShowPanel(UIPanel.HUD, false);
        }
        
        private void InitializeControllers()
        {
            // Initialize HUD
            if (hudController == null)
                hudController = GetComponentInChildren<HUDController>();
            
            if (hudController != null)
                hudController.Initialize();
            
            // Initialize Minimap
            if (minimapController == null)
                minimapController = GetComponentInChildren<MinimapController>();
            
            if (minimapController != null)
                minimapController.Initialize();
            
            // Initialize Notifications
            if (notificationController == null)
                notificationController = GetComponentInChildren<NotificationController>();
            
            if (notificationController != null)
                notificationController.Initialize();
            
            // Initialize menu controllers
            if (pauseMenuController == null)
                pauseMenuController = GetComponentInChildren<PauseMenuController>();
            
            if (inventoryController == null)
                inventoryController = GetComponentInChildren<InventoryController>();
            
            if (settingsController == null)
                settingsController = GetComponentInChildren<SettingsController>();
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleInput()
        {
            // Escape key handling
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                HandleEscapeKey();
            }
            
            // Inventory toggle
            if (Input.GetKeyDown(KeyCode.Tab) || Input.GetKeyDown(KeyCode.I))
            {
                ToggleInventory();
            }
            
            // Debug panel toggle
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleDebugPanel();
            }
            
            // Settings
            if (Input.GetKeyDown(KeyCode.F10))
            {
                ToggleSettings();
            }
        }
        
        private void HandleEscapeKey()
        {
            switch (currentPanel)
            {
                case UIPanel.HUD:
                    ShowPauseMenu();
                    break;
                    
                case UIPanel.Pause:
                    ResumeGame();
                    break;
                    
                case UIPanel.Inventory:
                case UIPanel.Settings:
                    GoBack();
                    break;
                    
                default:
                    ShowPanel(UIPanel.HUD);
                    break;
            }
        }
        
        #endregion
        
        #region Panel Management
        
        public void ShowPanel(UIPanel panel, bool addToHistory = true)
        {
            if (isTransitioning || currentPanel == panel) return;
            
            StartCoroutine(TransitionToPanel(panel, addToHistory));
        }
        
        private System.Collections.IEnumerator TransitionToPanel(UIPanel newPanel, bool addToHistory)
        {
            isTransitioning = true;
            
            // Add current panel to history
            if (addToHistory && currentPanel != newPanel)
            {
                panelHistory.Push(currentPanel);
            }
            
            // Hide current panel
            if (panelObjects.ContainsKey(currentPanel) && panelObjects[currentPanel] != null)
            {
                yield return StartCoroutine(HidePanel(panelObjects[currentPanel]));
            }
            
            // Update current panel
            UIPanel previousPanel = currentPanel;
            currentPanel = newPanel;
            
            // Show new panel
            if (panelObjects.ContainsKey(newPanel) && panelObjects[newPanel] != null)
            {
                yield return StartCoroutine(ShowPanel(panelObjects[newPanel]));
            }
            
            // Handle panel-specific logic
            HandlePanelTransition(previousPanel, newPanel);
            
            // Notify listeners
            OnPanelOpened?.Invoke(newPanel);
            
            isTransitioning = false;
        }
        
        private System.Collections.IEnumerator ShowPanel(GameObject panel)
        {
            panel.SetActive(true);
            
            // Animate panel appearance
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = panel.AddComponent<CanvasGroup>();
            
            float elapsed = 0f;
            canvasGroup.alpha = 0f;
            
            while (elapsed < transitionDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / transitionDuration;
                canvasGroup.alpha = transitionCurve.Evaluate(progress);
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
        }
        
        private System.Collections.IEnumerator HidePanel(GameObject panel)
        {
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                panel.SetActive(false);
                yield break;
            }
            
            float elapsed = 0f;
            float startAlpha = canvasGroup.alpha;
            
            while (elapsed < transitionDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / transitionDuration;
                canvasGroup.alpha = startAlpha * (1f - transitionCurve.Evaluate(progress));
                yield return null;
            }
            
            canvasGroup.alpha = 0f;
            panel.SetActive(false);
        }
        
        private void HandlePanelTransition(UIPanel from, UIPanel to)
        {
            // Handle game state changes
            switch (to)
            {
                case UIPanel.HUD:
                    if (gameManager != null && gameManager.IsGamePaused())
                    {
                        gameManager.ResumeGame();
                    }
                    break;
                    
                case UIPanel.Pause:
                    if (gameManager != null && !gameManager.IsGamePaused())
                    {
                        gameManager.PauseGame();
                    }
                    break;
                    
                case UIPanel.Inventory:
                    // Pause game when in inventory
                    Time.timeScale = 0f;
                    break;
                    
                case UIPanel.Settings:
                    // Settings can be accessed from anywhere
                    break;
            }
            
            // Update controllers
            UpdateControllers(to);
        }
        
        private void UpdateControllers(UIPanel activePanel)
        {
            // Update HUD visibility
            if (hudController != null)
            {
                hudController.SetVisible(activePanel == UIPanel.HUD);
            }
            
            // Update minimap
            if (minimapController != null)
            {
                minimapController.SetVisible(activePanel == UIPanel.HUD);
            }
            
            // Update specific controllers
            switch (activePanel)
            {
                case UIPanel.Inventory:
                    if (inventoryController != null)
                        inventoryController.RefreshInventory();
                    break;
                    
                case UIPanel.Settings:
                    if (settingsController != null)
                        settingsController.RefreshSettings();
                    break;
            }
        }
        
        #endregion
        
        #region Specific Panel Methods
        
        public void ShowPauseMenu()
        {
            ShowPanel(UIPanel.Pause);
        }
        
        public void ResumeGame()
        {
            ShowPanel(UIPanel.HUD);
        }
        
        public void ToggleInventory()
        {
            if (currentPanel == UIPanel.Inventory)
            {
                GoBack();
            }
            else if (currentPanel == UIPanel.HUD)
            {
                ShowPanel(UIPanel.Inventory);
            }
        }
        
        public void ToggleSettings()
        {
            if (currentPanel == UIPanel.Settings)
            {
                GoBack();
            }
            else
            {
                ShowPanel(UIPanel.Settings);
            }
        }
        
        public void ToggleDebugPanel()
        {
            if (currentPanel == UIPanel.Debug)
            {
                GoBack();
            }
            else
            {
                ShowPanel(UIPanel.Debug);
            }
        }
        
        public void ShowGameOver()
        {
            ShowPanel(UIPanel.GameOver, false);
        }
        
        public void ShowVictory()
        {
            ShowPanel(UIPanel.Victory, false);
        }
        
        public void GoBack()
        {
            if (panelHistory.Count > 0)
            {
                UIPanel previousPanel = panelHistory.Pop();
                ShowPanel(previousPanel, false);
            }
            else
            {
                ShowPanel(UIPanel.HUD, false);
            }
        }
        
        #endregion
        
        #region Notifications
        
        public void ShowNotification(string message, NotificationType type = NotificationType.Info, float duration = 3f)
        {
            if (notificationController != null)
            {
                notificationController.ShowNotification(message, type, duration);
            }
            
            OnNotificationShown?.Invoke(message);
            
            Debug.Log($"📢 Notification: {message}");
        }
        
        public void ShowAchievement(string title, string description, Sprite icon = null)
        {
            if (notificationController != null)
            {
                notificationController.ShowAchievement(title, description, icon);
            }
        }
        
        #endregion
        
        #region Event Handling
        
        private void SubscribeToEvents()
        {
            // Game state events
            GameManager.OnGameStateChanged += HandleGameStateChanged;
            GameManager.OnPlayerDeath += HandlePlayerDeath;
            GameManager.OnRunCompleted += HandleRunCompleted;
            
            // Player events
            PlayerController.OnPlayerDeath += HandlePlayerDeath;
            
            // Progression events
            ProgressionManager.OnLevelUp += HandleLevelUp;
            ProgressionManager.OnAchievementUnlocked += HandleAchievementUnlocked;
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (GameManager.Instance != null)
            {
                GameManager.OnGameStateChanged -= HandleGameStateChanged;
                GameManager.OnPlayerDeath -= HandlePlayerDeath;
                GameManager.OnRunCompleted -= HandleRunCompleted;
            }
            
            PlayerController.OnPlayerDeath -= HandlePlayerDeath;
            ProgressionManager.OnLevelUp -= HandleLevelUp;
            ProgressionManager.OnAchievementUnlocked -= HandleAchievementUnlocked;
        }
        
        private void HandleGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.GameOver:
                    ShowGameOver();
                    break;
                    
                case GameState.Victory:
                    ShowVictory();
                    break;
                    
                case GameState.InRun:
                    if (currentPanel != UIPanel.HUD)
                        ShowPanel(UIPanel.HUD, false);
                    break;
            }
        }
        
        private void HandlePlayerDeath()
        {
            ShowNotification("You have died!", NotificationType.Error, 5f);
        }
        
        private void HandleRunCompleted()
        {
            ShowNotification("Run completed!", NotificationType.Success, 5f);
        }
        
        private void HandleLevelUp(int newLevel)
        {
            ShowNotification($"Level Up! Now level {newLevel}", NotificationType.Success, 4f);
        }
        
        private void HandleAchievementUnlocked(string achievementId)
        {
            ShowNotification($"Achievement Unlocked: {achievementId}", NotificationType.Achievement, 5f);
        }
        
        #endregion
        
        #region Update
        
        private void UpdateUI()
        {
            // Update active controllers
            switch (currentPanel)
            {
                case UIPanel.HUD:
                    if (hudController != null)
                        hudController.UpdateHUD();
                    
                    if (minimapController != null)
                        minimapController.UpdateMinimap();
                    break;
            }
            
            // Always update notifications
            if (notificationController != null)
                notificationController.UpdateNotifications();
        }
        
        #endregion
        
        #region Public Methods
        
        public UIPanel GetCurrentPanel()
        {
            return currentPanel;
        }
        
        public bool IsInGameUI()
        {
            return currentPanel == UIPanel.HUD;
        }
        
        public bool IsMenuOpen()
        {
            return currentPanel != UIPanel.HUD;
        }
        
        public void SetHUDVisible(bool visible)
        {
            if (hudPanel != null)
                hudPanel.SetActive(visible);
        }
        
        public HUDController GetHUDController()
        {
            return hudController;
        }
        
        public MinimapController GetMinimapController()
        {
            return minimapController;
        }
        
        public NotificationController GetNotificationController()
        {
            return notificationController;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types de panneaux UI
    /// </summary>
    public enum UIPanel
    {
        HUD,
        Pause,
        Inventory,
        Settings,
        GameOver,
        Victory,
        Debug,
        MainMenu,
        Loading
    }
    
    /// <summary>
    /// Types de notifications
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error,
        Achievement,
        Quest,
        Combat
    }
}
