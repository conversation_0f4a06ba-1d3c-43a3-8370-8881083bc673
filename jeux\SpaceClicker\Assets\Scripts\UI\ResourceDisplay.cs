using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Numerics;
using SpaceClicker.Core;
using SpaceClicker.Utils;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Composant pour afficher une ressource spécifique
    /// </summary>
    public class ResourceDisplay : MonoBehaviour
    {
        [Header("UI References")]
        public TextMeshProUGUI resourceAmountText;
        public TextMeshProUGUI productionRateText;
        public Image resourceIcon;
        public Button clickButton;
        
        [Header("Settings")]
        public ResourceType resourceType;
        public bool showProductionRate = true;
        public bool isClickable = true;
        
        [Header("Animation")]
        public float countUpDuration = 0.5f;
        public AnimationCurve countUpCurve = AnimationCurve.EaseOut(0, 0, 1, 1);
        
        // Private fields
        private ResourceManager resourceManager;
        private BigInteger currentDisplayedAmount;
        private BigInteger targetAmount;
        private Coroutine countUpCoroutine;
        
        // Animation
        private Vector3 originalScale;
        private Color originalColor;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Store original values for animations
            if (resourceAmountText != null)
            {
                originalScale = resourceAmountText.transform.localScale;
                originalColor = resourceAmountText.color;
            }
        }
        
        private void Start()
        {
            Initialize();
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (resourceManager != null)
            {
                ResourceManager.OnResourceChanged -= OnResourceChanged;
                ResourceManager.OnResourceGained -= OnResourceGained;
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void Initialize()
        {
            // Get ResourceManager reference
            resourceManager = FindObjectOfType<ResourceManager>();
            
            if (resourceManager == null)
            {
                Debug.LogError($"ResourceManager not found for {gameObject.name}");
                return;
            }
            
            // Subscribe to events
            ResourceManager.OnResourceChanged += OnResourceChanged;
            ResourceManager.OnResourceGained += OnResourceGained;
            
            // Setup click button
            if (clickButton != null && isClickable)
            {
                clickButton.onClick.AddListener(OnResourceClicked);
            }
            
            // Initial display update
            UpdateDisplay();
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnResourceChanged(ResourceType type, BigInteger amount)
        {
            if (type == resourceType)
            {
                targetAmount = amount;
                StartCountUpAnimation();
            }
        }
        
        private void OnResourceGained(ResourceType type, BigInteger amount)
        {
            if (type == resourceType)
            {
                // Play gain animation
                PlayGainAnimation();
            }
        }
        
        private void OnResourceClicked()
        {
            if (resourceManager != null && isClickable)
            {
                resourceManager.OnResourceClicked(resourceType);
                PlayClickAnimation();
            }
        }
        
        #endregion
        
        #region Display Updates
        
        private void UpdateDisplay()
        {
            if (resourceManager == null) return;
            
            // Update amount
            BigInteger currentAmount = resourceManager.GetResource(resourceType);
            currentDisplayedAmount = currentAmount;
            targetAmount = currentAmount;
            
            if (resourceAmountText != null)
            {
                resourceAmountText.text = currentAmount.ToDisplayString();
            }
            
            // Update production rate
            if (showProductionRate && productionRateText != null)
            {
                float rate = resourceManager.GetProductionRate(resourceType);
                if (rate > 0)
                {
                    productionRateText.text = $"+{rate.ToBigInteger().ToCompactString()}/sec";
                    productionRateText.gameObject.SetActive(true);
                }
                else
                {
                    productionRateText.gameObject.SetActive(false);
                }
            }
            
            // Update icon color
            if (resourceIcon != null)
            {
                resourceIcon.color = GetResourceColor();
            }
        }
        
        private Color GetResourceColor()
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return ColorUtility.TryParseHtmlString(GameConstants.ENERGY_COLOR, out Color energyColor) ? energyColor : Color.yellow;
                case ResourceType.Minerals:
                    return ColorUtility.TryParseHtmlString(GameConstants.MINERALS_COLOR, out Color mineralsColor) ? mineralsColor : Color.cyan;
                case ResourceType.ResearchData:
                    return ColorUtility.TryParseHtmlString(GameConstants.RESEARCH_COLOR, out Color researchColor) ? researchColor : Color.green;
                case ResourceType.SpaceCurrency:
                    return ColorUtility.TryParseHtmlString(GameConstants.CURRENCY_COLOR, out Color currencyColor) ? currencyColor : Color.magenta;
                default:
                    return Color.white;
            }
        }
        
        #endregion
        
        #region Animations
        
        private void StartCountUpAnimation()
        {
            if (countUpCoroutine != null)
            {
                StopCoroutine(countUpCoroutine);
            }
            
            countUpCoroutine = StartCoroutine(CountUpCoroutine());
        }
        
        private System.Collections.IEnumerator CountUpCoroutine()
        {
            BigInteger startAmount = currentDisplayedAmount;
            BigInteger endAmount = targetAmount;
            
            float elapsedTime = 0f;
            
            while (elapsedTime < countUpDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / countUpDuration;
                float curveValue = countUpCurve.Evaluate(progress);
                
                // Interpolate between start and end amounts
                double startDouble = (double)startAmount;
                double endDouble = (double)endAmount;
                double currentDouble = Mathf.Lerp((float)startDouble, (float)endDouble, curveValue);
                
                currentDisplayedAmount = new BigInteger(currentDouble);
                
                if (resourceAmountText != null)
                {
                    resourceAmountText.text = currentDisplayedAmount.ToDisplayString();
                }
                
                yield return null;
            }
            
            // Ensure we end with the exact target amount
            currentDisplayedAmount = targetAmount;
            if (resourceAmountText != null)
            {
                resourceAmountText.text = currentDisplayedAmount.ToDisplayString();
            }
        }
        
        private void PlayGainAnimation()
        {
            if (resourceAmountText != null)
            {
                // Scale pulse animation
                LeanTween.scale(resourceAmountText.gameObject, originalScale * 1.2f, 0.1f)
                    .setEase(LeanTweenType.easeOutQuad)
                    .setOnComplete(() => {
                        LeanTween.scale(resourceAmountText.gameObject, originalScale, 0.2f)
                            .setEase(LeanTweenType.easeOutQuad);
                    });
                
                // Color flash animation
                LeanTween.value(gameObject, 0f, 1f, 0.3f)
                    .setOnUpdate((float value) => {
                        Color flashColor = Color.Lerp(Color.white, originalColor, value);
                        resourceAmountText.color = flashColor;
                    });
            }
        }
        
        private void PlayClickAnimation()
        {
            if (clickButton != null)
            {
                // Button press animation
                LeanTween.scale(clickButton.gameObject, originalScale * 0.95f, 0.05f)
                    .setEase(LeanTweenType.easeOutQuad)
                    .setOnComplete(() => {
                        LeanTween.scale(clickButton.gameObject, originalScale, 0.1f)
                            .setEase(LeanTweenType.easeOutBounce);
                    });
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Force refresh the display
        /// </summary>
        public void RefreshDisplay()
        {
            UpdateDisplay();
        }
        
        /// <summary>
        /// Set the resource type for this display
        /// </summary>
        public void SetResourceType(ResourceType type)
        {
            resourceType = type;
            UpdateDisplay();
        }
        
        /// <summary>
        /// Enable or disable click functionality
        /// </summary>
        public void SetClickable(bool clickable)
        {
            isClickable = clickable;
            
            if (clickButton != null)
            {
                clickButton.interactable = clickable;
            }
        }
        
        #endregion
    }
}
