using UnityEngine;
using System.Collections.Generic;
using System.Numerics;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Données d'une amélioration
    /// </summary>
    [System.Serializable]
    public class UpgradeData
    {
        [Header("Basic Info")]
        public string id;
        public string name;
        public string description;
        
        [Header("Cost")]
        public ResourceType costType;
        public float baseCost;
        public float costMultiplier = 1.15f;
        
        [Header("Effect")]
        public ResourceType targetResource;
        public float effectValue;
        public bool isMultiplier = false;
        
        [Header("Limits")]
        public int maxLevel = -1; // -1 = unlimited
        
        // Current state
        public int currentLevel = 0;
        
        /// <summary>
        /// Obtient le coût pour le prochain niveau
        /// </summary>
        public BigInteger GetNextCost()
        {
            if (maxLevel > 0 && currentLevel >= maxLevel)
                return BigInteger.Zero;
            
            float cost = baseCost * Mathf.Pow(costMultiplier, currentLevel);
            return new BigInteger(cost);
        }
        
        /// <summary>
        /// Obtient l'effet total actuel
        /// </summary>
        public float GetCurrentEffect()
        {
            return effectValue * currentLevel;
        }
        
        /// <summary>
        /// Vérifie si l'amélioration peut être achetée
        /// </summary>
        public bool CanPurchase()
        {
            return maxLevel < 0 || currentLevel < maxLevel;
        }
    }
    
    /// <summary>
    /// Gestionnaire d'améliorations pour SpaceClicker
    /// </summary>
    public class UpgradeManager : MonoBehaviour
    {
        [Header("Upgrades")]
        public List<UpgradeData> availableUpgrades = new List<UpgradeData>();
        
        // Components
        private ResourceManager resourceManager;
        
        // Events
        public static System.Action<UpgradeData> OnUpgradePurchased;
        public static System.Action<UpgradeData> OnUpgradeUnlocked;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeUpgrades();
        }
        
        private void Start()
        {
            resourceManager = FindObjectOfType<ResourceManager>();
            LoadData();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeUpgrades()
        {
            if (availableUpgrades.Count == 0)
            {
                CreateDefaultUpgrades();
            }
            
            Debug.Log("⬆️ UpgradeManager initialized");
        }
        
        private void CreateDefaultUpgrades()
        {
            // Energy generation upgrades
            availableUpgrades.Add(new UpgradeData
            {
                id = "energy_generator_1",
                name = "Solar Panel",
                description = "Generates +1 Energy per second",
                costType = ResourceType.Credits,
                baseCost = 10f,
                costMultiplier = 1.15f,
                targetResource = ResourceType.Energy,
                effectValue = 1f,
                isMultiplier = false
            });
            
            availableUpgrades.Add(new UpgradeData
            {
                id = "energy_generator_2",
                name = "Fusion Reactor",
                description = "Generates +5 Energy per second",
                costType = ResourceType.Credits,
                baseCost = 100f,
                costMultiplier = 1.2f,
                targetResource = ResourceType.Energy,
                effectValue = 5f,
                isMultiplier = false
            });
            
            // Matter generation upgrades
            availableUpgrades.Add(new UpgradeData
            {
                id = "matter_converter_1",
                name = "Matter Converter",
                description = "Generates +0.5 Matter per second",
                costType = ResourceType.Energy,
                baseCost = 50f,
                costMultiplier = 1.25f,
                targetResource = ResourceType.Matter,
                effectValue = 0.5f,
                isMultiplier = false
            });
            
            // Click multipliers
            availableUpgrades.Add(new UpgradeData
            {
                id = "click_multiplier_1",
                name = "Enhanced Clicking",
                description = "Increases click value by 100%",
                costType = ResourceType.Credits,
                baseCost = 25f,
                costMultiplier = 2f,
                targetResource = ResourceType.Energy,
                effectValue = 1f,
                isMultiplier = true,
                maxLevel = 10
            });
        }
        
        #endregion
        
        #region Upgrade Management
        
        public bool PurchaseUpgrade(string upgradeId)
        {
            UpgradeData upgrade = GetUpgrade(upgradeId);
            if (upgrade == null || !upgrade.CanPurchase())
                return false;
            
            BigInteger cost = upgrade.GetNextCost();
            
            if (resourceManager != null && resourceManager.SpendResource(upgrade.costType, cost))
            {
                // Apply upgrade effect
                ApplyUpgradeEffect(upgrade);
                
                // Increase level
                upgrade.currentLevel++;
                
                // Notify listeners
                OnUpgradePurchased?.Invoke(upgrade);
                
                Debug.Log($"⬆️ Purchased {upgrade.name} level {upgrade.currentLevel}");
                return true;
            }
            
            return false;
        }
        
        private void ApplyUpgradeEffect(UpgradeData upgrade)
        {
            if (resourceManager == null) return;
            
            if (upgrade.isMultiplier)
            {
                // Apply multiplier effect (this would need more complex logic)
                Debug.Log($"Applied multiplier effect: {upgrade.effectValue}");
            }
            else
            {
                // Apply additive effect
                resourceManager.AddGenerationRate(upgrade.targetResource, upgrade.effectValue);
            }
        }
        
        public UpgradeData GetUpgrade(string upgradeId)
        {
            return availableUpgrades.Find(u => u.id == upgradeId);
        }
        
        public List<UpgradeData> GetAvailableUpgrades()
        {
            return availableUpgrades.FindAll(u => u.CanPurchase());
        }
        
        public List<UpgradeData> GetPurchasableUpgrades()
        {
            if (resourceManager == null) return new List<UpgradeData>();
            
            return availableUpgrades.FindAll(u => 
                u.CanPurchase() && 
                resourceManager.HasResource(u.costType, u.GetNextCost())
            );
        }
        
        #endregion
        
        #region Save/Load
        
        public void SaveData()
        {
            foreach (var upgrade in availableUpgrades)
            {
                string key = $"Upgrade_{upgrade.id}_Level";
                PlayerPrefs.SetInt(key, upgrade.currentLevel);
            }
            
            PlayerPrefs.Save();
        }
        
        public void LoadData()
        {
            foreach (var upgrade in availableUpgrades)
            {
                string key = $"Upgrade_{upgrade.id}_Level";
                if (PlayerPrefs.HasKey(key))
                {
                    int level = PlayerPrefs.GetInt(key);
                    
                    // Apply all levels of the upgrade
                    for (int i = 0; i < level; i++)
                    {
                        ApplyUpgradeEffect(upgrade);
                    }
                    
                    upgrade.currentLevel = level;
                }
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public bool CanAffordUpgrade(string upgradeId)
        {
            UpgradeData upgrade = GetUpgrade(upgradeId);
            if (upgrade == null || !upgrade.CanPurchase())
                return false;
            
            return resourceManager != null && 
                   resourceManager.HasResource(upgrade.costType, upgrade.GetNextCost());
        }
        
        public int GetUpgradeLevel(string upgradeId)
        {
            UpgradeData upgrade = GetUpgrade(upgradeId);
            return upgrade?.currentLevel ?? 0;
        }
        
        #endregion
    }
}
