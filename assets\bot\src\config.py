"""
Gestionnaire de configuration pour le bot de publication.
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class ConfigManager:
    """Gestionnaire de configuration centralisé."""
    
    def __init__(self, config_file: str = "config.yaml", env_file: str = ".env"):
        self.config_file = Path(config_file)
        self.env_file = Path(env_file)
        self.config = {}
        
        # Charger les variables d'environnement
        self._load_environment()
        
        # Charger la configuration
        self._load_config()
    
    def _load_environment(self):
        """Charge les variables d'environnement depuis le fichier .env."""
        if self.env_file.exists():
            load_dotenv(self.env_file)
            logger.info(f"Variables d'environnement chargées depuis {self.env_file}")
        else:
            logger.warning(f"Fichier .env non trouvé: {self.env_file}")
    
    def _load_config(self):
        """Charge la configuration depuis le fichier YAML."""
        if not self.config_file.exists():
            logger.error(f"Fichier de configuration non trouvé: {self.config_file}")
            self._create_default_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f) or {}
            logger.info(f"Configuration chargée depuis {self.config_file}")
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Crée une configuration par défaut."""
        self.config = {
            'general': {
                'timezone': 'Europe/Paris',
                'default_retry_attempts': 3,
                'default_retry_delay': 60,
                'log_file': 'bot.log',
                'posts_directory': 'posts'
            },
            'platforms': {
                'twitter': {
                    'enabled': True,
                    'max_length': 280,
                    'hashtags': ['#dev', '#opensource'],
                    'rate_limit_delay': 15
                },
                'reddit': {
                    'enabled': True,
                    'default_subreddit': 'test',
                    'rate_limit_delay': 60,
                    'subreddits': ['webdev', 'programming']
                },
                'linkedin': {
                    'enabled': True,
                    'max_length': 3000,
                    'rate_limit_delay': 30
                },
                'devto': {
                    'enabled': True,
                    'default_tags': ['webdev', 'opensource'],
                    'rate_limit_delay': 120
                }
            },
            'scheduler': {
                'enabled': True,
                'default_interval_hours': 24,
                'working_hours': {
                    'start': '09:00',
                    'end': '18:00'
                },
                'working_days': [1, 2, 3, 4, 5]
            }
        }
        logger.info("Configuration par défaut créée")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Récupère une valeur de configuration."""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """Définit une valeur de configuration."""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """Sauvegarde la configuration dans le fichier."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"Configuration sauvegardée dans {self.config_file}")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de la configuration: {e}")
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Valide que toutes les clés API nécessaires sont présentes."""
        required_keys = {
            'twitter': [
                'TWITTER_API_KEY',
                'TWITTER_API_SECRET', 
                'TWITTER_ACCESS_TOKEN',
                'TWITTER_ACCESS_TOKEN_SECRET',
                'TWITTER_BEARER_TOKEN'
            ],
            'reddit': [
                'REDDIT_CLIENT_ID',
                'REDDIT_CLIENT_SECRET',
                'REDDIT_USERNAME',
                'REDDIT_PASSWORD'
            ],
            'linkedin': [
                'LINKEDIN_ACCESS_TOKEN'
            ],
            'devto': [
                'DEVTO_API_KEY'
            ]
        }
        
        validation_results = {}
        
        for platform, keys in required_keys.items():
            platform_enabled = self.get(f'platforms.{platform}.enabled', True)
            
            if not platform_enabled:
                validation_results[platform] = True  # Skip validation for disabled platforms
                continue
            
            missing_keys = []
            for key in keys:
                if not os.getenv(key):
                    missing_keys.append(key)
            
            validation_results[platform] = len(missing_keys) == 0
            
            if missing_keys:
                logger.warning(f"Clés API manquantes pour {platform}: {missing_keys}")
        
        return validation_results
    
    def get_platform_config(self, platform: str) -> Dict[str, Any]:
        """Récupère la configuration d'une plateforme spécifique."""
        return self.get(f'platforms.{platform}', {})
    
    def is_platform_enabled(self, platform: str) -> bool:
        """Vérifie si une plateforme est activée."""
        return self.get(f'platforms.{platform}.enabled', False)

def setup_logging(config: ConfigManager):
    """Configure le système de logging."""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_file = config.get('general.log_file', 'bot.log')
    
    # Configuration du format de log
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # Configuration du logger principal
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # Réduire le niveau de log pour les librairies externes
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('tweepy').setLevel(logging.WARNING)
    logging.getLogger('praw').setLevel(logging.WARNING)
    logging.getLogger('apscheduler').setLevel(logging.WARNING)
    
    logger.info(f"Logging configuré - Niveau: {log_level}, Fichier: {log_file}")

def validate_environment() -> bool:
    """Valide que l'environnement est correctement configuré."""
    errors = []
    
    # Vérifier Python version
    import sys
    if sys.version_info < (3, 8):
        errors.append("Python 3.8+ requis")
    
    # Vérifier les dépendances critiques
    try:
        import yaml
        import tweepy
        import praw
        import requests
        from apscheduler.schedulers.blocking import BlockingScheduler
    except ImportError as e:
        errors.append(f"Dépendance manquante: {e}")
    
    # Vérifier les fichiers requis
    required_files = ['config.yaml']
    for file_path in required_files:
        if not Path(file_path).exists():
            errors.append(f"Fichier requis manquant: {file_path}")
    
    if errors:
        for error in errors:
            logger.error(f"❌ {error}")
        return False
    
    logger.info("✅ Environnement validé avec succès")
    return True
