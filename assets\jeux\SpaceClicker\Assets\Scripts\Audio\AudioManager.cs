using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace SpaceClicker.Audio
{
    /// <summary>
    /// Gestionnaire audio pour Space Clicker
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("Audio Sources")]
        public AudioSource musicSource;
        public AudioSource sfxSource;
        public AudioSource ambientSource;
        
        [Header("Music Tracks")]
        public AudioClip mainMenuMusic;
        public AudioClip gameplayMusic;
        public AudioClip upgradeMusic;
        
        [<PERSON><PERSON>("SFX Clips")]
        public AudioClip clickSound;
        public AudioClip upgradeSound;
        public AudioClip resourceGainSound;
        public AudioClip buttonHoverSound;
        public AudioClip errorSound;
        public AudioClip successSound;
        
        [Header("Ambient Sounds")]
        public AudioClip spaceAmbient;
        public AudioClip stationHum;
        
        [Header("Settings")]
        [Range(0f, 1f)]
        public float masterVolume = 1f;
        [Range(0f, 1f)]
        public float musicVolume = 0.7f;
        [Range(0f, 1f)]
        public float sfxVolume = 0.8f;
        [Range(0f, 1f)]
        public float ambientVolume = 0.5f;
        
        [Header("Fade Settings")]
        public float musicFadeDuration = 2f;
        public float sfxFadeDuration = 0.5f;
        
        // Private fields
        private Dictionary<string, AudioClip> sfxClips = new Dictionary<string, AudioClip>();
        private Dictionary<string, AudioClip> musicClips = new Dictionary<string, AudioClip>();
        private Coroutine musicFadeCoroutine;
        private bool isMuted = false;
        
        // Singleton pattern
        public static AudioManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudio();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            LoadAudioSettings();
            PlayAmbientSound();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeAudio()
        {
            // Setup audio sources if not assigned
            if (musicSource == null)
            {
                GameObject musicObj = new GameObject("MusicSource");
                musicObj.transform.SetParent(transform);
                musicSource = musicObj.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }
            
            if (sfxSource == null)
            {
                GameObject sfxObj = new GameObject("SFXSource");
                sfxObj.transform.SetParent(transform);
                sfxSource = sfxObj.AddComponent<AudioSource>();
                sfxSource.loop = false;
                sfxSource.playOnAwake = false;
            }
            
            if (ambientSource == null)
            {
                GameObject ambientObj = new GameObject("AmbientSource");
                ambientObj.transform.SetParent(transform);
                ambientSource = ambientObj.AddComponent<AudioSource>();
                ambientSource.loop = true;
                ambientSource.playOnAwake = false;
            }
            
            // Initialize clip dictionaries
            InitializeClipDictionaries();
        }
        
        private void InitializeClipDictionaries()
        {
            // Music clips
            if (mainMenuMusic != null) musicClips["menu"] = mainMenuMusic;
            if (gameplayMusic != null) musicClips["gameplay"] = gameplayMusic;
            if (upgradeMusic != null) musicClips["upgrade"] = upgradeMusic;
            
            // SFX clips
            if (clickSound != null) sfxClips["click"] = clickSound;
            if (upgradeSound != null) sfxClips["upgrade"] = upgradeSound;
            if (resourceGainSound != null) sfxClips["resource_gain"] = resourceGainSound;
            if (buttonHoverSound != null) sfxClips["button_hover"] = buttonHoverSound;
            if (errorSound != null) sfxClips["error"] = errorSound;
            if (successSound != null) sfxClips["success"] = successSound;
        }
        
        #endregion
        
        #region Music Control
        
        public void PlayMusic(string trackName, bool fadeIn = true)
        {
            if (!musicClips.ContainsKey(trackName))
            {
                Debug.LogWarning($"Music track '{trackName}' not found!");
                return;
            }
            
            AudioClip newTrack = musicClips[trackName];
            
            if (musicSource.clip == newTrack && musicSource.isPlaying)
                return; // Already playing this track
            
            if (fadeIn && musicSource.isPlaying)
            {
                // Fade out current track, then fade in new track
                StartCoroutine(CrossfadeMusic(newTrack));
            }
            else
            {
                // Play immediately
                musicSource.clip = newTrack;
                musicSource.volume = musicVolume * masterVolume;
                musicSource.Play();
            }
        }
        
        public void StopMusic(bool fadeOut = true)
        {
            if (fadeOut)
            {
                StartCoroutine(FadeOutMusic());
            }
            else
            {
                musicSource.Stop();
            }
        }
        
        public void PauseMusic()
        {
            musicSource.Pause();
        }
        
        public void ResumeMusic()
        {
            musicSource.UnPause();
        }
        
        private IEnumerator CrossfadeMusic(AudioClip newTrack)
        {
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            musicFadeCoroutine = StartCoroutine(CrossfadeMusicCoroutine(newTrack));
            yield return musicFadeCoroutine;
        }
        
        private IEnumerator CrossfadeMusicCoroutine(AudioClip newTrack)
        {
            float startVolume = musicSource.volume;
            
            // Fade out current track
            float elapsedTime = 0f;
            while (elapsedTime < musicFadeDuration * 0.5f)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / (musicFadeDuration * 0.5f);
                musicSource.volume = Mathf.Lerp(startVolume, 0f, progress);
                yield return null;
            }
            
            // Switch to new track
            musicSource.clip = newTrack;
            musicSource.Play();
            
            // Fade in new track
            elapsedTime = 0f;
            float targetVolume = musicVolume * masterVolume;
            
            while (elapsedTime < musicFadeDuration * 0.5f)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / (musicFadeDuration * 0.5f);
                musicSource.volume = Mathf.Lerp(0f, targetVolume, progress);
                yield return null;
            }
            
            musicSource.volume = targetVolume;
        }
        
        private IEnumerator FadeOutMusic()
        {
            float startVolume = musicSource.volume;
            float elapsedTime = 0f;
            
            while (elapsedTime < musicFadeDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / musicFadeDuration;
                musicSource.volume = Mathf.Lerp(startVolume, 0f, progress);
                yield return null;
            }
            
            musicSource.Stop();
            musicSource.volume = startVolume;
        }
        
        #endregion
        
        #region SFX Control
        
        public void PlaySFX(string clipName, float volumeMultiplier = 1f)
        {
            if (!sfxClips.ContainsKey(clipName))
            {
                Debug.LogWarning($"SFX clip '{clipName}' not found!");
                return;
            }
            
            if (isMuted) return;
            
            AudioClip clip = sfxClips[clipName];
            float volume = sfxVolume * masterVolume * volumeMultiplier;
            
            sfxSource.PlayOneShot(clip, volume);
        }
        
        public void PlaySFX(AudioClip clip, float volumeMultiplier = 1f)
        {
            if (clip == null || isMuted) return;
            
            float volume = sfxVolume * masterVolume * volumeMultiplier;
            sfxSource.PlayOneShot(clip, volume);
        }
        
        public void PlayClickSound()
        {
            PlaySFX("click");
        }
        
        public void PlayUpgradeSound()
        {
            PlaySFX("upgrade");
        }
        
        public void PlayResourceGainSound()
        {
            PlaySFX("resource_gain");
        }
        
        public void PlayButtonHoverSound()
        {
            PlaySFX("button_hover", 0.5f); // Quieter for hover
        }
        
        public void PlayErrorSound()
        {
            PlaySFX("error");
        }
        
        public void PlaySuccessSound()
        {
            PlaySFX("success");
        }
        
        #endregion
        
        #region Ambient Control
        
        public void PlayAmbientSound()
        {
            if (spaceAmbient != null)
            {
                ambientSource.clip = spaceAmbient;
                ambientSource.volume = ambientVolume * masterVolume;
                ambientSource.Play();
            }
        }
        
        public void StopAmbientSound()
        {
            ambientSource.Stop();
        }
        
        #endregion
        
        #region Volume Control
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
            SaveAudioSettings();
        }
        
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            UpdateMusicVolume();
            SaveAudioSettings();
        }
        
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            SaveAudioSettings();
        }
        
        public void SetAmbientVolume(float volume)
        {
            ambientVolume = Mathf.Clamp01(volume);
            UpdateAmbientVolume();
            SaveAudioSettings();
        }
        
        public void ToggleMute()
        {
            isMuted = !isMuted;
            
            if (isMuted)
            {
                musicSource.volume = 0f;
                ambientSource.volume = 0f;
            }
            else
            {
                UpdateAllVolumes();
            }
            
            SaveAudioSettings();
        }
        
        private void UpdateAllVolumes()
        {
            UpdateMusicVolume();
            UpdateAmbientVolume();
        }
        
        private void UpdateMusicVolume()
        {
            if (musicSource != null && !isMuted)
            {
                musicSource.volume = musicVolume * masterVolume;
            }
        }
        
        private void UpdateAmbientVolume()
        {
            if (ambientSource != null && !isMuted)
            {
                ambientSource.volume = ambientVolume * masterVolume;
            }
        }
        
        #endregion
        
        #region Settings Persistence
        
        private void SaveAudioSettings()
        {
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", sfxVolume);
            PlayerPrefs.SetFloat("AmbientVolume", ambientVolume);
            PlayerPrefs.SetInt("AudioMuted", isMuted ? 1 : 0);
            PlayerPrefs.Save();
        }
        
        private void LoadAudioSettings()
        {
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 0.8f);
            ambientVolume = PlayerPrefs.GetFloat("AmbientVolume", 0.5f);
            isMuted = PlayerPrefs.GetInt("AudioMuted", 0) == 1;
            
            UpdateAllVolumes();
        }
        
        #endregion
        
        #region Public Utility Methods
        
        /// <summary>
        /// Check if music is currently playing
        /// </summary>
        public bool IsMusicPlaying()
        {
            return musicSource != null && musicSource.isPlaying;
        }
        
        /// <summary>
        /// Get current music volume
        /// </summary>
        public float GetMusicVolume()
        {
            return musicVolume;
        }
        
        /// <summary>
        /// Get current SFX volume
        /// </summary>
        public float GetSFXVolume()
        {
            return sfxVolume;
        }
        
        /// <summary>
        /// Get current ambient volume
        /// </summary>
        public float GetAmbientVolume()
        {
            return ambientVolume;
        }
        
        /// <summary>
        /// Check if audio is muted
        /// </summary>
        public bool IsMuted()
        {
            return isMuted;
        }
        
        #endregion
    }
}
