using UnityEngine;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Gestionnaire principal du jeu SpaceClicker
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public bool autoSave = true;
        public float autoSaveInterval = 30f;
        
        // Managers
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        
        // Game state
        private bool isGamePaused = false;
        private float lastSaveTime = 0f;
        
        // Singleton
        public static GameManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartGame();
        }
        
        private void Update()
        {
            if (autoSave && Time.time - lastSaveTime >= autoSaveInterval)
            {
                SaveGame();
                lastSaveTime = Time.time;
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeGame()
        {
            // Find or create managers
            resourceManager = FindObjectOfType<ResourceManager>();
            if (resourceManager == null)
            {
                GameObject rmObj = new GameObject("ResourceManager");
                resourceManager = rmObj.AddComponent<ResourceManager>();
            }
            
            upgradeManager = FindObjectOfType<UpgradeManager>();
            if (upgradeManager == null)
            {
                GameObject umObj = new GameObject("UpgradeManager");
                upgradeManager = umObj.AddComponent<UpgradeManager>();
            }
            
            Debug.Log("🚀 SpaceClicker GameManager initialized");
        }
        
        private void StartGame()
        {
            LoadGame();
            Debug.Log("🎮 SpaceClicker game started");
        }
        
        #endregion
        
        #region Game Control
        
        public void PauseGame()
        {
            isGamePaused = true;
            Time.timeScale = 0f;
        }
        
        public void ResumeGame()
        {
            isGamePaused = false;
            Time.timeScale = 1f;
        }
        
        public void RestartGame()
        {
            Time.timeScale = 1f;
            UnityEngine.SceneManagement.SceneManager.LoadScene(0);
        }
        
        #endregion
        
        #region Save/Load
        
        public void SaveGame()
        {
            // Save game data
            if (resourceManager != null)
            {
                resourceManager.SaveData();
            }
            
            if (upgradeManager != null)
            {
                upgradeManager.SaveData();
            }
            
            PlayerPrefs.SetString("LastSave", System.DateTime.Now.ToString());
            PlayerPrefs.Save();
            
            Debug.Log("💾 Game saved");
        }
        
        public void LoadGame()
        {
            // Load game data
            if (resourceManager != null)
            {
                resourceManager.LoadData();
            }
            
            if (upgradeManager != null)
            {
                upgradeManager.LoadData();
            }
            
            Debug.Log("📂 Game loaded");
        }
        
        #endregion
        
        #region Public Methods
        
        public bool IsGamePaused()
        {
            return isGamePaused;
        }
        
        public ResourceManager GetResourceManager()
        {
            return resourceManager;
        }
        
        public UpgradeManager GetUpgradeManager()
        {
            return upgradeManager;
        }

        public bool LoadGame()
        {
            try
            {
                LoadGame();
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load game: {e.Message}");
                return false;
            }
        }

        #endregion
    }
}
