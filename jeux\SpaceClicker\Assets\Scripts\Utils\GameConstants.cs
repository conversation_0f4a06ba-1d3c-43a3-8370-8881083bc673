using System.Numerics;

namespace SpaceClicker.Utils
{
    /// <summary>
    /// Constantes du jeu
    /// </summary>
    public static class GameConstants
    {
        #region Version
        public const string GAME_VERSION = "1.0.0";
        public const string SAVE_VERSION = "1.0";
        #endregion
        
        #region Production
        public const float BASE_ENERGY_PRODUCTION = 1.0f;
        public const float BASE_MINERALS_PRODUCTION = 0.0f;
        public const float BASE_RESEARCH_PRODUCTION = 0.0f;
        
        public const float IDLE_PRODUCTION_INTERVAL = 1.0f;
        public const float MAX_OFFLINE_HOURS = 1.0f;
        #endregion
        
        #region Click Values
        public const float BASE_CLICK_VALUE = 1.0f;
        public const float CLICK_MULTIPLIER_BASE = 1.0f;
        #endregion
        
        #region Upgrade Costs
        public const float UPGRADE_COST_MULTIPLIER = 1.15f;
        public const int MAX_UPGRADE_LEVEL = 100;
        
        // Coûts de base des upgrades
        public static readonly BigInteger SOLAR_PANEL_BASE_COST = new BigInteger(10);
        public static readonly BigInteger FUSION_REACTOR_BASE_COST = new BigInteger(100);
        public static readonly BigInteger MINING_EXTRACTOR_BASE_COST = new BigInteger(50);
        public static readonly BigInteger RESEARCH_SATELLITE_BASE_COST = new BigInteger(200);
        public static readonly BigInteger CLICK_MULTIPLIER_BASE_COST = new BigInteger(25);
        #endregion
        
        #region Prestige
        public static readonly BigInteger PRESTIGE_REQUIREMENT_ENERGY = new BigInteger(1000000);
        public static readonly BigInteger PRESTIGE_REQUIREMENT_MINERALS = new BigInteger(1000000);
        public static readonly BigInteger PRESTIGE_REQUIREMENT_RESEARCH = new BigInteger(1000000);
        
        public const float PRESTIGE_BONUS_PER_POINT = 0.02f; // 2% par point de prestige
        #endregion
        
        #region UI
        public const float AUTO_SAVE_INTERVAL = 30.0f;
        public const float UI_UPDATE_INTERVAL = 0.1f;
        
        // Couleurs des ressources
        public const string ENERGY_COLOR = "#FFFF00";      // Jaune
        public const string MINERALS_COLOR = "#00FFFF";    // Cyan
        public const string RESEARCH_COLOR = "#00FF00";    // Vert
        public const string CURRENCY_COLOR = "#FF00FF";    // Magenta
        #endregion
        
        #region Audio
        public const float MASTER_VOLUME = 1.0f;
        public const float SFX_VOLUME = 0.8f;
        public const float MUSIC_VOLUME = 0.6f;
        #endregion
        
        #region Monetization
        public const float REWARDED_AD_COOLDOWN = 300.0f;  // 5 minutes
        public const float PRODUCTION_BOOST_DURATION = 600.0f; // 10 minutes
        public const float PRODUCTION_BOOST_MULTIPLIER = 2.0f;
        
        public const int MAX_REWARDED_ADS_PER_DAY = 6;
        #endregion
        
        #region Planets
        public static readonly string[] PLANET_NAMES = {
            "Station Orbitale",
            "Lune",
            "Mars", 
            "Europa",
            "Proxima Centauri",
            "Kepler-442b",
            "Trappist-1e"
        };
        
        public static readonly BigInteger[] PLANET_UNLOCK_COSTS = {
            BigInteger.Zero,                    // Station Orbitale (débloquée)
            new BigInteger(1000),              // Lune
            new BigInteger(10000),             // Mars
            new BigInteger(100000),            // Europa
            new BigInteger(1000000),           // Proxima Centauri
            new BigInteger(10000000),          // Kepler-442b
            new BigInteger(100000000)          // Trappist-1e
        };
        #endregion
        
        #region Research
        public static readonly string[] RESEARCH_CATEGORIES = {
            "Efficacité Énergétique",
            "Extraction Avancée",
            "Intelligence Artificielle",
            "Propulsion Spatiale",
            "Nanotechnologie"
        };
        
        public static readonly float[] RESEARCH_TIME_HOURS = {
            0.5f,   // Recherches de base
            1.0f,   // Recherches intermédiaires
            2.0f,   // Recherches avancées
            4.0f,   // Recherches expertes
            8.0f    // Recherches légendaires
        };
        #endregion
        
        #region Achievements
        public static readonly string[] ACHIEVEMENT_CATEGORIES = {
            "Collectionneur",
            "Explorateur", 
            "Ingénieur",
            "Commandant",
            "Scientifique"
        };
        
        public static readonly BigInteger[] COLLECTION_MILESTONES = {
            new BigInteger(1000),
            new BigInteger(10000),
            new BigInteger(100000),
            new BigInteger(1000000),
            new BigInteger(10000000)
        };
        #endregion
        
        #region Performance
        public const int TARGET_FPS = 60;
        public const float LOW_FPS_THRESHOLD = 30.0f;
        public const int MAX_PARTICLES = 100;
        public const float PARTICLE_CLEANUP_INTERVAL = 5.0f;
        #endregion
        
        #region Debug
        #if UNITY_EDITOR
        public const bool ENABLE_DEBUG_CHEATS = true;
        public const bool SHOW_DEBUG_UI = true;
        #else
        public const bool ENABLE_DEBUG_CHEATS = false;
        public const bool SHOW_DEBUG_UI = false;
        #endif
        
        public static readonly BigInteger DEBUG_RESOURCE_AMOUNT = new BigInteger(1000000);
        #endregion
        
        #region File Paths
        public const string SAVE_FILE_NAME = "spaceclicker_save.dat";
        public const string SETTINGS_FILE_NAME = "spaceclicker_settings.json";
        public const string ANALYTICS_FILE_NAME = "spaceclicker_analytics.json";
        #endregion
        
        #region Encryption
        public const string ENCRYPTION_KEY = "SpaceClicker2025SecretKey";
        public const bool USE_ENCRYPTION_DEFAULT = true;
        #endregion
        
        #region Social
        public const string DISCORD_INVITE = "https://discord.gg/spaceclicker";
        public const string TWITTER_HANDLE = "@SpaceClickerGame";
        public const string GITHUB_REPO = "https://github.com/NeethDseven/SpaceClicker";
        #endregion
        
        #region Store
        // IDs des produits IAP
        public const string IAP_STARTER_PACK = "com.spaceclicker.starter_pack";
        public const string IAP_EXPLORER_PACK = "com.spaceclicker.explorer_pack";
        public const string IAP_COMMANDER_PACK = "com.spaceclicker.commander_pack";
        public const string IAP_REMOVE_ADS = "com.spaceclicker.remove_ads";
        
        // IDs des placements publicitaires
        public const string AD_PLACEMENT_REWARDED = "rewardedVideo";
        public const string AD_PLACEMENT_INTERSTITIAL = "interstitial";
        public const string AD_PLACEMENT_BANNER = "banner";
        #endregion
        
        #region Notifications
        public const string NOTIFICATION_CHANNEL_RESOURCES = "resources";
        public const string NOTIFICATION_CHANNEL_RESEARCH = "research";
        public const string NOTIFICATION_CHANNEL_EVENTS = "events";
        
        public const int NOTIFICATION_ID_RESOURCES = 1001;
        public const int NOTIFICATION_ID_RESEARCH = 1002;
        public const int NOTIFICATION_ID_EVENTS = 1003;
        #endregion
    }
}
