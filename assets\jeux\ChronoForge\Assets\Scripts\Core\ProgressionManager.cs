using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace ChronoForge.Core
{
    /// <summary>
    /// Gestionnaire de progression et méta-progression pour ChronoForge
    /// </summary>
    public class ProgressionManager : MonoBehaviour
    {
        [Header("Player Progression")]
        public int playerLevel = 1;
        public int currentExperience = 0;
        public int experienceToNextLevel = 100;
        
        [Header("Currencies")]
        public int arcaneShards = 0;
        public int memoryFragments = 0;
        public int nanoSouls = 0;
        
        [Header("Unlocks")]
        public List<string> unlockedClasses = new List<string>();
        public List<string> unlockedWeapons = new List<string>();
        public List<string> unlockedArtifacts = new List<string>();
        public List<string> unlockedBiomes = new List<string>();
        
        [Header("Statistics")]
        public int totalRuns = 0;
        public int successfulRuns = 0;
        public int totalDeaths = 0;
        public float totalPlayTime = 0f;
        public int totalEnemiesKilled = 0;
        public int totalBossesDefeated = 0;
        
        [Header("Achievements")]
        public List<Achievement> achievements = new List<Achievement>();
        public List<string> unlockedAchievements = new List<string>();
        
        // Events
        public static System.Action<int> OnLevelUp;
        public static System.Action<int, int> OnExperienceGained;
        public static System.Action<string, int> OnCurrencyChanged;
        public static System.Action<string> OnClassUnlocked;
        public static System.Action<string> OnWeaponUnlocked;
        public static System.Action<string> OnAchievementUnlocked;
        
        // Private fields
        private List<RunData> runHistory = new List<RunData>();
        private Dictionary<string, int> currencies = new Dictionary<string, int>();
        private float sessionStartTime;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeProgression();
        }
        
        private void Start()
        {
            sessionStartTime = Time.time;
            LoadDefaultUnlocks();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                SaveProgression();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                SaveProgression();
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeProgression()
        {
            // Initialize currencies dictionary
            currencies["ArcaneShards"] = arcaneShards;
            currencies["MemoryFragments"] = memoryFragments;
            currencies["NanoSouls"] = nanoSouls;
            
            // Initialize achievements
            InitializeAchievements();
            
            UnityEngine.Debug.Log("📈 ProgressionManager initialized");
        }
        
        private void LoadDefaultUnlocks()
        {
            // Ensure player has at least one class unlocked
            if (unlockedClasses.Count == 0)
            {
                UnlockClass("CyberWarrior");
            }
            
            // Ensure player has basic weapons
            if (unlockedWeapons.Count == 0)
            {
                UnlockWeapon("PlasmaSword");
                UnlockWeapon("BasicBlaster");
            }
            
            // Unlock first biome
            if (unlockedBiomes.Count == 0)
            {
                UnlockBiome("RuinedCitadel");
            }
        }
        
        #endregion
        
        #region Experience and Leveling
        
        public void AddExperience(int amount)
        {
            if (amount <= 0) return;
            
            currentExperience += amount;
            OnExperienceGained?.Invoke(amount, currentExperience);
            
            UnityEngine.Debug.Log($"📚 Gained {amount} experience (Total: {currentExperience})");
            
            // Check for level up
            CheckLevelUp();
        }
        
        private void CheckLevelUp()
        {
            while (currentExperience >= experienceToNextLevel)
            {
                LevelUp();
            }
        }
        
        private void LevelUp()
        {
            currentExperience -= experienceToNextLevel;
            playerLevel++;
            
            // Calculate next level requirement (exponential growth)
            experienceToNextLevel = Mathf.RoundToInt(100 * Mathf.Pow(1.2f, playerLevel - 1));
            
            UnityEngine.Debug.Log($"🎉 Level up! Now level {playerLevel}");
            
            // Award level up rewards
            AwardLevelUpRewards();
            
            OnLevelUp?.Invoke(playerLevel);
        }
        
        private void AwardLevelUpRewards()
        {
            // Award currencies based on level
            int shardsReward = playerLevel * 5;
            int fragmentsReward = playerLevel * 3;
            
            AddCurrency("ArcaneShards", shardsReward);
            AddCurrency("MemoryFragments", fragmentsReward);
            
            // Check for unlock rewards
            CheckLevelUnlocks();
        }
        
        private void CheckLevelUnlocks()
        {
            // Unlock new classes at specific levels
            switch (playerLevel)
            {
                case 3:
                    UnlockClass("TechnoMage");
                    break;
                case 5:
                    UnlockClass("DimensionalRogue");
                    break;
                case 10:
                    UnlockBiome("CyberCatacombs");
                    break;
                case 15:
                    UnlockBiome("QuantumLaboratory");
                    break;
                case 20:
                    UnlockBiome("ChronoNexus");
                    break;
            }
        }
        
        #endregion
        
        #region Currency Management
        
        public void AddCurrency(string currencyType, int amount)
        {
            if (amount <= 0) return;
            
            if (currencies.ContainsKey(currencyType))
            {
                currencies[currencyType] += amount;
            }
            else
            {
                currencies[currencyType] = amount;
            }
            
            // Update individual currency fields
            UpdateCurrencyFields();
            
            Debug.log($"💰 Added {amount} {currencyType} (Total: {currencies[currencyType]})");
            
            OnCurrencyChanged?.Invoke(currencyType, currencies[currencyType]);
        }
        
        public bool SpendCurrency(string currencyType, int amount)
        {
            if (!CanAffordCurrency(currencyType, amount))
            {
                return false;
            }
            
            currencies[currencyType] -= amount;
            UpdateCurrencyFields();
            
            UnityEngine.Debug.Log($"💸 Spent {amount} {currencyType} (Remaining: {currencies[currencyType]})");
            
            OnCurrencyChanged?.Invoke(currencyType, currencies[currencyType]);
            return true;
        }
        
        public bool CanAffordCurrency(string currencyType, int amount)
        {
            return currencies.ContainsKey(currencyType) && currencies[currencyType] >= amount;
        }
        
        public int GetCurrency(string currencyType)
        {
            return currencies.ContainsKey(currencyType) ? currencies[currencyType] : 0;
        }
        
        private void UpdateCurrencyFields()
        {
            arcaneShards = GetCurrency("ArcaneShards");
            memoryFragments = GetCurrency("MemoryFragments");
            nanoSouls = GetCurrency("NanoSouls");
        }
        
        #endregion
        
        #region Unlocks Management
        
        public void UnlockClass(string className)
        {
            if (!unlockedClasses.Contains(className))
            {
                unlockedClasses.Add(className);
                UnityEngine.Debug.Log($"🎭 Class unlocked: {className}");
                OnClassUnlocked?.Invoke(className);
                
                CheckAchievements();
            }
        }
        
        public void UnlockWeapon(string weaponName)
        {
            if (!unlockedWeapons.Contains(weaponName))
            {
                unlockedWeapons.Add(weaponName);
                UnityEngine.Debug.Log($"⚔️ Weapon unlocked: {weaponName}");
                OnWeaponUnlocked?.Invoke(weaponName);
                
                CheckAchievements();
            }
        }
        
        public void UnlockArtifact(string artifactName)
        {
            if (!unlockedArtifacts.Contains(artifactName))
            {
                unlockedArtifacts.Add(artifactName);
                UnityEngine.Debug.Log($"🔮 Artifact unlocked: {artifactName}");
                
                CheckAchievements();
            }
        }
        
        public void UnlockBiome(string biomeName)
        {
            if (!unlockedBiomes.Contains(biomeName))
            {
                unlockedBiomes.Add(biomeName);
                UnityEngine.Debug.Log($"🌍 Biome unlocked: {biomeName}");
                
                CheckAchievements();
            }
        }
        
        public bool IsClassUnlocked(string className)
        {
            return unlockedClasses.Contains(className);
        }
        
        public bool IsWeaponUnlocked(string weaponName)
        {
            return unlockedWeapons.Contains(weaponName);
        }
        
        public bool IsArtifactUnlocked(string artifactName)
        {
            return unlockedArtifacts.Contains(artifactName);
        }
        
        public bool IsBiomeUnlocked(string biomeName)
        {
            return unlockedBiomes.Contains(biomeName);
        }
        
        #endregion
        
        #region Statistics
        
        public void AddRunToHistory(RunData runData)
        {
            runHistory.Add(runData);
            totalRuns++;
            
            if (runData.completed)
            {
                successfulRuns++;
            }
            else
            {
                totalDeaths++;
            }
            
            totalEnemiesKilled += runData.enemiesKilled;
            totalBossesDefeated += runData.bossesDefeated;
            
            UnityEngine.Debug.Log($"📊 Run added to history. Total runs: {totalRuns}");
            
            CheckAchievements();
        }
        
        public void UpdatePlayTime()
        {
            totalPlayTime += Time.time - sessionStartTime;
            sessionStartTime = Time.time;
        }
        
        public float GetWinRate()
        {
            return totalRuns > 0 ? (float)successfulRuns / totalRuns : 0f;
        }
        
        public RunData GetBestRun()
        {
            return runHistory.OrderByDescending(run => run.score).FirstOrDefault();
        }
        
        public List<RunData> GetRecentRuns(int count = 10)
        {
            return runHistory.TakeLast(count).ToList();
        }
        
        #endregion
        
        #region Achievements
        
        private void InitializeAchievements()
        {
            achievements.Clear();
            
            // Add achievements
            achievements.Add(new Achievement("FirstSteps", "First Steps", "Complete your first run", false));
            achievements.Add(new Achievement("Survivor", "Survivor", "Complete 10 runs", false));
            achievements.Add(new Achievement("Veteran", "Veteran", "Complete 50 runs", false));
            achievements.Add(new Achievement("Slayer", "Slayer", "Kill 100 enemies", false));
            achievements.Add(new Achievement("BossHunter", "Boss Hunter", "Defeat 10 bosses", false));
            achievements.Add(new Achievement("Collector", "Collector", "Unlock 5 weapons", false));
            achievements.Add(new Achievement("Master", "Master", "Reach level 20", false));
            achievements.Add(new Achievement("Explorer", "Explorer", "Unlock all biomes", false));
        }
        
        private void CheckAchievements()
        {
            foreach (var achievement in achievements)
            {
                if (achievement.unlocked || unlockedAchievements.Contains(achievement.id))
                    continue;
                
                bool shouldUnlock = false;
                
                switch (achievement.id)
                {
                    case "FirstSteps":
                        shouldUnlock = successfulRuns >= 1;
                        break;
                    case "Survivor":
                        shouldUnlock = successfulRuns >= 10;
                        break;
                    case "Veteran":
                        shouldUnlock = successfulRuns >= 50;
                        break;
                    case "Slayer":
                        shouldUnlock = totalEnemiesKilled >= 100;
                        break;
                    case "BossHunter":
                        shouldUnlock = totalBossesDefeated >= 10;
                        break;
                    case "Collector":
                        shouldUnlock = unlockedWeapons.Count >= 5;
                        break;
                    case "Master":
                        shouldUnlock = playerLevel >= 20;
                        break;
                    case "Explorer":
                        shouldUnlock = unlockedBiomes.Count >= 4;
                        break;
                }
                
                if (shouldUnlock)
                {
                    UnlockAchievement(achievement);
                }
            }
        }
        
        private void UnlockAchievement(Achievement achievement)
        {
            achievement.unlocked = true;
            unlockedAchievements.Add(achievement.id);
            
            UnityEngine.Debug.Log($"🏆 Achievement unlocked: {achievement.name}");
            OnAchievementUnlocked?.Invoke(achievement.id);
            
            // Award achievement rewards
            AwardAchievementReward(achievement.id);
        }
        
        private void AwardAchievementReward(string achievementId)
        {
            // Award currencies based on achievement
            switch (achievementId)
            {
                case "FirstSteps":
                    AddCurrency("ArcaneShards", 50);
                    break;
                case "Survivor":
                    AddCurrency("MemoryFragments", 100);
                    break;
                case "Veteran":
                    AddCurrency("NanoSouls", 10);
                    break;
                case "Slayer":
                    AddCurrency("ArcaneShards", 200);
                    break;
                case "BossHunter":
                    AddCurrency("NanoSouls", 5);
                    break;
                case "Collector":
                    AddCurrency("MemoryFragments", 150);
                    break;
                case "Master":
                    AddCurrency("NanoSouls", 20);
                    break;
                case "Explorer":
                    AddCurrency("ArcaneShards", 500);
                    break;
            }
        }
        
        #endregion
        
        #region Save/Load
        
        public void SaveProgression()
        {
            UpdatePlayTime();
            
            // This would typically save to PlayerPrefs or a file
            // For now, we'll use PlayerPrefs for simplicity
            
            PlayerPrefs.SetInt("PlayerLevel", playerLevel);
            PlayerPrefs.SetInt("CurrentExperience", currentExperience);
            PlayerPrefs.SetInt("ArcaneShards", arcaneShards);
            PlayerPrefs.SetInt("MemoryFragments", memoryFragments);
            PlayerPrefs.SetInt("NanoSouls", nanoSouls);
            PlayerPrefs.SetInt("TotalRuns", totalRuns);
            PlayerPrefs.SetInt("SuccessfulRuns", successfulRuns);
            PlayerPrefs.SetFloat("TotalPlayTime", totalPlayTime);
            
            // Save unlocks (simplified)
            PlayerPrefs.SetString("UnlockedClasses", string.Join(",", unlockedClasses));
            PlayerPrefs.SetString("UnlockedWeapons", string.Join(",", unlockedWeapons));
            PlayerPrefs.SetString("UnlockedBiomes", string.Join(",", unlockedBiomes));
            PlayerPrefs.SetString("UnlockedAchievements", string.Join(",", unlockedAchievements));
            
            PlayerPrefs.Save();
            UnityEngine.Debug.Log("💾 Progression saved");
        }
        
        public void LoadProgression()
        {
            playerLevel = PlayerPrefs.GetInt("PlayerLevel", 1);
            currentExperience = PlayerPrefs.GetInt("CurrentExperience", 0);
            arcaneShards = PlayerPrefs.GetInt("ArcaneShards", 0);
            memoryFragments = PlayerPrefs.GetInt("MemoryFragments", 0);
            nanoSouls = PlayerPrefs.GetInt("NanoSouls", 0);
            totalRuns = PlayerPrefs.GetInt("TotalRuns", 0);
            successfulRuns = PlayerPrefs.GetInt("SuccessfulRuns", 0);
            totalPlayTime = PlayerPrefs.GetFloat("TotalPlayTime", 0f);
            
            // Load unlocks
            string classesString = PlayerPrefs.GetString("UnlockedClasses", "");
            if (!string.IsNullOrEmpty(classesString))
            {
                unlockedClasses = classesString.Split(',').ToList();
            }
            
            string weaponsString = PlayerPrefs.GetString("UnlockedWeapons", "");
            if (!string.IsNullOrEmpty(weaponsString))
            {
                unlockedWeapons = weaponsString.Split(',').ToList();
            }
            
            string biomesString = PlayerPrefs.GetString("UnlockedBiomes", "");
            if (!string.IsNullOrEmpty(biomesString))
            {
                unlockedBiomes = biomesString.Split(',').ToList();
            }
            
            string achievementsString = PlayerPrefs.GetString("UnlockedAchievements", "");
            if (!string.IsNullOrEmpty(achievementsString))
            {
                unlockedAchievements = achievementsString.Split(',').ToList();
            }
            
            // Update currencies dictionary
            UpdateCurrencyFields();
            
            UnityEngine.Debug.Log("📂 Progression loaded");
        }
        
        #endregion
    }
    
    /// <summary>
    /// Données d'un achievement
    /// </summary>
    [System.Serializable]
    public class Achievement
    {
        public string id;
        public string name;
        public string description;
        public bool unlocked;
        
        public Achievement(string id, string name, string description, bool unlocked = false)
        {
            this.id = id;
            this.name = name;
            this.description = description;
            this.unlocked = unlocked;
        }
    }
}
