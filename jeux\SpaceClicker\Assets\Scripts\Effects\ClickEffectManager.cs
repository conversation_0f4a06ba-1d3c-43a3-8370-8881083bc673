using UnityEngine;
using System.Collections.Generic;
using SpaceClicker.Core;
using SpaceClicker.Utils;

namespace SpaceClicker.Effects
{
    /// <summary>
    /// Gestionnaire des effets visuels de clic
    /// </summary>
    public class ClickEffectManager : MonoBehaviour
    {
        [Header("Effect Prefabs")]
        public GameObject clickParticlesPrefab;
        public GameObject resourceGainTextPrefab;
        public GameObject upgradeEffectPrefab;
        
        [Header("Settings")]
        public int maxParticles = 50;
        public float particleLifetime = 2f;
        public float textLifetime = 1.5f;
        
        [Header("Audio")]
        public AudioSource audioSource;
        public AudioClip clickSound;
        public AudioClip upgradeSound;
        public AudioClip resourceGainSound;
        
        // Object pooling
        private Queue<GameObject> particlePool;
        private Queue<GameObject> textPool;
        private List<GameObject> activeEffects;
        
        // Singleton
        public static ClickEffectManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePools();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            // Subscribe to events
            ResourceManager.OnResourceGained += OnResourceGained;
            UpgradeManager.OnUpgradePurchased += OnUpgradePurchased;
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            ResourceManager.OnResourceGained -= OnResourceGained;
            UpgradeManager.OnUpgradePurchased -= OnUpgradePurchased;
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializePools()
        {
            particlePool = new Queue<GameObject>();
            textPool = new Queue<GameObject>();
            activeEffects = new List<GameObject>();
            
            // Pre-instantiate particle effects
            if (clickParticlesPrefab != null)
            {
                for (int i = 0; i < maxParticles / 2; i++)
                {
                    GameObject particle = Instantiate(clickParticlesPrefab);
                    particle.SetActive(false);
                    particlePool.Enqueue(particle);
                }
            }
            
            // Pre-instantiate text effects
            if (resourceGainTextPrefab != null)
            {
                for (int i = 0; i < maxParticles / 2; i++)
                {
                    GameObject text = Instantiate(resourceGainTextPrefab);
                    text.SetActive(false);
                    textPool.Enqueue(text);
                }
            }
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnResourceGained(ResourceType type, System.Numerics.BigInteger amount)
        {
            // Play resource gain effect at the appropriate module position
            Vector3 modulePosition = GetModulePosition(type);
            PlayResourceGainEffect(modulePosition, type, amount);
        }
        
        private void OnUpgradePurchased(UpgradeData upgrade)
        {
            // Play upgrade effect
            PlayUpgradeEffect();
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Play click effect at specified position
        /// </summary>
        public void PlayClickEffect(Vector3 position, ResourceType resourceType)
        {
            // Play particle effect
            PlayParticleEffect(position, resourceType);
            
            // Play click sound
            PlaySound(clickSound);
        }
        
        /// <summary>
        /// Play resource gain effect
        /// </summary>
        public void PlayResourceGainEffect(Vector3 position, ResourceType resourceType, System.Numerics.BigInteger amount)
        {
            // Play floating text
            PlayFloatingText(position, $"+{amount.ToCompactString()}", GetResourceColor(resourceType));
            
            // Play particle burst
            PlayParticleEffect(position, resourceType);
            
            // Play sound
            PlaySound(resourceGainSound);
        }
        
        /// <summary>
        /// Play upgrade purchase effect
        /// </summary>
        public void PlayUpgradeEffect()
        {
            // Play screen-wide effect
            if (upgradeEffectPrefab != null)
            {
                GameObject effect = Instantiate(upgradeEffectPrefab);
                Destroy(effect, 2f);
            }
            
            // Play upgrade sound
            PlaySound(upgradeSound);
        }
        
        #endregion
        
        #region Private Methods
        
        private void PlayParticleEffect(Vector3 position, ResourceType resourceType)
        {
            GameObject particle = GetPooledParticle();
            if (particle == null) return;
            
            // Position the effect
            particle.transform.position = position;
            particle.SetActive(true);
            
            // Configure particle system
            ParticleSystem ps = particle.GetComponent<ParticleSystem>();
            if (ps != null)
            {
                var main = ps.main;
                main.startColor = GetResourceColor(resourceType);
                ps.Play();
            }
            
            // Add to active effects
            activeEffects.Add(particle);
            
            // Return to pool after lifetime
            StartCoroutine(ReturnParticleToPool(particle, particleLifetime));
        }
        
        private void PlayFloatingText(Vector3 position, string text, Color color)
        {
            GameObject textObj = GetPooledText();
            if (textObj == null) return;
            
            // Position the text
            textObj.transform.position = position;
            textObj.SetActive(true);
            
            // Configure text
            TMPro.TextMeshProUGUI textComponent = textObj.GetComponent<TMPro.TextMeshProUGUI>();
            if (textComponent != null)
            {
                textComponent.text = text;
                textComponent.color = color;
            }
            
            // Animate the text
            AnimateFloatingText(textObj);
            
            // Add to active effects
            activeEffects.Add(textObj);
            
            // Return to pool after lifetime
            StartCoroutine(ReturnTextToPool(textObj, textLifetime));
        }
        
        private void AnimateFloatingText(GameObject textObj)
        {
            Vector3 startPos = textObj.transform.position;
            Vector3 endPos = startPos + Vector3.up * 100f; // Move up 100 pixels
            
            // Move up animation
            LeanTween.move(textObj, endPos, textLifetime)
                .setEase(LeanTweenType.easeOutQuad);
            
            // Fade out animation
            TMPro.TextMeshProUGUI textComponent = textObj.GetComponent<TMPro.TextMeshProUGUI>();
            if (textComponent != null)
            {
                LeanTween.value(textObj, 1f, 0f, textLifetime * 0.7f)
                    .setDelay(textLifetime * 0.3f)
                    .setOnUpdate((float alpha) => {
                        Color color = textComponent.color;
                        color.a = alpha;
                        textComponent.color = color;
                    });
            }
        }
        
        private GameObject GetPooledParticle()
        {
            if (particlePool.Count > 0)
            {
                return particlePool.Dequeue();
            }
            
            // Create new if pool is empty
            if (clickParticlesPrefab != null)
            {
                return Instantiate(clickParticlesPrefab);
            }
            
            return null;
        }
        
        private GameObject GetPooledText()
        {
            if (textPool.Count > 0)
            {
                return textPool.Dequeue();
            }
            
            // Create new if pool is empty
            if (resourceGainTextPrefab != null)
            {
                return Instantiate(resourceGainTextPrefab);
            }
            
            return null;
        }
        
        private System.Collections.IEnumerator ReturnParticleToPool(GameObject particle, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (particle != null)
            {
                particle.SetActive(false);
                activeEffects.Remove(particle);
                particlePool.Enqueue(particle);
            }
        }
        
        private System.Collections.IEnumerator ReturnTextToPool(GameObject text, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (text != null)
            {
                text.SetActive(false);
                activeEffects.Remove(text);
                textPool.Enqueue(text);
            }
        }
        
        private Color GetResourceColor(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return ColorUtility.TryParseHtmlString(GameConstants.ENERGY_COLOR, out Color energyColor) ? energyColor : Color.yellow;
                case ResourceType.Minerals:
                    return ColorUtility.TryParseHtmlString(GameConstants.MINERALS_COLOR, out Color mineralsColor) ? mineralsColor : Color.cyan;
                case ResourceType.ResearchData:
                    return ColorUtility.TryParseHtmlString(GameConstants.RESEARCH_COLOR, out Color researchColor) ? researchColor : Color.green;
                case ResourceType.SpaceCurrency:
                    return ColorUtility.TryParseHtmlString(GameConstants.CURRENCY_COLOR, out Color currencyColor) ? currencyColor : Color.magenta;
                default:
                    return Color.white;
            }
        }
        
        private Vector3 GetModulePosition(ResourceType resourceType)
        {
            // TODO: Get actual module positions from UI
            // For now, return screen center with offset
            Vector3 screenCenter = new Vector3(Screen.width / 2, Screen.height / 2, 0);
            
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return screenCenter + Vector3.left * 200;
                case ResourceType.Minerals:
                    return screenCenter;
                case ResourceType.ResearchData:
                    return screenCenter + Vector3.right * 200;
                default:
                    return screenCenter;
            }
        }
        
        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        #endregion
        
        #region Cleanup
        
        /// <summary>
        /// Clean up all active effects
        /// </summary>
        public void CleanupEffects()
        {
            foreach (GameObject effect in activeEffects)
            {
                if (effect != null)
                {
                    effect.SetActive(false);
                }
            }
            
            activeEffects.Clear();
        }
        
        #endregion
    }
}
