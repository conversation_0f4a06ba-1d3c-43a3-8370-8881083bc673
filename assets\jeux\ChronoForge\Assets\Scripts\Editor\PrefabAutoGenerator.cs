using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using TMPro;
using System.IO;
using ChronoForge.Core;
using ChronoForge.Player;
using ChronoForge.UI;
using ChronoForge.Audio;
using ChronoForge.Effects;
using ChronoForge.Procedural;

namespace ChronoForge.Editor
{
    public class PrefabAutoGenerator : EditorWindow
    {
        private bool createFolders = true;
        private bool createPlayerPrefab = true;
        private bool createGameManagerPrefab = true;
        private bool createUICanvasPrefab = true;
        private bool createAudioManagerPrefab = true;
        private bool createEffectsManagerPrefab = true;
        private bool createLevelGeneratorPrefab = true;

        [MenuItem("ChronoForge/Auto Generate Prefabs")]
        public static void ShowWindow()
        {
            GetWindow<PrefabAutoGenerator>("ChronoForge Prefab Generator");
        }

        private void OnGUI()
        {
            GUILayout.Label("🚀 ChronoForge Prefab Auto-Generator", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("Sélectionnez les prefabs à créer :", EditorStyles.label);
            
            createFolders = EditorGUILayout.Toggle("Créer la structure de dossiers", createFolders);
            GUILayout.Space(5);
            
            createPlayerPrefab = EditorGUILayout.Toggle("🎮 Player Prefab", createPlayerPrefab);
            createGameManagerPrefab = EditorGUILayout.Toggle("⚙️ GameManager Prefab", createGameManagerPrefab);
            createUICanvasPrefab = EditorGUILayout.Toggle("🖼️ UI Canvas Prefab", createUICanvasPrefab);
            createAudioManagerPrefab = EditorGUILayout.Toggle("🔊 Audio Manager Prefab", createAudioManagerPrefab);
            createEffectsManagerPrefab = EditorGUILayout.Toggle("✨ Effects Manager Prefab", createEffectsManagerPrefab);
            createLevelGeneratorPrefab = EditorGUILayout.Toggle("🌍 Level Generator Prefab", createLevelGeneratorPrefab);

            GUILayout.Space(20);

            if (GUILayout.Button("🎯 GÉNÉRER TOUS LES PREFABS", GUILayout.Height(40)))
            {
                GenerateAllPrefabs();
            }

            GUILayout.Space(10);
            
            if (GUILayout.Button("🗂️ Créer seulement les dossiers", GUILayout.Height(30)))
            {
                CreateFolderStructure();
            }
        }

        private void GenerateAllPrefabs()
        {
            Debug.Log("🚀 Début de la génération automatique des prefabs ChronoForge...");

            if (createFolders)
                CreateFolderStructure();

            if (createPlayerPrefab)
                CreatePlayerPrefab();

            if (createGameManagerPrefab)
                CreateGameManagerPrefab();

            if (createUICanvasPrefab)
                CreateUICanvasPrefab();

            if (createAudioManagerPrefab)
                CreateAudioManagerPrefab();

            if (createEffectsManagerPrefab)
                CreateEffectsManagerPrefab();

            if (createLevelGeneratorPrefab)
                CreateLevelGeneratorPrefab();

            AssetDatabase.Refresh();
            Debug.Log("✅ Génération des prefabs terminée avec succès !");
            
            EditorUtility.DisplayDialog("ChronoForge", "🎉 Tous les prefabs ont été générés avec succès !\n\nVérifiez le dossier Assets/Prefabs/", "OK");
        }

        private void CreateFolderStructure()
        {
            string[] folders = {
                "Assets/Prefabs",
                "Assets/Prefabs/Core",
                "Assets/Prefabs/Player",
                "Assets/Prefabs/UI",
                "Assets/Prefabs/Audio",
                "Assets/Prefabs/Effects",
                "Assets/Prefabs/World"
            };

            foreach (string folder in folders)
            {
                if (!AssetDatabase.IsValidFolder(folder))
                {
                    string parentFolder = Path.GetDirectoryName(folder).Replace('\\', '/');
                    string folderName = Path.GetFileName(folder);
                    AssetDatabase.CreateFolder(parentFolder, folderName);
                    Debug.Log($"📁 Dossier créé : {folder}");
                }
            }
        }

        private void CreatePlayerPrefab()
        {
            Debug.Log("🎮 Création du Player Prefab...");

            // Créer le GameObject principal
            GameObject player = new GameObject("Player");
            
            // Ajouter SpriteRenderer
            SpriteRenderer spriteRenderer = player.AddComponent<SpriteRenderer>();
            spriteRenderer.sortingLayerName = "Default";
            spriteRenderer.sortingOrder = 0;

            // Ajouter Rigidbody2D
            Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
            rb.freezeRotation = true;
            rb.drag = 5f;

            // Ajouter Collider
            CapsuleCollider2D collider = player.AddComponent<CapsuleCollider2D>();
            collider.size = new Vector2(0.8f, 1.6f);

            // Ajouter les scripts
            player.AddComponent<PlayerController>();
            player.AddComponent<PlayerCombat>();
            player.AddComponent<ChronoForge.Player.HealthSystem>();
            player.AddComponent<PlayerStats>();

            // Créer AttackPoint enfant
            GameObject attackPoint = new GameObject("AttackPoint");
            attackPoint.transform.SetParent(player.transform);
            attackPoint.transform.localPosition = new Vector3(0, -0.5f, 0);

            // Sauvegarder le prefab
            string prefabPath = "Assets/Prefabs/Player/Player.prefab";
            PrefabUtility.SaveAsPrefabAsset(player, prefabPath);
            DestroyImmediate(player);

            Debug.Log("✅ Player Prefab créé : " + prefabPath);
        }

        private void CreateGameManagerPrefab()
        {
            Debug.Log("⚙️ Création du GameManager Prefab...");

            GameObject gameManager = new GameObject("GameManager");

            // Ajouter tous les scripts de management
            gameManager.AddComponent<GameManager>();
            gameManager.AddComponent<RunManager>();
            gameManager.AddComponent<ProgressionManager>();
            gameManager.AddComponent<GameSettings>();
            gameManager.AddComponent<SaveSystem>();
            gameManager.AddComponent<GameIntegration>();

            string prefabPath = "Assets/Prefabs/Core/GameManager.prefab";
            PrefabUtility.SaveAsPrefabAsset(gameManager, prefabPath);
            DestroyImmediate(gameManager);

            Debug.Log("✅ GameManager Prefab créé : " + prefabPath);
        }

        private void CreateUICanvasPrefab()
        {
            Debug.Log("🖼️ Création du UI Canvas Prefab...");

            // Créer le Canvas principal
            GameObject canvas = new GameObject("MainCanvas");
            Canvas canvasComponent = canvas.AddComponent<Canvas>();
            canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasComponent.sortingOrder = 0;

            // Ajouter CanvasScaler
            CanvasScaler scaler = canvas.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.matchWidthOrHeight = 0.5f;

            // Ajouter GraphicRaycaster
            canvas.AddComponent<GraphicRaycaster>();

            // Ajouter UIManager
            canvas.AddComponent<UIManager>();

            // Créer les panels enfants
            CreateUIPanel(canvas, "HUD", true);
            CreateUIPanel(canvas, "MenuPanel", false);
            CreateUIPanel(canvas, "InventoryPanel", false);
            CreateUIPanel(canvas, "SettingsPanel", false);
            CreateUIPanel(canvas, "NotificationPanel", false);

            string prefabPath = "Assets/Prefabs/UI/MainCanvas.prefab";
            PrefabUtility.SaveAsPrefabAsset(canvas, prefabPath);
            DestroyImmediate(canvas);

            Debug.Log("✅ UI Canvas Prefab créé : " + prefabPath);
        }

        private void CreateUIPanel(GameObject parent, string panelName, bool isActive)
        {
            GameObject panel = new GameObject(panelName);
            panel.transform.SetParent(parent.transform, false);

            RectTransform rectTransform = panel.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            Image image = panel.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.5f);

            panel.SetActive(isActive);

            // Ajouter les controllers spécifiques
            switch (panelName)
            {
                case "HUD":
                    panel.AddComponent<HUDController>();
                    break;
                case "InventoryPanel":
                    panel.AddComponent<InventoryController>();
                    break;
                case "SettingsPanel":
                    panel.AddComponent<SettingsController>();
                    break;
                case "NotificationPanel":
                    panel.AddComponent<NotificationController>();
                    break;
            }
        }

        private void CreateAudioManagerPrefab()
        {
            Debug.Log("🔊 Création du Audio Manager Prefab...");

            GameObject audioManager = new GameObject("AudioManager");

            // Ajouter le script principal
            audioManager.AddComponent<AudioManager>();

            // Créer les AudioSources enfants
            CreateAudioSource(audioManager, "MusicSource", true, 0.7f);
            CreateAudioSource(audioManager, "SFXSource", false, 1.0f);
            CreateAudioSource(audioManager, "AmbientSource", true, 0.5f);
            CreateAudioSource(audioManager, "UISource", false, 0.8f);

            string prefabPath = "Assets/Prefabs/Audio/AudioManager.prefab";
            PrefabUtility.SaveAsPrefabAsset(audioManager, prefabPath);
            DestroyImmediate(audioManager);

            Debug.Log("✅ Audio Manager Prefab créé : " + prefabPath);
        }

        private void CreateAudioSource(GameObject parent, string sourceName, bool loop, float volume)
        {
            GameObject sourceObj = new GameObject(sourceName);
            sourceObj.transform.SetParent(parent.transform);

            AudioSource source = sourceObj.AddComponent<AudioSource>();
            source.playOnAwake = false;
            source.loop = loop;
            source.volume = volume;
            source.priority = 128;
        }

        private void CreateEffectsManagerPrefab()
        {
            Debug.Log("✨ Création du Effects Manager Prefab...");

            GameObject effectsManager = new GameObject("EffectsManager");
            effectsManager.AddComponent<EffectsManager>();

            string prefabPath = "Assets/Prefabs/Effects/EffectsManager.prefab";
            PrefabUtility.SaveAsPrefabAsset(effectsManager, prefabPath);
            DestroyImmediate(effectsManager);

            Debug.Log("✅ Effects Manager Prefab créé : " + prefabPath);
        }

        private void CreateLevelGeneratorPrefab()
        {
            Debug.Log("🌍 Création du Level Generator Prefab...");

            GameObject levelGenerator = new GameObject("LevelGenerator");
            levelGenerator.AddComponent<LevelGenerator>();

            string prefabPath = "Assets/Prefabs/World/LevelGenerator.prefab";
            PrefabUtility.SaveAsPrefabAsset(levelGenerator, prefabPath);
            DestroyImmediate(levelGenerator);

            Debug.Log("✅ Level Generator Prefab créé : " + prefabPath);
        }
    }
}
