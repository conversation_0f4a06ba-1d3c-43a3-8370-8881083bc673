using UnityEngine;
using UnityEngine.UI;
using TMPro;
using ChronoForge.Core;
using ChronoForge.Audio;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur du menu pause pour ChronoForge
    /// </summary>
    public class PauseMenuController : MonoBehaviour
    {
        [Header("UI Elements")]
        public Button resumeButton;
        public Button settingsButton;
        public Button mainMenuButton;
        public Button quitButton;
        
        [Header("Info Display")]
        public TextMeshProUGUI runInfoText;
        public TextMeshProUGUI timeText;
        public TextMeshProUGUI statsText;
        
        [Header("Quick Settings")]
        public Slider masterVolumeSlider;
        public Slider musicVolumeSlider;
        public Slider sfxVolumeSlider;
        public Toggle fullscreenToggle;
        
        [Header("Animation")]
        public float fadeInDuration = 0.3f;
        public AnimationCurve fadeInCurve = AnimationCurve.EaseOut(0, 0, 1, 1);
        
        // Components
        private UIManager uiManager;
        private GameManager gameManager;
        private AudioManager audioManager;
        private RunManager runManager;
        private CanvasGroup canvasGroup;
        
        // State
        private bool isInitialized = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void OnEnable()
        {
            if (isInitialized)
            {
                RefreshPauseMenu();
                StartCoroutine(FadeInMenu());
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeComponents()
        {
            // Get components
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            
            // Find managers
            uiManager = FindObjectOfType<UIManager>();
            gameManager = GameManager.Instance;
            audioManager = AudioManager.Instance;
            runManager = FindObjectOfType<RunManager>();
            
            // Setup buttons
            SetupButtons();
            
            // Setup sliders
            SetupSliders();
            
            // Setup toggles
            SetupToggles();
            
            isInitialized = true;
            
            Debug.Log("⏸️ PauseMenuController initialized");
        }
        
        private void SetupButtons()
        {
            // Resume button
            if (resumeButton != null)
            {
                resumeButton.onClick.AddListener(OnResumeClicked);
            }
            
            // Settings button
            if (settingsButton != null)
            {
                settingsButton.onClick.AddListener(OnSettingsClicked);
            }
            
            // Main menu button
            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.AddListener(OnMainMenuClicked);
            }
            
            // Quit button
            if (quitButton != null)
            {
                quitButton.onClick.AddListener(OnQuitClicked);
            }
        }
        
        private void SetupSliders()
        {
            // Master volume slider
            if (masterVolumeSlider != null && audioManager != null)
            {
                masterVolumeSlider.value = audioManager.GetMasterVolume();
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            }
            
            // Music volume slider
            if (musicVolumeSlider != null && audioManager != null)
            {
                musicVolumeSlider.value = audioManager.GetMusicVolume();
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            }
            
            // SFX volume slider
            if (sfxVolumeSlider != null && audioManager != null)
            {
                sfxVolumeSlider.value = audioManager.GetSFXVolume();
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
            }
        }
        
        private void SetupToggles()
        {
            // Fullscreen toggle
            if (fullscreenToggle != null)
            {
                fullscreenToggle.isOn = Screen.fullScreen;
                fullscreenToggle.onValueChanged.AddListener(OnFullscreenToggled);
            }
        }
        
        #endregion
        
        #region Menu Updates
        
        public void RefreshPauseMenu()
        {
            UpdateRunInfo();
            UpdateTimeInfo();
            UpdateStatsInfo();
            UpdateVolumeSliders();
        }
        
        private void UpdateRunInfo()
        {
            if (runInfoText == null || runManager == null) return;
            
            string runInfo = $"Run #{runManager.currentRunNumber}\n";
            runInfo += $"Floor: {runManager.currentFloor}\n";
            runInfo += $"Room: {runManager.currentRoom}/{runManager.maxRooms}";
            
            runInfoText.text = runInfo;
        }
        
        private void UpdateTimeInfo()
        {
            if (timeText == null || runManager == null) return;
            
            float runTime = runManager.GetRunTime();
            int hours = Mathf.FloorToInt(runTime / 3600f);
            int minutes = Mathf.FloorToInt((runTime % 3600f) / 60f);
            int seconds = Mathf.FloorToInt(runTime % 60f);
            
            if (hours > 0)
            {
                timeText.text = $"Time: {hours:00}:{minutes:00}:{seconds:00}";
            }
            else
            {
                timeText.text = $"Time: {minutes:00}:{seconds:00}";
            }
        }
        
        private void UpdateStatsInfo()
        {
            if (statsText == null || runManager == null) return;
            
            string stats = $"Enemies Killed: {runManager.enemiesKilled}\n";
            stats += $"Rooms Cleared: {runManager.roomsCleared}\n";
            stats += $"Items Found: {runManager.itemsFound}\n";
            stats += $"Deaths: {runManager.deathCount}";
            
            statsText.text = stats;
        }
        
        private void UpdateVolumeSliders()
        {
            if (audioManager == null) return;
            
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = audioManager.GetMasterVolume();
            
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = audioManager.GetMusicVolume();
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = audioManager.GetSFXVolume();
        }
        
        #endregion
        
        #region Button Handlers
        
        private void OnResumeClicked()
        {
            // Play UI sound
            if (audioManager != null)
                audioManager.PlayUI("button_click");
            
            // Resume game
            if (uiManager != null)
            {
                uiManager.ResumeGame();
            }
            else if (gameManager != null)
            {
                gameManager.ResumeGame();
            }
        }
        
        private void OnSettingsClicked()
        {
            // Play UI sound
            if (audioManager != null)
                audioManager.PlayUI("button_click");
            
            // Open settings
            if (uiManager != null)
            {
                uiManager.ToggleSettings();
            }
        }
        
        private void OnMainMenuClicked()
        {
            // Play UI sound
            if (audioManager != null)
                audioManager.PlayUI("button_click");
            
            // Show confirmation dialog
            ShowMainMenuConfirmation();
        }
        
        private void OnQuitClicked()
        {
            // Play UI sound
            if (audioManager != null)
                audioManager.PlayUI("button_click");
            
            // Show quit confirmation
            ShowQuitConfirmation();
        }
        
        #endregion
        
        #region Volume Handlers
        
        private void OnMasterVolumeChanged(float value)
        {
            if (audioManager != null)
            {
                audioManager.SetMasterVolume(value);
            }
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            if (audioManager != null)
            {
                audioManager.SetMusicVolume(value);
            }
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            if (audioManager != null)
            {
                audioManager.SetSFXVolume(value);
                // Play test sound
                audioManager.PlayUI("volume_test");
            }
        }
        
        private void OnFullscreenToggled(bool isFullscreen)
        {
            Screen.fullScreen = isFullscreen;
            
            // Play UI sound
            if (audioManager != null)
                audioManager.PlayUI("toggle");
        }
        
        #endregion
        
        #region Confirmation Dialogs
        
        private void ShowMainMenuConfirmation()
        {
            // Create simple confirmation dialog
            if (gameManager != null)
            {
                // For now, just go to main menu directly
                // In a full implementation, you'd show a proper dialog
                gameManager.ReturnToMainMenu();
            }
        }
        
        private void ShowQuitConfirmation()
        {
            // Create simple confirmation dialog
            // For now, just quit directly
            // In a full implementation, you'd show a proper dialog
            QuitGame();
        }
        
        private void QuitGame()
        {
            // Save settings before quitting
            if (audioManager != null)
            {
                audioManager.SaveAudioSettings();
            }
            
            // Save game progress
            if (gameManager != null)
            {
                gameManager.SaveGame();
            }
            
            // Quit application
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        #endregion
        
        #region Animation
        
        private System.Collections.IEnumerator FadeInMenu()
        {
            if (canvasGroup == null) yield break;
            
            float elapsed = 0f;
            canvasGroup.alpha = 0f;
            
            while (elapsed < fadeInDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / fadeInDuration;
                canvasGroup.alpha = fadeInCurve.Evaluate(progress);
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
        }
        
        #endregion
        
        #region Keyboard Navigation
        
        private void Update()
        {
            HandleKeyboardInput();
        }
        
        private void HandleKeyboardInput()
        {
            // ESC to resume
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                OnResumeClicked();
            }
            
            // R to resume
            if (Input.GetKeyDown(KeyCode.R))
            {
                OnResumeClicked();
            }
            
            // S for settings
            if (Input.GetKeyDown(KeyCode.S))
            {
                OnSettingsClicked();
            }
            
            // Q to quit
            if (Input.GetKeyDown(KeyCode.Q))
            {
                OnQuitClicked();
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetInteractable(bool interactable)
        {
            if (canvasGroup != null)
            {
                canvasGroup.interactable = interactable;
            }
        }
        
        public void UpdateRunStats(int enemiesKilled, int roomsCleared, int itemsFound)
        {
            if (runManager != null)
            {
                runManager.enemiesKilled = enemiesKilled;
                runManager.roomsCleared = roomsCleared;
                runManager.itemsFound = itemsFound;
            }
            
            UpdateStatsInfo();
        }
        
        #endregion
    }
}
