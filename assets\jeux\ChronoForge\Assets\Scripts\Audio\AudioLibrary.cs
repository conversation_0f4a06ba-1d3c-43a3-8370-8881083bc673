using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace ChronoForge.Audio
{
    /// <summary>
    /// Bibliothèque audio pour organiser les clips audio dans ChronoForge
    /// </summary>
    [CreateAssetMenu(fileName = "AudioLibrary", menuName = "ChronoForge/Audio Library")]
    public class AudioLibrary : ScriptableObject
    {
        [Header("Library Info")]
        public string libraryName;
        public string description;
        
        [Header("Audio Clips")]
        public List<AudioEntry> audioEntries = new List<AudioEntry>();
        
        // Cache for faster lookup
        private Dictionary<string, AudioClip> audioCache = new Dictionary<string, AudioClip>();
        private bool isCacheBuilt = false;
        
        #region Public Methods
        
        /// <summary>
        /// Obtient un clip audio par son nom
        /// </summary>
        public AudioClip GetClip(string clipName)
        {
            if (!isCacheBuilt)
                BuildCache();
            
            audioCache.TryGetValue(clipName, out AudioClip clip);
            return clip;
        }
        
        /// <summary>
        /// Obtient un clip audio aléatoire d'une catégorie
        /// </summary>
        public AudioClip GetRandomClip(string category = "")
        {
            List<AudioEntry> entries;
            
            if (string.IsNullOrEmpty(category))
            {
                entries = audioEntries.Where(e => e.audioClip != null).ToList();
            }
            else
            {
                entries = audioEntries.Where(e => e.audioClip != null && e.category == category).ToList();
            }
            
            if (entries.Count == 0)
                return null;
            
            AudioEntry randomEntry = entries[Random.Range(0, entries.Count)];
            return randomEntry.audioClip;
        }
        
        /// <summary>
        /// Obtient tous les clips d'une catégorie
        /// </summary>
        public List<AudioClip> GetClipsByCategory(string category)
        {
            return audioEntries
                .Where(e => e.category == category && e.audioClip != null)
                .Select(e => e.audioClip)
                .ToList();
        }
        
        /// <summary>
        /// Vérifie si un clip existe
        /// </summary>
        public bool HasClip(string clipName)
        {
            if (!isCacheBuilt)
                BuildCache();
            
            return audioCache.ContainsKey(clipName);
        }
        
        /// <summary>
        /// Obtient toutes les catégories disponibles
        /// </summary>
        public List<string> GetCategories()
        {
            return audioEntries
                .Where(e => !string.IsNullOrEmpty(e.category))
                .Select(e => e.category)
                .Distinct()
                .ToList();
        }
        
        /// <summary>
        /// Ajoute un nouveau clip audio
        /// </summary>
        public void AddClip(string name, AudioClip clip, string category = "", float volume = 1f, float pitch = 1f)
        {
            AudioEntry newEntry = new AudioEntry
            {
                name = name,
                audioClip = clip,
                category = category,
                volume = volume,
                pitch = pitch
            };
            
            audioEntries.Add(newEntry);
            RebuildCache();
        }
        
        /// <summary>
        /// Supprime un clip audio
        /// </summary>
        public bool RemoveClip(string name)
        {
            AudioEntry entry = audioEntries.FirstOrDefault(e => e.name == name);
            if (entry != null)
            {
                audioEntries.Remove(entry);
                RebuildCache();
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// Obtient les informations d'un clip
        /// </summary>
        public AudioEntry GetAudioEntry(string name)
        {
            return audioEntries.FirstOrDefault(e => e.name == name);
        }
        
        #endregion
        
        #region Cache Management
        
        private void BuildCache()
        {
            audioCache.Clear();
            
            foreach (AudioEntry entry in audioEntries)
            {
                if (entry.audioClip != null && !string.IsNullOrEmpty(entry.name))
                {
                    if (!audioCache.ContainsKey(entry.name))
                    {
                        audioCache[entry.name] = entry.audioClip;
                    }
                    else
                    {
                        Debug.LogWarning($"Duplicate audio entry name: {entry.name} in library: {libraryName}");
                    }
                }
            }
            
            isCacheBuilt = true;
        }
        
        private void RebuildCache()
        {
            isCacheBuilt = false;
            BuildCache();
        }
        
        private void OnValidate()
        {
            // Rebuild cache when entries are modified in editor
            RebuildCache();
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Obtient le nombre total de clips
        /// </summary>
        public int GetClipCount()
        {
            return audioEntries.Count(e => e.audioClip != null);
        }
        
        /// <summary>
        /// Obtient le nombre de clips par catégorie
        /// </summary>
        public int GetClipCount(string category)
        {
            return audioEntries.Count(e => e.audioClip != null && e.category == category);
        }
        
        /// <summary>
        /// Valide la bibliothèque
        /// </summary>
        public List<string> ValidateLibrary()
        {
            List<string> issues = new List<string>();
            
            // Check for null clips
            var nullClips = audioEntries.Where(e => e.audioClip == null).ToList();
            if (nullClips.Count > 0)
            {
                issues.Add($"{nullClips.Count} entries have null audio clips");
            }
            
            // Check for empty names
            var emptyNames = audioEntries.Where(e => string.IsNullOrEmpty(e.name)).ToList();
            if (emptyNames.Count > 0)
            {
                issues.Add($"{emptyNames.Count} entries have empty names");
            }
            
            // Check for duplicate names
            var duplicateNames = audioEntries
                .Where(e => !string.IsNullOrEmpty(e.name))
                .GroupBy(e => e.name)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();
            
            if (duplicateNames.Count > 0)
            {
                issues.Add($"Duplicate names found: {string.Join(", ", duplicateNames)}");
            }
            
            return issues;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Entrée audio dans la bibliothèque
    /// </summary>
    [System.Serializable]
    public class AudioEntry
    {
        [Header("Basic Info")]
        public string name;
        public AudioClip audioClip;
        public string category = "";
        
        [Header("Playback Settings")]
        [Range(0f, 2f)]
        public float volume = 1f;
        
        [Range(0.1f, 3f)]
        public float pitch = 1f;
        
        [Header("3D Audio Settings")]
        public bool is3D = false;
        
        [Range(0f, 1f)]
        public float spatialBlend = 0f;
        
        public float minDistance = 1f;
        public float maxDistance = 500f;
        
        [Header("Loop Settings")]
        public bool loop = false;
        public float loopStartTime = 0f;
        public float loopEndTime = 0f;
        
        [Header("Random Settings")]
        public bool randomizePitch = false;
        
        [Range(0f, 1f)]
        public float pitchVariation = 0.1f;
        
        public bool randomizeVolume = false;
        
        [Range(0f, 1f)]
        public float volumeVariation = 0.1f;
        
        [Header("Metadata")]
        public string description = "";
        public string[] tags = new string[0];
        
        /// <summary>
        /// Obtient le volume avec variation aléatoire
        /// </summary>
        public float GetRandomizedVolume()
        {
            if (!randomizeVolume)
                return volume;
            
            float variation = Random.Range(-volumeVariation, volumeVariation);
            return Mathf.Clamp01(volume + variation);
        }
        
        /// <summary>
        /// Obtient le pitch avec variation aléatoire
        /// </summary>
        public float GetRandomizedPitch()
        {
            if (!randomizePitch)
                return pitch;
            
            float variation = Random.Range(-pitchVariation, pitchVariation);
            return Mathf.Clamp(pitch + variation, 0.1f, 3f);
        }
        
        /// <summary>
        /// Vérifie si l'entrée a un tag spécifique
        /// </summary>
        public bool HasTag(string tag)
        {
            return tags != null && tags.Contains(tag);
        }
        
        /// <summary>
        /// Obtient la durée du clip audio
        /// </summary>
        public float GetDuration()
        {
            return audioClip != null ? audioClip.length : 0f;
        }
        
        /// <summary>
        /// Vérifie si l'entrée est valide
        /// </summary>
        public bool IsValid()
        {
            return audioClip != null && !string.IsNullOrEmpty(name);
        }
    }
}
