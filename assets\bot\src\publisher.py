"""
Module de publication qui coordonne les publications sur toutes les plateformes.
"""

import time
import logging
from typing import Dict, Any, List
from .content_manager import Post
from .platforms.base import PublicationResult
from .platforms.twitter import TwitterConnector
from .platforms.reddit import RedditConnector
from .platforms.linkedin import LinkedInConnector
from .platforms.devto import DevtoConnector

logger = logging.getLogger(__name__)

class Publisher:
    """Gestionnaire de publication sur toutes les plateformes."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.platforms = {}
        self._initialize_platforms()
        
    def _initialize_platforms(self):
        """Initialise tous les connecteurs de plateformes."""
        platform_configs = self.config.get('platforms', {})
        
        # Twitter
        if platform_configs.get('twitter', {}).get('enabled', True):
            try:
                self.platforms['twitter'] = TwitterConnector(platform_configs.get('twitter', {}))
                logger.info("🐦 Connecteur Twitter initialisé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation Twitter: {e}")
        
        # Reddit
        if platform_configs.get('reddit', {}).get('enabled', True):
            try:
                self.platforms['reddit'] = RedditConnector(platform_configs.get('reddit', {}))
                logger.info("🔴 Connecteur Reddit initialisé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation Reddit: {e}")
        
        # LinkedIn
        if platform_configs.get('linkedin', {}).get('enabled', True):
            try:
                self.platforms['linkedin'] = LinkedInConnector(platform_configs.get('linkedin', {}))
                logger.info("💼 Connecteur LinkedIn initialisé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation LinkedIn: {e}")
        
        # Dev.to
        if platform_configs.get('devto', {}).get('enabled', True):
            try:
                self.platforms['devto'] = DevtoConnector(platform_configs.get('devto', {}))
                logger.info("📝 Connecteur Dev.to initialisé")
            except Exception as e:
                logger.error(f"❌ Erreur initialisation Dev.to: {e}")
    
    def authenticate_all(self) -> Dict[str, bool]:
        """Authentifie tous les connecteurs."""
        auth_results = {}
        
        for platform_name, connector in self.platforms.items():
            try:
                success = connector.authenticate()
                auth_results[platform_name] = success
                
                if success:
                    logger.info(f"✅ Authentification réussie: {platform_name}")
                else:
                    logger.error(f"❌ Échec authentification: {platform_name}")
                    
            except Exception as e:
                logger.error(f"❌ Erreur authentification {platform_name}: {e}")
                auth_results[platform_name] = False
        
        return auth_results
    
    def publish_to_all_platforms(self, post: Post) -> List[PublicationResult]:
        """Publie un post sur toutes les plateformes configurées."""
        results = []
        
        for platform_name in post.platforms:
            if platform_name not in self.platforms:
                logger.warning(f"⚠️ Plateforme {platform_name} non disponible pour le post {post.id}")
                results.append(PublicationResult(
                    success=False,
                    platform=platform_name,
                    error="Plateforme non configurée"
                ))
                continue
            
            # Publier sur la plateforme
            result = self.publish_to_platform(post, platform_name)
            results.append(result)
            
            # Respecter les rate limits
            if result.success:
                delay = self.platforms[platform_name].get_rate_limit_delay()
                if delay > 0:
                    logger.info(f"⏳ Attente de {delay}s pour respecter les rate limits de {platform_name}")
                    time.sleep(delay)
        
        return results
    
    def publish_to_platform(self, post: Post, platform_name: str) -> PublicationResult:
        """Publie un post sur une plateforme spécifique."""
        if platform_name not in self.platforms:
            return PublicationResult(
                success=False,
                platform=platform_name,
                error="Plateforme non disponible"
            )
        
        connector = self.platforms[platform_name]
        
        # Vérifier l'authentification
        if not connector.authenticated:
            auth_success = connector.authenticate()
            if not auth_success:
                return PublicationResult(
                    success=False,
                    platform=platform_name,
                    error="Échec de l'authentification"
                )
        
        # Préparer les paramètres spécifiques à la plateforme
        kwargs = self._prepare_platform_kwargs(post, platform_name)
        
        # Valider le post
        if not connector.validate_post(post.title, post.body, **kwargs):
            return PublicationResult(
                success=False,
                platform=platform_name,
                error="Validation du post échouée"
            )
        
        # Publier
        try:
            result = connector.publish_post(post.title, post.body, **kwargs)
            return result
        except Exception as e:
            logger.error(f"❌ Erreur lors de la publication sur {platform_name}: {e}")
            return PublicationResult(
                success=False,
                platform=platform_name,
                error=str(e)
            )
    
    def _prepare_platform_kwargs(self, post: Post, platform_name: str) -> Dict[str, Any]:
        """Prépare les paramètres spécifiques à chaque plateforme."""
        kwargs = {
            'post_id': post.id,
            'tags': post.tags,
            'image': post.image
        }
        
        # Paramètres spécifiques à Reddit
        if platform_name == 'reddit':
            # Utiliser le subreddit spécifié dans le post ou suggérer un subreddit
            if post.subreddit:
                kwargs['subreddit'] = post.subreddit
            else:
                # Suggérer un subreddit basé sur les tags
                connector = self.platforms['reddit']
                kwargs['subreddit'] = connector.get_suitable_subreddit(post.tags)
        
        # Paramètres spécifiques à Dev.to
        elif platform_name == 'devto':
            # Dev.to peut avoir des paramètres supplémentaires
            kwargs['series'] = getattr(post, 'series', None)
            kwargs['canonical_url'] = getattr(post, 'canonical_url', None)
        
        return kwargs
    
    def test_all_connections(self) -> Dict[str, Dict[str, Any]]:
        """Teste toutes les connexions aux plateformes."""
        test_results = {}
        
        for platform_name, connector in self.platforms.items():
            test_results[platform_name] = {
                'available': True,
                'authenticated': False,
                'error': None
            }
            
            try:
                # Test d'authentification
                auth_success = connector.authenticate()
                test_results[platform_name]['authenticated'] = auth_success
                
                if not auth_success:
                    test_results[platform_name]['error'] = "Échec de l'authentification"
                    
            except Exception as e:
                test_results[platform_name]['available'] = False
                test_results[platform_name]['error'] = str(e)
        
        return test_results
    
    def get_platform_status(self) -> Dict[str, Dict[str, Any]]:
        """Retourne le statut de toutes les plateformes."""
        status = {}
        
        for platform_name, connector in self.platforms.items():
            status[platform_name] = {
                'enabled': True,
                'authenticated': connector.authenticated,
                'rate_limit_delay': connector.get_rate_limit_delay(),
                'config': {
                    'max_length': getattr(connector, 'max_length', None),
                    'default_subreddit': getattr(connector, 'default_subreddit', None),
                    'default_tags': getattr(connector, 'default_tags', None)
                }
            }
        
        return status
    
    def publish_test_post(self) -> List[PublicationResult]:
        """Publie un post de test sur toutes les plateformes."""
        test_post = Post(
            id="test_post",
            title="Test du bot de publication",
            body="Ceci est un test automatisé du bot de publication. 🤖",
            platforms=list(self.platforms.keys()),
            schedule="now",
            tags=["test", "bot"]
        )
        
        return self.publish_to_all_platforms(test_post)
