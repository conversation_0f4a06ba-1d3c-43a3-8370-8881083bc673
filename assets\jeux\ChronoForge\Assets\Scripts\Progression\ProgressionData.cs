using UnityEngine;
using System.Collections.Generic;

namespace ChronoForge.Progression
{
    /// <summary>
    /// Données de progression du joueur pour ChronoForge
    /// </summary>
    [System.Serializable]
    public class PlayerProgressionData
    {
        [Header("Level and Experience")]
        public int playerLevel = 1;
        public int totalExperience = 0;
        
        [Header("Meta Currency")]
        public int metaCurrency = 0;
        
        [<PERSON><PERSON>("Meta Upgrades")]
        public SerializableDictionary<string, int> metaUpgrades = new SerializableDictionary<string, int>();
        
        [Header("Run Statistics")]
        public int totalRuns = 0;
        public int successfulRuns = 0;
        public int totalEnemiesKilled = 0;
        public float totalPlayTime = 0f;
        public int bestFloorReached = 0;
        public float bestRunTime = 0f;
        
        [Header("Detailed Statistics")]
        public SerializableDictionary<string, int> enemyKillCounts = new SerializableDictionary<string, int>();
        public List<string> itemsFound = new List<string>();
        public List<string> achievementsUnlocked = new List<string>();
        public List<string> unlockedContent = new List<string>();
        
        [Header("Settings and Preferences")]
        public string favoriteClass = "CyberWarrior";
        public string favoriteWeapon = "PlasmaSword";
        public List<string> customLoadouts = new List<string>();
        
        /// <summary>
        /// Obtient le nombre total d'ennemis d'un type spécifique tués
        /// </summary>
        public int GetEnemyKillCount(string enemyType)
        {
            return enemyKillCounts.ContainsKey(enemyType) ? enemyKillCounts[enemyType] : 0;
        }
        
        /// <summary>
        /// Vérifie si un achievement est débloqué
        /// </summary>
        public bool HasAchievement(string achievementId)
        {
            return achievementsUnlocked.Contains(achievementId);
        }
        
        /// <summary>
        /// Vérifie si un contenu est débloqué
        /// </summary>
        public bool IsContentUnlocked(string contentId)
        {
            return unlockedContent.Contains(contentId);
        }
        
        /// <summary>
        /// Obtient le taux de réussite des runs
        /// </summary>
        public float GetSuccessRate()
        {
            return totalRuns > 0 ? (float)successfulRuns / totalRuns : 0f;
        }
        
        /// <summary>
        /// Obtient le temps de jeu formaté
        /// </summary>
        public string GetFormattedPlayTime()
        {
            int hours = Mathf.FloorToInt(totalPlayTime / 3600f);
            int minutes = Mathf.FloorToInt((totalPlayTime % 3600f) / 60f);
            
            if (hours > 0)
                return $"{hours}h {minutes}m";
            else
                return $"{minutes}m";
        }
        
        /// <summary>
        /// Obtient le temps du meilleur run formaté
        /// </summary>
        public string GetFormattedBestRunTime()
        {
            if (bestRunTime <= 0) return "N/A";
            
            int minutes = Mathf.FloorToInt(bestRunTime / 60f);
            int seconds = Mathf.FloorToInt(bestRunTime % 60f);
            
            return $"{minutes:00}:{seconds:00}";
        }
    }
    
    /// <summary>
    /// Amélioration méta-jeu
    /// </summary>
    [System.Serializable]
    public class MetaUpgrade
    {
        [Header("Basic Info")]
        public string id;
        public string name;
        public string description;
        public MetaUpgradeCategory category;
        
        [Header("Cost")]
        public int baseCost;
        public float costMultiplier = 1.5f;
        
        [Header("Progression")]
        public int maxLevel = 10;
        public float valuePerLevel = 1f;
        
        [Header("Visual")]
        public Sprite icon;
        public Color categoryColor = Color.white;
        
        /// <summary>
        /// Obtient le coût pour un niveau spécifique
        /// </summary>
        public int GetCostForLevel(int level)
        {
            return Mathf.RoundToInt(baseCost * Mathf.Pow(costMultiplier, level));
        }
        
        /// <summary>
        /// Obtient la valeur totale pour un niveau spécifique
        /// </summary>
        public float GetValueForLevel(int level)
        {
            return valuePerLevel * level;
        }
        
        /// <summary>
        /// Obtient la description avec les valeurs actuelles
        /// </summary>
        public string GetDescriptionWithValues(int currentLevel)
        {
            string desc = description;
            
            if (currentLevel > 0)
            {
                float currentValue = GetValueForLevel(currentLevel);
                desc += $"\n\nCurrent: {FormatValue(currentValue)}";
            }
            
            if (currentLevel < maxLevel)
            {
                float nextValue = GetValueForLevel(currentLevel + 1);
                desc += $"\nNext: {FormatValue(nextValue)}";
            }
            
            return desc;
        }
        
        private string FormatValue(float value)
        {
            switch (category)
            {
                case MetaUpgradeCategory.Health:
                    return $"+{value:F0} HP";
                case MetaUpgradeCategory.Damage:
                case MetaUpgradeCategory.Speed:
                case MetaUpgradeCategory.Critical:
                    return $"+{value * 100:F0}%";
                case MetaUpgradeCategory.Economy:
                    return $"+{value:F0} coins";
                default:
                    return value.ToString("F1");
            }
        }
    }
    
    /// <summary>
    /// Catégories d'améliorations méta
    /// </summary>
    public enum MetaUpgradeCategory
    {
        Health,
        Damage,
        Speed,
        Critical,
        Defense,
        Economy,
        Utility,
        Special
    }
    
    /// <summary>
    /// Contenu débloquable
    /// </summary>
    [System.Serializable]
    public class UnlockableContent
    {
        [Header("Basic Info")]
        public string id;
        public string name;
        public string description;
        public UnlockableType type;
        
        [Header("Unlock Condition")]
        public UnlockCondition unlockCondition;
        public int requiredValue;
        public string additionalRequirement = "";
        
        [Header("State")]
        public bool isUnlocked = false;
        public bool isNew = false;
        
        [Header("Visual")]
        public Sprite icon;
        public Color typeColor = Color.white;
        
        /// <summary>
        /// Obtient la description de la condition de déblocage
        /// </summary>
        public string GetUnlockConditionDescription()
        {
            switch (unlockCondition)
            {
                case UnlockCondition.ReachLevel:
                    return $"Reach level {requiredValue}";
                case UnlockCondition.CompleteRuns:
                    return $"Complete {requiredValue} runs";
                case UnlockCondition.KillEnemies:
                    return $"Kill {requiredValue} enemies";
                case UnlockCondition.PlayTime:
                    return $"Play for {requiredValue} minutes";
                case UnlockCondition.ReachFloor:
                    return $"Reach floor {requiredValue}";
                case UnlockCondition.FindItems:
                    return $"Find {requiredValue} items";
                case UnlockCondition.SpendCurrency:
                    return $"Spend {requiredValue} meta currency";
                case UnlockCondition.Custom:
                    return additionalRequirement;
                default:
                    return "Unknown condition";
            }
        }
        
        /// <summary>
        /// Vérifie le progrès vers le déblocage
        /// </summary>
        public float GetUnlockProgress(PlayerProgressionData progressionData)
        {
            if (isUnlocked) return 1f;
            
            int currentValue = GetCurrentProgressValue(progressionData);
            return Mathf.Clamp01((float)currentValue / requiredValue);
        }
        
        private int GetCurrentProgressValue(PlayerProgressionData progressionData)
        {
            switch (unlockCondition)
            {
                case UnlockCondition.ReachLevel:
                    return progressionData.playerLevel;
                case UnlockCondition.CompleteRuns:
                    return progressionData.successfulRuns;
                case UnlockCondition.KillEnemies:
                    return progressionData.totalEnemiesKilled;
                case UnlockCondition.PlayTime:
                    return Mathf.FloorToInt(progressionData.totalPlayTime / 60f);
                case UnlockCondition.ReachFloor:
                    return progressionData.bestFloorReached;
                case UnlockCondition.FindItems:
                    return progressionData.itemsFound.Count;
                default:
                    return 0;
            }
        }
    }
    
    /// <summary>
    /// Types de contenu débloquable
    /// </summary>
    public enum UnlockableType
    {
        PlayerClass,
        Weapon,
        Biome,
        GameMode,
        Achievement,
        Cosmetic,
        Feature,
        Challenge
    }
    
    /// <summary>
    /// Conditions de déblocage
    /// </summary>
    public enum UnlockCondition
    {
        ReachLevel,
        CompleteRuns,
        KillEnemies,
        PlayTime,
        ReachFloor,
        FindItems,
        SpendCurrency,
        Custom
    }
    
    /// <summary>
    /// Achievement du jeu
    /// </summary>
    [System.Serializable]
    public class Achievement
    {
        [Header("Basic Info")]
        public string id;
        public string name;
        public string description;
        public AchievementCategory category;
        
        [Header("Condition")]
        public AchievementCondition condition;
        public int targetValue;
        public string specificRequirement = "";
        
        [Header("Reward")]
        public int metaCurrencyReward = 0;
        public string unlockContentId = "";
        
        [Header("State")]
        public bool isUnlocked = false;
        public bool isHidden = false;
        public int currentProgress = 0;
        
        [Header("Visual")]
        public Sprite icon;
        public AchievementRarity rarity = AchievementRarity.Common;
        
        /// <summary>
        /// Obtient le pourcentage de progression
        /// </summary>
        public float GetProgressPercentage()
        {
            if (isUnlocked) return 1f;
            return targetValue > 0 ? Mathf.Clamp01((float)currentProgress / targetValue) : 0f;
        }
        
        /// <summary>
        /// Met à jour le progrès de l'achievement
        /// </summary>
        public bool UpdateProgress(int newProgress)
        {
            if (isUnlocked) return false;
            
            currentProgress = newProgress;
            
            if (currentProgress >= targetValue)
            {
                isUnlocked = true;
                return true; // Achievement unlocked
            }
            
            return false;
        }
        
        /// <summary>
        /// Obtient la couleur basée sur la rareté
        /// </summary>
        public Color GetRarityColor()
        {
            switch (rarity)
            {
                case AchievementRarity.Common:
                    return Color.white;
                case AchievementRarity.Uncommon:
                    return Color.green;
                case AchievementRarity.Rare:
                    return Color.blue;
                case AchievementRarity.Epic:
                    return Color.magenta;
                case AchievementRarity.Legendary:
                    return Color.yellow;
                default:
                    return Color.gray;
            }
        }
    }
    
    /// <summary>
    /// Catégories d'achievements
    /// </summary>
    public enum AchievementCategory
    {
        Combat,
        Exploration,
        Progression,
        Collection,
        Challenge,
        Social,
        Hidden
    }
    
    /// <summary>
    /// Conditions d'achievements
    /// </summary>
    public enum AchievementCondition
    {
        KillEnemies,
        CompleteRuns,
        ReachLevel,
        CollectItems,
        SpendCurrency,
        SurviveTime,
        DealDamage,
        UseAbilities,
        Custom
    }
    
    /// <summary>
    /// Rareté des achievements
    /// </summary>
    public enum AchievementRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
    
    /// <summary>
    /// Dictionnaire sérialisable pour Unity
    /// </summary>
    [System.Serializable]
    public class SerializableDictionary<TKey, TValue> : Dictionary<TKey, TValue>, ISerializationCallbackReceiver
    {
        [SerializeField]
        private List<TKey> keys = new List<TKey>();
        
        [SerializeField]
        private List<TValue> values = new List<TValue>();
        
        public void OnBeforeSerialize()
        {
            keys.Clear();
            values.Clear();
            
            foreach (KeyValuePair<TKey, TValue> pair in this)
            {
                keys.Add(pair.Key);
                values.Add(pair.Value);
            }
        }
        
        public void OnAfterDeserialize()
        {
            this.Clear();
            
            if (keys.Count != values.Count)
            {
                Debug.LogError("SerializableDictionary: keys and values count mismatch");
                return;
            }
            
            for (int i = 0; i < keys.Count; i++)
            {
                this.Add(keys[i], values[i]);
            }
        }
    }
}
