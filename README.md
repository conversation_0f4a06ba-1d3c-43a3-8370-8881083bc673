# 🔄 API2CSV - Convertisseur J<PERSON><PERSON> vers CSV

[![GitHub Pages](https://img.shields.io/badge/demo-live-brightgreen)](https://neethdseven.github.io/api2csv/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![GitHub stars](https://img.shields.io/github/stars/NeethDseven/api2csv.svg)](https://github.com/NeethDseven/api2csv/stargazers)

> **Convertissez instantanément vos réponses JSON d'API en fichiers CSV téléchargeables**  
> Outil gratuit, sécurisé et sans inscription. Parfait pour développeurs et data analysts.

## 🚀 Démo en ligne

**[👉 Essayer API2CSV maintenant](https://neethdseven.github.io/api2csv/)**

## 🎬 Démo en Action

![API2CSV Demo](Animation.gif)

*Convertissez du JSON complexe en CSV propre en quelques secondes !*

## ✨ Fonctionnalités

### 🎯 Fonctionnalités principales
- ✅ **Conversion JSON → CSV** instantanée
- ✅ **Aperçu en temps réel** du tableau généré
- ✅ **Gestion des objets imbriqués** (notation `a.b.c`)
- ✅ **Validation JSON** avec messages d'erreur clairs
- ✅ **Téléchargement direct** du fichier CSV
- ✅ **100% côté client** - vos données restent privées

### 🎨 Fonctionnalités bonus
- 🌙 **Mode sombre** automatique
- ⌨️ **Raccourcis clavier** (Ctrl+Enter pour convertir)
- 📱 **Interface responsive** (mobile/desktop)
- 🔧 **Exemples JSON** intégrés
- 🎯 **Auto-détection** du presse-papiers

## 🛠️ Comment utiliser

### Méthode simple
1. **Collez** votre JSON dans la zone de texte
2. **Cliquez** sur "Convertir en CSV" (ou Ctrl+Enter)
3. **Prévisualisez** le résultat dans le tableau
4. **Téléchargez** votre fichier CSV

### Exemples JSON supportés

#### Objet simple
```json
{
  "name": "John Doe",
  "age": 30,
  "city": "Paris"
}
```

#### Tableau d'objets
```json
[
  {"name": "Alice", "age": 25, "city": "Lyon"},
  {"name": "Bob", "age": 35, "city": "Marseille"}
]
```

#### Objets imbriqués
```json
{
  "user": {
    "profile": {
      "name": "Jane Smith",
      "contact": {
        "email": "<EMAIL>"
      }
    }
  }
}
```
*Résultat CSV : `user.profile.name`, `user.profile.contact.email`*

## 🎯 Cas d'utilisation

### 👨‍💻 Pour les développeurs
- **Tester des API** : Convertir rapidement les réponses pour Excel
- **Debug de données** : Visualiser les structures JSON complexes
- **Prototypage** : Créer des datasets CSV pour les tests

### 📊 Pour les data analysts
- **Import Excel/Sheets** : Transformer les exports JSON en CSV
- **Analyse de données** : Préparer les données pour les outils BI
- **Reporting** : Convertir les données API pour les rapports

### 🎓 Pour les étudiants
- **Projets scolaires** : Convertir des données JSON sans coder
- **Apprentissage** : Comprendre la structure des données API
- **Recherche** : Préparer des datasets pour l'analyse

## 🔧 Installation locale

```bash
# Cloner le repository
git clone https://github.com/NeethDseven/api2csv.git

# Aller dans le dossier
cd api2csv

# Ouvrir index.html dans votre navigateur
# Ou utiliser un serveur local
python -m http.server 8000
# Puis aller sur http://localhost:8000
```

## 🏗️ Architecture technique

### Stack technique
- **Frontend** : HTML5, TailwindCSS, JavaScript Vanilla
- **Parsing CSV** : [PapaParse](https://www.papaparse.com/) 5.4.1
- **Hébergement** : GitHub Pages (statique)
- **Compatibilité** : Chrome, Firefox, Edge, Safari récents

### Structure des fichiers
```
api2csv/
├── index.html          # Page principale
├── script.js           # Logique JavaScript
├── assets/             # Images et ressources
├── README.md           # Documentation
└── LICENSE             # Licence MIT
```

### Fonctionnalités techniques
- **Sécurité** : Aucune donnée envoyée sur serveur
- **Performance** : Chargement instantané, traitement local
- **Accessibilité** : Support clavier, aria-labels
- **SEO** : Meta tags optimisés, Open Graph

## 🧪 Tests

### Tests manuels recommandés
- [ ] JSON valide (tableau d'objets)
- [ ] JSON invalide (syntaxe incorrecte)
- [ ] Objets imbriqués complexes
- [ ] Tableaux de primitives
- [ ] Valeurs null/undefined
- [ ] Gros volumes de données (>1000 lignes)

### Navigateurs testés
- ✅ Chrome 120+
- ✅ Firefox 115+
- ✅ Safari 16+
- ✅ Edge 120+

## 🤝 Contribuer

Les contributions sont les bienvenues ! 

### Comment contribuer
1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** vos changements (`git commit -m 'Add AmazingFeature'`)
4. **Push** vers la branche (`git push origin feature/AmazingFeature`)
5. **Ouvrir** une Pull Request

### Idées de contributions
- 🔧 Support de nouveaux formats (Excel, TSV)
- 🎨 Améliorations UI/UX
- 🌍 Traductions (anglais, espagnol...)
- 📱 Optimisations mobile
- ⚡ Optimisations performance

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## ☕ Support

Si cet outil vous a été utile, vous pouvez :

- ⭐ **Star** le repository
- 🐛 **Signaler** des bugs via les [Issues](https://github.com/votre-username/api2csv/issues)
- 💰 **Sponsoriser** le projet via [GitHub Sponsors](https://github.com/sponsors/votre-username)
- ☕ **Offrir un café** via [Buy Me a Coffee](https://buymeacoffee.com/votre-username)

## 🔗 Liens utiles

- 📖 [Documentation PapaParse](https://www.papaparse.com/docs)
- 🎨 [TailwindCSS](https://tailwindcss.com/)
- 🚀 [GitHub Pages](https://pages.github.com/)

---

**Fait avec ❤️ pour la communauté des développeurs**

*API2CSV - Parce que convertir du JSON ne devrait pas être compliqué*
