<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>API2CSV - Convert JSON to CSV Online Free | No Signup Required</title>
    <meta name="description" content="Convert JSON API responses to CSV files instantly. Free online tool with no signup required. Perfect for developers, data analysts, and students. Works 100% in your browser.">
    <meta name="keywords" content="JSON to CSV converter, API response to CSV, free JSON parser, online CSV generator, developer tools, data conversion, Excel import">
    <meta name="author" content="API2CSV">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://neethdseven.github.io/api2csv/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://neethdseven.github.io/api2csv/">
    <meta property="og:title" content="API2CSV - Convert JSON to CSV Online Free">
    <meta property="og:description" content="Convert JSON API responses to CSV files instantly. Free tool with no signup required. Perfect for developers and data analysts.">
    <meta property="og:image" content="https://neethdseven.github.io/api2csv/assets/og-image.png">
    <meta property="og:site_name" content="API2CSV">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://neethdseven.github.io/api2csv/">
    <meta property="twitter:title" content="API2CSV - Convert JSON to CSV Online Free">
    <meta property="twitter:description" content="Convert JSON API responses to CSV files instantly. Free tool with no signup required.">
    <meta property="twitter:image" content="https://neethdseven.github.io/api2csv/assets/twitter-image.png">

    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "API2CSV",
        "description": "Free online tool to convert JSON to CSV",
        "url": "https://neethdseven.github.io/api2csv/",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "creator": {
            "@type": "Person",
            "name": "NeethDseven"
        },
        "featureList": [
            "Convert JSON to CSV",
            "Handle nested objects",
            "Real-time preview",
            "No signup required",
            "100% client-side processing"
        ]
    }
    </script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        success: {
                            500: '#10b981',
                            600: '#059669'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body class="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 min-h-screen">

    <!-- Navigation -->
    <nav class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl animate-bounce-gentle">🔄</div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">API2CSV</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400">JSON to CSV Converter</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="https://github.com/NeethDseven/api2csv"
                       class="flex items-center space-x-2 bg-gray-900 dark:bg-white text-white dark:text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">GitHub</span>
                    </a>

                    <button id="darkModeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div class="absolute top-0 left-0 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div class="absolute top-0 right-0 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow" style="animation-delay: 1s;"></div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
            <div class="text-center">
                <!-- Viral Badge -->
                <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-8 animate-fade-in">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span id="heroCounter">3,247+ developers used this week</span>
                </div>

                <!-- Main Headline -->
                <h1 class="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 animate-slide-up">
                    <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        JSON to CSV
                    </span>
                    <br>
                    <span class="text-gray-900 dark:text-white">in Seconds</span>
                </h1>

                <!-- Subtitle -->
                <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto animate-slide-up" style="animation-delay: 0.2s;">
                    Convert API responses to CSV files instantly.
                    <span class="font-semibold text-blue-600 dark:text-blue-400">No signup, no coding, no hassle.</span>
                    Perfect for developers and data analysts.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-slide-up" style="animation-delay: 0.4s;">
                    <a href="#app"
                       class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                        🚀 Try It Free Now
                    </a>

                    <a href="#demo"
                       class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-200">
                        📹 Watch Demo
                    </a>
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500 dark:text-gray-400 animate-fade-in" style="animation-delay: 0.6s;">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>100% Free</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Secure & Private</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        <span>No Signup Required</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Instant Download</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Comparatif Gratuit vs Premium -->
    <section id="premium" class="max-w-4xl mx-auto my-16 bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
      <h2 class="text-3xl font-bold mb-6 text-center text-gray-900 dark:text-white">Gratuit <span class="text-gray-400">vs</span> Premium</h2>
      <div class="overflow-x-auto">
        <table class="w-full text-center border-collapse">
          <thead>
            <tr class="bg-gray-100 dark:bg-gray-700">
              <th class="py-3 px-2">Fonctionnalité</th>
              <th class="py-3 px-2">Gratuit</th>
              <th class="py-3 px-2 text-green-600 dark:text-green-400">Premium</th>
            </tr>
          </thead>
          <tbody>
            <tr><td class="py-2">Conversion JSON → CSV</td><td>✔️</td><td>✔️</td></tr>
            <tr><td class="py-2">Upload de fichiers JSON</td><td>❌</td><td>✔️</td></tr>
            <tr><td class="py-2">Support de grandes structures/API</td><td>❌</td><td>✔️</td></tr>
            <tr><td class="py-2">Export Excel, XML, Google Sheets</td><td>❌</td><td>✔️</td></tr>
            <tr><td class="py-2">Historique & stockage en ligne</td><td>❌</td><td>✔️</td></tr>
            <tr><td class="py-2">Compte Pro / Cloud sync / API privée</td><td>❌</td><td>✔️</td></tr>
          </tbody>
        </table>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
        <a href="https://gumroad.com/" target="_blank">
          <button class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold text-lg shadow">Obtenir la version Premium (Gumroad)</button>
        </a>
        <a href="https://stripe.com/" target="_blank">
          <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold text-lg shadow">Abonnement Premium (Stripe)</button>
        </a>
      </div>
    </section>

    <!-- Section Témoignages -->
    <section class="max-w-3xl mx-auto my-12">
      <h2 class="text-2xl font-bold mb-4 text-center text-gray-900 dark:text-white">Témoignages</h2>
      <div class="space-y-4">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 shadow">
          “Super pratique pour convertir mes exports d’API en CSV !”<br><span class="font-semibold">— Julie, développeuse</span>
        </div>
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 shadow">
          “La version premium m’a fait gagner un temps fou sur mes gros fichiers.”<br><span class="font-semibold">— Maxime, data analyst</span>
        </div>
      </div>
    </section>

    <!-- Section FAQ -->
    <section class="max-w-3xl mx-auto my-12">
      <h2 class="text-2xl font-bold mb-4 text-center text-gray-900 dark:text-white">FAQ</h2>
      <div class="space-y-4">
        <div>
          <div class="font-semibold">Comment fonctionne la version gratuite ?</div>
          <div>Collez votre JSON, cliquez, récupérez le CSV. Simple et rapide !</div>
        </div>
        <div>
          <div class="font-semibold">Comment débloquer les fonctions premium ?</div>
          <div>En achetant la version sur Gumroad ou via abonnement Stripe, vous recevez un accès immédiat.</div>
        </div>
        <div>
          <div class="font-semibold">Puis-je utiliser api2csv pour des fichiers volumineux ?</div>
          <div>Oui, la version premium est optimisée pour les gros volumes et les API complexes.</div>
        </div>
        <div>
          <div class="font-semibold">Un support est-il inclus ?</div>
          <div>Oui, vous pouvez contacter le support par email après achat.</div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-900 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col sm:flex-row justify-between items-center">
                <div class="text-center sm:text-left mb-4 sm:mb-0">
                    <a href="#" class="text-3xl font-bold text-gray-900 dark:text-white">API2CSV</a>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#"
                       class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                        Privacy Policy
                    </a>
                    <a href="#"
                       class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                        Terms of Service
                    </a>
                </div>

                <div class="text-center sm:text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        &copy; 2023 API2CSV. All rights reserved.
                    </p>
                </div