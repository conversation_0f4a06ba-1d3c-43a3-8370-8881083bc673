using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;
using System.Collections;
using ChronoForge.Core;

namespace ChronoForge.Audio
{
    /// <summary>
    /// Gestionnaire audio principal pour ChronoForge
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("Audio Mixer")]
        public AudioMixerGroup masterMixerGroup;
        public AudioMixerGroup musicMixerGroup;
        public AudioMixerGroup sfxMixerGroup;
        public AudioMixerGroup ambientMixerGroup;
        public AudioMixerGroup uiMixerGroup;
        
        [Header("Audio Sources")]
        public AudioSource musicSource;
        public AudioSource ambientSource;
        public AudioSource uiSource;
        public GameObject sfxSourcePrefab;
        
        [Header("Music Settings")]
        public float musicFadeDuration = 2f;
        public bool loopMusic = true;
        public float musicVolume = 0.7f;
        
        [Header("SFX Settings")]
        public int maxSFXSources = 20;
        public float sfxVolume = 0.8f;
        
        [Header("Ambient Settings")]
        public float ambientVolume = 0.5f;
        public float ambientFadeDuration = 1f;
        
        [Header("Audio Libraries")]
        public AudioLibrary musicLibrary;
        public AudioLibrary sfxLibrary;
        public AudioLibrary ambientLibrary;
        public AudioLibrary uiLibrary;
        
        // Events
        public static System.Action<string> OnMusicChanged;
        public static System.Action<string> OnSFXPlayed;
        public static System.Action<float> OnVolumeChanged;
        
        // Audio source pools
        private Queue<AudioSource> availableSFXSources = new Queue<AudioSource>();
        private List<AudioSource> activeSFXSources = new List<AudioSource>();
        
        // State
        private string currentMusicTrack = "";
        private string currentAmbientTrack = "";
        private bool isMusicFading = false;
        private bool isAmbientFading = false;
        
        // Volume settings
        private float masterVolume = 1f;
        private float currentMusicVolume = 1f;
        private float currentSFXVolume = 1f;
        private float currentAmbientVolume = 1f;
        private float currentUIVolume = 1f;
        
        // Singleton
        public static AudioManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            LoadAudioSettings();
        }
        
        private void Update()
        {
            UpdateAudioSources();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeAudioManager()
        {
            // Setup audio sources
            SetupAudioSources();
            
            // Create SFX source pool
            CreateSFXSourcePool();
            
            // Load audio libraries
            LoadAudioLibraries();
            
            UnityEngine.Debug.Log("🔊 AudioManager initialized");
        }
        
        private void SetupAudioSources()
        {
            // Setup music source
            if (musicSource == null)
            {
                GameObject musicObj = new GameObject("MusicSource");
                musicObj.transform.SetParent(transform);
                musicSource = musicObj.AddComponent<AudioSource>();
            }
            
            musicSource.outputAudioMixerGroup = musicMixerGroup;
            musicSource.loop = loopMusic;
            musicSource.playOnAwake = false;
            musicSource.volume = musicVolume;
            
            // Setup ambient source
            if (ambientSource == null)
            {
                GameObject ambientObj = new GameObject("AmbientSource");
                ambientObj.transform.SetParent(transform);
                ambientSource = ambientObj.AddComponent<AudioSource>();
            }
            
            ambientSource.outputAudioMixerGroup = ambientMixerGroup;
            ambientSource.loop = true;
            ambientSource.playOnAwake = false;
            ambientSource.volume = ambientVolume;
            
            // Setup UI source
            if (uiSource == null)
            {
                GameObject uiObj = new GameObject("UISource");
                uiObj.transform.SetParent(transform);
                uiSource = uiObj.AddComponent<AudioSource>();
            }
            
            uiSource.outputAudioMixerGroup = uiMixerGroup;
            uiSource.playOnAwake = false;
        }
        
        private void CreateSFXSourcePool()
        {
            // Create SFX source prefab if none assigned
            if (sfxSourcePrefab == null)
            {
                sfxSourcePrefab = new GameObject("SFXSource");
                AudioSource source = sfxSourcePrefab.AddComponent<AudioSource>();
                source.outputAudioMixerGroup = sfxMixerGroup;
                source.playOnAwake = false;
            }
            
            // Create pool of SFX sources
            for (int i = 0; i < maxSFXSources; i++)
            {
                GameObject sfxObj = Instantiate(sfxSourcePrefab, transform);
                sfxObj.name = $"SFXSource_{i}";
                AudioSource source = sfxObj.GetComponent<AudioSource>();
                availableSFXSources.Enqueue(source);
            }
        }
        
        private void LoadAudioLibraries()
        {
            // Create default libraries if none assigned
            if (musicLibrary == null)
                musicLibrary = CreateDefaultMusicLibrary();
            
            if (sfxLibrary == null)
                sfxLibrary = CreateDefaultSFXLibrary();
            
            if (ambientLibrary == null)
                ambientLibrary = CreateDefaultAmbientLibrary();
            
            if (uiLibrary == null)
                uiLibrary = CreateDefaultUILibrary();
        }
        
        #endregion
        
        #region Music Management
        
        public void PlayMusic(string trackName, bool fadeIn = true)
        {
            if (currentMusicTrack == trackName && musicSource.isPlaying) return;
            
            AudioClip clip = GetMusicClip(trackName);
            if (clip == null)
            {
                UnityEngine.Debug.LogWarning($"Music track not found: {trackName}");
                return;
            }
            
            if (fadeIn && musicSource.isPlaying)
            {
                StartCoroutine(CrossfadeMusic(clip, trackName));
            }
            else
            {
                musicSource.clip = clip;
                musicSource.Play();
                currentMusicTrack = trackName;
                
                if (fadeIn)
                {
                    StartCoroutine(FadeInMusic());
                }
            }
            
            OnMusicChanged?.Invoke(trackName);
            UnityEngine.Debug.Log($"🎵 Playing music: {trackName}");
        }
        
        public void StopMusic(bool fadeOut = true)
        {
            if (fadeOut)
            {
                StartCoroutine(FadeOutMusic());
            }
            else
            {
                musicSource.Stop();
                currentMusicTrack = "";
            }
        }
        
        public void PauseMusic()
        {
            musicSource.Pause();
        }
        
        public void ResumeMusic()
        {
            musicSource.UnPause();
        }
        
        private IEnumerator CrossfadeMusic(AudioClip newClip, string trackName)
        {
            if (isMusicFading) yield break;
            
            isMusicFading = true;
            
            // Fade out current music
            yield return StartCoroutine(FadeOutMusic(false));
            
            // Switch to new clip
            musicSource.clip = newClip;
            musicSource.Play();
            currentMusicTrack = trackName;
            
            // Fade in new music
            yield return StartCoroutine(FadeInMusic());
            
            isMusicFading = false;
        }
        
        private IEnumerator FadeInMusic()
        {
            float startVolume = 0f;
            float targetVolume = musicVolume * currentMusicVolume;
            
            musicSource.volume = startVolume;
            
            float elapsed = 0f;
            while (elapsed < musicFadeDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / musicFadeDuration;
                musicSource.volume = Mathf.Lerp(startVolume, targetVolume, progress);
                yield return null;
            }
            
            musicSource.volume = targetVolume;
        }
        
        private IEnumerator FadeOutMusic(bool stopAfterFade = true)
        {
            float startVolume = musicSource.volume;
            float targetVolume = 0f;
            
            float elapsed = 0f;
            while (elapsed < musicFadeDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / musicFadeDuration;
                musicSource.volume = Mathf.Lerp(startVolume, targetVolume, progress);
                yield return null;
            }
            
            musicSource.volume = targetVolume;
            
            if (stopAfterFade)
            {
                musicSource.Stop();
                currentMusicTrack = "";
            }
        }
        
        #endregion
        
        #region SFX Management
        
        public void PlaySFX(string sfxName, float volume = 1f, float pitch = 1f, Vector3? position = null)
        {
            AudioClip clip = GetSFXClip(sfxName);
            if (clip == null)
            {
                UnityEngine.Debug.LogWarning($"SFX not found: {sfxName}");
                return;
            }
            
            AudioSource source = GetAvailableSFXSource();
            if (source == null)
            {
                UnityEngine.Debug.LogWarning("No available SFX sources");
                return;
            }
            
            // Setup source
            source.clip = clip;
            source.volume = volume * sfxVolume * currentSFXVolume;
            source.pitch = pitch;
            
            // Set position if specified
            if (position.HasValue)
            {
                source.transform.position = position.Value;
                source.spatialBlend = 1f; // 3D sound
            }
            else
            {
                source.spatialBlend = 0f; // 2D sound
            }
            
            // Play and track
            source.Play();
            activeSFXSources.Add(source);
            
            OnSFXPlayed?.Invoke(sfxName);
            
            UnityEngine.Debug.Log($"🔊 Playing SFX: {sfxName}");
        }
        
        public void PlaySFXOneShot(string sfxName, float volume = 1f)
        {
            AudioClip clip = GetSFXClip(sfxName);
            if (clip == null) return;
            
            AudioSource source = GetAvailableSFXSource();
            if (source == null) return;
            
            source.volume = volume * sfxVolume * currentSFXVolume;
            source.PlayOneShot(clip);
            
            // Return to pool after clip duration
            StartCoroutine(ReturnSFXSourceAfterDelay(source, clip.length));
        }
        
        private AudioSource GetAvailableSFXSource()
        {
            if (availableSFXSources.Count > 0)
            {
                return availableSFXSources.Dequeue();
            }
            
            // Try to find an inactive source
            foreach (AudioSource source in activeSFXSources)
            {
                if (!source.isPlaying)
                {
                    activeSFXSources.Remove(source);
                    return source;
                }
            }
            
            return null;
        }
        
        private IEnumerator ReturnSFXSourceAfterDelay(AudioSource source, float delay)
        {
            yield return new WaitForSeconds(delay);
            ReturnSFXSourceToPool(source);
        }
        
        private void ReturnSFXSourceToPool(AudioSource source)
        {
            if (activeSFXSources.Contains(source))
            {
                activeSFXSources.Remove(source);
            }
            
            source.Stop();
            source.clip = null;
            source.spatialBlend = 0f;
            source.pitch = 1f;
            availableSFXSources.Enqueue(source);
        }
        
        #endregion
        
        #region Ambient Management
        
        public void PlayAmbient(string ambientName, bool fadeIn = true)
        {
            if (currentAmbientTrack == ambientName && ambientSource.isPlaying) return;
            
            AudioClip clip = GetAmbientClip(ambientName);
            if (clip == null)
            {
                UnityEngine.Debug.LogWarning($"Ambient track not found: {ambientName}");
                return;
            }
            
            if (fadeIn && ambientSource.isPlaying)
            {
                StartCoroutine(CrossfadeAmbient(clip, ambientName));
            }
            else
            {
                ambientSource.clip = clip;
                ambientSource.Play();
                currentAmbientTrack = ambientName;
                
                if (fadeIn)
                {
                    StartCoroutine(FadeInAmbient());
                }
            }
            
            UnityEngine.Debug.Log($"🌊 Playing ambient: {ambientName}");
        }
        
        public void StopAmbient(bool fadeOut = true)
        {
            if (fadeOut)
            {
                StartCoroutine(FadeOutAmbient());
            }
            else
            {
                ambientSource.Stop();
                currentAmbientTrack = "";
            }
        }
        
        private IEnumerator CrossfadeAmbient(AudioClip newClip, string trackName)
        {
            if (isAmbientFading) yield break;
            
            isAmbientFading = true;
            
            yield return StartCoroutine(FadeOutAmbient(false));
            
            ambientSource.clip = newClip;
            ambientSource.Play();
            currentAmbientTrack = trackName;
            
            yield return StartCoroutine(FadeInAmbient());
            
            isAmbientFading = false;
        }
        
        private IEnumerator FadeInAmbient()
        {
            float startVolume = 0f;
            float targetVolume = ambientVolume * currentAmbientVolume;
            
            ambientSource.volume = startVolume;
            
            float elapsed = 0f;
            while (elapsed < ambientFadeDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / ambientFadeDuration;
                ambientSource.volume = Mathf.Lerp(startVolume, targetVolume, progress);
                yield return null;
            }
            
            ambientSource.volume = targetVolume;
        }
        
        private IEnumerator FadeOutAmbient(bool stopAfterFade = true)
        {
            float startVolume = ambientSource.volume;
            float targetVolume = 0f;
            
            float elapsed = 0f;
            while (elapsed < ambientFadeDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / ambientFadeDuration;
                ambientSource.volume = Mathf.Lerp(startVolume, targetVolume, progress);
                yield return null;
            }
            
            ambientSource.volume = targetVolume;
            
            if (stopAfterFade)
            {
                ambientSource.Stop();
                currentAmbientTrack = "";
            }
        }
        
        #endregion
        
        #region UI Audio
        
        public void PlayUI(string uiSoundName, float volume = 1f)
        {
            AudioClip clip = GetUIClip(uiSoundName);
            if (clip == null) return;
            
            uiSource.volume = volume * currentUIVolume;
            uiSource.PlayOneShot(clip);
        }
        
        #endregion
        
        #region Volume Control
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
            OnVolumeChanged?.Invoke(masterVolume);
        }
        
        public void SetMusicVolume(float volume)
        {
            currentMusicVolume = Mathf.Clamp01(volume);
            musicSource.volume = musicVolume * currentMusicVolume * masterVolume;
        }
        
        public void SetSFXVolume(float volume)
        {
            currentSFXVolume = Mathf.Clamp01(volume);
            // SFX volume is applied when playing sounds
        }
        
        public void SetAmbientVolume(float volume)
        {
            currentAmbientVolume = Mathf.Clamp01(volume);
            ambientSource.volume = ambientVolume * currentAmbientVolume * masterVolume;
        }
        
        public void SetUIVolume(float volume)
        {
            currentUIVolume = Mathf.Clamp01(volume);
            // UI volume is applied when playing sounds
        }
        
        private void ApplyVolumeSettings()
        {
            if (masterMixerGroup != null)
            {
                float dbVolume = masterVolume > 0 ? 20f * Mathf.Log10(masterVolume) : -80f;
                masterMixerGroup.audioMixer.SetFloat("MasterVolume", dbVolume);
            }
        }
        
        #endregion
        
        #region Audio Clip Retrieval
        
        private AudioClip GetMusicClip(string name)
        {
            return musicLibrary?.GetClip(name);
        }
        
        private AudioClip GetSFXClip(string name)
        {
            return sfxLibrary?.GetClip(name);
        }
        
        private AudioClip GetAmbientClip(string name)
        {
            return ambientLibrary?.GetClip(name);
        }
        
        private AudioClip GetUIClip(string name)
        {
            return uiLibrary?.GetClip(name);
        }
        
        #endregion
        
        #region Update and Cleanup
        
        private void UpdateAudioSources()
        {
            // Clean up finished SFX sources
            for (int i = activeSFXSources.Count - 1; i >= 0; i--)
            {
                if (!activeSFXSources[i].isPlaying)
                {
                    ReturnSFXSourceToPool(activeSFXSources[i]);
                }
            }
        }
        
        #endregion
        
        #region Settings
        
        private void LoadAudioSettings()
        {
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            currentMusicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            currentSFXVolume = PlayerPrefs.GetFloat("SFXVolume", 0.8f);
            currentAmbientVolume = PlayerPrefs.GetFloat("AmbientVolume", 0.5f);
            currentUIVolume = PlayerPrefs.GetFloat("UIVolume", 1f);
            
            ApplyVolumeSettings();
        }
        
        public void SaveAudioSettings()
        {
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", currentMusicVolume);
            PlayerPrefs.SetFloat("SFXVolume", currentSFXVolume);
            PlayerPrefs.SetFloat("AmbientVolume", currentAmbientVolume);
            PlayerPrefs.SetFloat("UIVolume", currentUIVolume);
            PlayerPrefs.Save();
        }
        
        #endregion
        
        #region Default Libraries
        
        private AudioLibrary CreateDefaultMusicLibrary()
        {
            AudioLibrary library = ScriptableObject.CreateInstance<AudioLibrary>();
            library.libraryName = "Default Music Library";
            return library;
        }
        
        private AudioLibrary CreateDefaultSFXLibrary()
        {
            AudioLibrary library = ScriptableObject.CreateInstance<AudioLibrary>();
            library.libraryName = "Default SFX Library";
            return library;
        }
        
        private AudioLibrary CreateDefaultAmbientLibrary()
        {
            AudioLibrary library = ScriptableObject.CreateInstance<AudioLibrary>();
            library.libraryName = "Default Ambient Library";
            return library;
        }
        
        private AudioLibrary CreateDefaultUILibrary()
        {
            AudioLibrary library = ScriptableObject.CreateInstance<AudioLibrary>();
            library.libraryName = "Default UI Library";
            return library;
        }
        
        #endregion
        
        #region Public Getters
        
        public bool IsMusicPlaying()
        {
            return musicSource.isPlaying;
        }
        
        public string GetCurrentMusicTrack()
        {
            return currentMusicTrack;
        }
        
        public string GetCurrentAmbientTrack()
        {
            return currentAmbientTrack;
        }
        
        public float GetMasterVolume()
        {
            return masterVolume;
        }
        
        public float GetMusicVolume()
        {
            return currentMusicVolume;
        }
        
        public float GetSFXVolume()
        {
            return currentSFXVolume;
        }
        
        public float GetAmbientVolume()
        {
            return currentAmbientVolume;
        }
        
        public float GetUIVolume()
        {
            return currentUIVolume;
        }
        
        #endregion
    }
}
