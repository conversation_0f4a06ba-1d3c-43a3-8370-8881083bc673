# 🛠️ Guide de Configuration des Prefabs ChronoForge

## 📁 Structure des Dossiers

Créez cette structure dans Unity :

```
Assets/Prefabs/
├── Core/
│   ├── GameManager.prefab
│   ├── RunManager.prefab
│   └── SaveSystem.prefab
├── Player/
│   ├── Player.prefab
│   └── PlayerUI.prefab
├── UI/
│   ├── MainCanvas.prefab
│   ├── HUD.prefab
│   └── MenuCanvas.prefab
├── Audio/
│   └── AudioManager.prefab
├── Effects/
│   └── EffectsManager.prefab
└── World/
    ├── LevelGenerator.prefab
    └── Room.prefab
```

## 🎯 1. Player Prefab

### Étapes de création :

1. **Créer un GameObject vide** : `GameObject → Create Empty`
2. **Renommer** : "Player"
3. **Ajouter les composants** :

#### Transform
- Position: (0, 0, 0)
- Rotation: (0, 0, 0)
- Scale: (1, 1, 1)

#### SpriteRenderer
- Sprite: (Votre sprite de joueur)
- Color: White
- Sorting Layer: Player
- Order in Layer: 0

#### Rigidbody2D
- Body Type: Dynamic
- Mass: 1
- Linear Drag: 5
- Angular Drag: 5
- Gravity Scale: 0
- Freeze Rotation Z: ✓

#### Collider2D
- BoxCollider2D ou CapsuleCollider2D
- Is Trigger: ✗
- Size: Ajuster selon le sprite

#### Scripts Player
- **PlayerController** (Assets/Scripts/Player/PlayerController.cs)
- **PlayerCombat** (Assets/Scripts/Player/PlayerCombat.cs)
- **HealthSystem** (Assets/Scripts/Player/HealthSystem.cs)
- **PlayerStats** (Assets/Scripts/Player/PlayerStats.cs)

#### Enfants du Player
Créez ces GameObjects enfants :

**AttackPoint** (Empty GameObject)
- Position: (0, -0.5, 0)

**HealthBar** (UI → Slider)
- Canvas: World Space
- Position: (0, 1, 0)
- Scale: (0.01, 0.01, 0.01)

## 🎮 2. GameManager Prefab

### Étapes de création :

1. **Créer un GameObject vide** : "GameManager"
2. **Ajouter les scripts** :

#### Scripts Core
- **GameManager** (Assets/Scripts/Core/GameManager.cs)
- **RunManager** (Assets/Scripts/Core/RunManager.cs)
- **ProgressionManager** (Assets/Scripts/Core/ProgressionManager.cs)
- **GameSettings** (Assets/Scripts/Core/GameSettings.cs)
- **SaveSystem** (Assets/Scripts/Core/SaveSystem.cs)
- **GameIntegration** (Assets/Scripts/Core/GameIntegration.cs)

#### Configuration GameManager
- Game State: Menu
- Debug Mode: ✓ (pour les tests)

#### Configuration RunManager
- Max Floors: 10
- Base Room Count: 5
- Difficulty Scaling: 1.2

## 🖼️ 3. UI Canvas Prefab

### Étapes de création :

1. **Créer un Canvas** : `UI → Canvas`
2. **Renommer** : "MainCanvas"
3. **Configuration Canvas** :

#### Canvas
- Render Mode: Screen Space - Overlay
- Pixel Perfect: ✓
- Sort Order: 0

#### Canvas Scaler
- UI Scale Mode: Scale With Screen Size
- Reference Resolution: 1920x1080
- Screen Match Mode: Match Width Or Height
- Match: 0.5

#### Graphic Raycaster
- Ignore Reversed Graphics: ✓
- Blocking Objects: None

### Enfants du Canvas :

**HUD** (Panel)
- Anchors: Stretch
- Margins: (0, 0, 0, 0)
- Script: **HUDController**

**MenuPanel** (Panel)
- Anchors: Stretch
- Active: ✗
- Script: **MenuController**

**InventoryPanel** (Panel)
- Anchors: Center
- Size: (800, 600)
- Active: ✗
- Script: **InventoryController**

**SettingsPanel** (Panel)
- Anchors: Center
- Size: (600, 400)
- Active: ✗
- Script: **SettingsController**

#### Script UIManager
Ajouter le script **UIManager** au Canvas principal

## 🔊 4. Audio Manager Prefab

### Étapes de création :

1. **Créer un GameObject vide** : "AudioManager"
2. **Ajouter les composants** :

#### AudioSource (Musique)
- Play On Awake: ✗
- Loop: ✓
- Volume: 0.7
- Priority: 128

#### AudioSource (SFX)
- Play On Awake: ✗
- Loop: ✗
- Volume: 1.0
- Priority: 128

#### AudioSource (Ambiant)
- Play On Awake: ✗
- Loop: ✓
- Volume: 0.5
- Priority: 128

#### Script AudioManager
- **AudioManager** (Assets/Scripts/Audio/AudioManager.cs)

## ✨ 5. Effects Manager Prefab

### Étapes de création :

1. **Créer un GameObject vide** : "EffectsManager"
2. **Ajouter le script** :
   - **EffectsManager** (Assets/Scripts/Effects/EffectsManager.cs)

## 🌍 6. Level Generator Prefab

### Étapes de création :

1. **Créer un GameObject vide** : "LevelGenerator"
2. **Ajouter le script** :
   - **LevelGenerator** (Assets/Scripts/Procedural/LevelGenerator.cs)

## 🔧 Configuration des Références

### Dans GameManager :
- Player Prefab: Glisser le Player prefab
- UI Manager: Glisser le MainCanvas prefab
- Audio Manager: Glisser l'AudioManager prefab

### Dans Player :
- Attack Point: Assigner l'enfant AttackPoint
- Health Bar: Assigner l'enfant HealthBar

### Dans UIManager :
- HUD Controller: Assigner le HUD panel
- Menu Controller: Assigner le MenuPanel
- Inventory Controller: Assigner l'InventoryPanel

## 🎯 Sauvegarde des Prefabs

1. **Glisser chaque GameObject** configuré dans le dossier Prefabs approprié
2. **Vérifier les références** après création du prefab
3. **Tester chaque prefab** en le plaçant dans une scène

## ✅ Checklist de Vérification

- [ ] Player prefab avec tous les scripts
- [ ] GameManager avec tous les managers
- [ ] UI Canvas avec tous les panels
- [ ] AudioManager configuré
- [ ] EffectsManager ajouté
- [ ] LevelGenerator configuré
- [ ] Toutes les références assignées
- [ ] Prefabs sauvegardés dans les bons dossiers

## 🚀 Test Final

1. **Créer une nouvelle scène**
2. **Ajouter GameManager prefab**
3. **Ajouter Player prefab**
4. **Ajouter MainCanvas prefab**
5. **Appuyer sur Play**
6. **Vérifier que tout fonctionne**

**🌟 Vos prefabs ChronoForge sont maintenant prêts ! 🌟**
