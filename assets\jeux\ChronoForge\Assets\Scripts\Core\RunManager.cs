using UnityEngine;
using System.Collections.Generic;
using ChronoForge.Player;
using ChronoForge.Loot;
using ChronoForge.Procedural;

namespace ChronoForge.Core
{
    /// <summary>
    /// Gestionnaire des runs individuelles dans ChronoForge
    /// </summary>
    public class RunManager : MonoBehaviour
    {
        [Header("Run Settings")]
        public int maxRooms = 15;
        public int currentRoom = 0;
        public float runStartTime;
        public int runSeed;
        
        [Header("Run Progression")]
        public int roomsCleared = 0;
        public int enemiesKilled = 0;
        public int itemsCollected = 0;
        public int bossesDefeated = 0;
        
        [Header("Current Run Data")]
        public RunData currentRun;
        public List<ItemData> collectedItems = new List<ItemData>();
        public List<string> visitedRooms = new List<string>();
        
        // Events
        public static System.Action OnRunStarted;
        public static System.Action OnRunCompleted;
        public static System.Action OnRunFailed;
        public static System.Action<int> OnRoomChanged;
        public static System.Action<ItemData> OnItemCollected;
        public static System.Action OnEnemyKilled;
        public static System.Action OnBossDefeated;
        
        // Private fields
        private LevelGenerator levelGenerator;
        private PlayerController player;
        private bool runActive = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeRunManager();
        }
        
        private void Start()
        {
            FindReferences();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeRunManager()
        {
            // Initialize run data
            currentRun = new RunData();
            
            // Subscribe to events
            SubscribeToEvents();
            
            UnityEngine.Debug.Log("🎯 RunManager initialized");
        }
        
        private void FindReferences()
        {
            if (levelGenerator == null)
                levelGenerator = FindFirstObjectByType<LevelGenerator>();
            
            if (player == null)
                player = FindFirstObjectByType<PlayerController>();
        }
        
        #endregion
        
        #region Run Management
        
        public void StartNewRun()
        {
            UnityEngine.Debug.Log("🚀 Starting new run...");
            
            // Generate new seed
            runSeed = Random.Range(0, int.MaxValue);
            Random.InitState(runSeed);
            
            // Initialize run data
            InitializeNewRun();
            
            // Generate first level
            if (levelGenerator != null)
            {
                levelGenerator.GenerateLevel(runSeed);
            }
            
            // Reset player
            if (player != null)
            {
                player.ResetForNewRun();
            }
            
            runActive = true;
            OnRunStarted?.Invoke();
            
            UnityEngine.Debug.Log($"✅ New run started with seed: {runSeed}");
        }
        
        public void RestartCurrentRun()
        {
            UnityEngine.Debug.Log("🔄 Restarting current run...");
            
            // Use same seed for consistency
            Random.InitState(runSeed);
            
            // Reset run data but keep seed
            ResetRunData();
            
            // Regenerate level with same seed
            if (levelGenerator != null)
            {
                levelGenerator.GenerateLevel(runSeed);
            }
            
            // Reset player
            if (player != null)
            {
                player.ResetForNewRun();
            }
            
            runActive = true;
            OnRunStarted?.Invoke();
        }
        
        public void CompleteRun()
        {
            if (!runActive) return;
            
            UnityEngine.Debug.Log("🏆 Run completed successfully!");
            
            // Calculate run statistics
            CalculateRunStats();
            
            // Award progression rewards
            AwardRunRewards();
            
            // End run
            EndRun(true);
            
            OnRunCompleted?.Invoke();
        }
        
        public void EndRun(bool victory)
        {
            if (!runActive) return;
            
            runActive = false;
            currentRun.completed = victory;
            currentRun.endTime = Time.time;
            currentRun.duration = currentRun.endTime - runStartTime;
            
            UnityEngine.Debug.Log($"🔚 Run ended - Victory: {victory}, Duration: {currentRun.duration:F1}s");
            
            // Save run statistics
            SaveRunStatistics();
            
            if (!victory)
            {
                OnRunFailed?.Invoke();
            }
        }
        
        #endregion
        
        #region Room Management
        
        public void EnterRoom(string roomId)
        {
            currentRoom++;
            visitedRooms.Add(roomId);
            
            UnityEngine.Debug.Log($"🚪 Entered room {currentRoom}: {roomId}");
            
            OnRoomChanged?.Invoke(currentRoom);
        }
        
        public void ClearRoom()
        {
            roomsCleared++;
            
            UnityEngine.Debug.Log($"✅ Room cleared! Total: {roomsCleared}");
            
            // Check if run is complete
            if (roomsCleared >= maxRooms)
            {
                CompleteRun();
            }
        }
        
        public bool IsLastRoom()
        {
            return currentRoom >= maxRooms;
        }
        
        public float GetRunProgress()
        {
            return (float)roomsCleared / maxRooms;
        }
        
        #endregion
        
        #region Item Management
        
        public void CollectItem(ItemData item)
        {
            if (item == null) return;
            
            collectedItems.Add(item);
            itemsCollected++;
            
            UnityEngine.Debug.Log($"📦 Item collected: {item.itemName}");
            
            OnItemCollected?.Invoke(item);
        }
        
        public bool HasItem(string itemId)
        {
            return collectedItems.Exists(item => item.itemId == itemId);
        }
        
        public List<ItemData> GetCollectedItems()
        {
            return new List<ItemData>(collectedItems);
        }
        
        #endregion
        
        #region Combat Tracking
        
        public void RegisterEnemyKill()
        {
            enemiesKilled++;
            OnEnemyKilled?.Invoke();
        }
        
        public void RegisterBossDefeat()
        {
            bossesDefeated++;
            OnBossDefeated?.Invoke();
        }
        
        #endregion
        
        #region Run Statistics
        
        private void InitializeNewRun()
        {
            currentRun = new RunData
            {
                runId = System.Guid.NewGuid().ToString(),
                seed = runSeed,
                startTime = Time.time,
                playerClass = GetPlayerClass(),
                completed = false
            };
            
            runStartTime = Time.time;
            
            // Reset counters
            ResetRunData();
        }
        
        private void ResetRunData()
        {
            currentRoom = 0;
            roomsCleared = 0;
            enemiesKilled = 0;
            itemsCollected = 0;
            bossesDefeated = 0;
            
            collectedItems.Clear();
            visitedRooms.Clear();
        }
        
        private void CalculateRunStats()
        {
            currentRun.roomsCleared = roomsCleared;
            currentRun.enemiesKilled = enemiesKilled;
            currentRun.itemsCollected = itemsCollected;
            currentRun.bossesDefeated = bossesDefeated;
            currentRun.score = CalculateScore();
        }
        
        private int CalculateScore()
        {
            int score = 0;
            
            // Base score from progression
            score += roomsCleared * 100;
            score += enemiesKilled * 10;
            score += bossesDefeated * 500;
            score += itemsCollected * 25;
            
            // Time bonus (faster = better)
            float timeBonus = Mathf.Max(0, 1800f - currentRun.duration); // 30 minutes max
            score += Mathf.RoundToInt(timeBonus);
            
            // Completion bonus
            if (currentRun.completed)
            {
                score += 1000;
            }
            
            return score;
        }
        
        private void AwardRunRewards()
        {
            ProgressionManager progression = GameManager.Instance.GetProgressionManager();
            if (progression == null) return;
            
            // Award experience
            int expGained = roomsCleared * 10 + enemiesKilled * 5 + bossesDefeated * 100;
            progression.AddExperience(expGained);
            
            // Award currencies
            int arcaneShards = roomsCleared * 2 + bossesDefeated * 10;
            int memoryFragments = enemiesKilled + bossesDefeated * 5;
            int nanoSouls = bossesDefeated;
            
            progression.AddCurrency("ArcaneShards", arcaneShards);
            progression.AddCurrency("MemoryFragments", memoryFragments);
            progression.AddCurrency("NanoSouls", nanoSouls);
            
            UnityEngine.Debug.Log($"💰 Run rewards: {expGained} EXP, {arcaneShards} Shards, {memoryFragments} Fragments, {nanoSouls} Souls");
        }
        
        private void SaveRunStatistics()
        {
            // Save to progression manager
            ProgressionManager progression = GameManager.Instance.GetProgressionManager();
            if (progression != null)
            {
                progression.AddRunToHistory(currentRun);
            }
        }
        
        #endregion
        
        #region Event Management
        
        private void SubscribeToEvents()
        {
            // Subscribe to relevant events
            PlayerController.OnPlayerDeath += HandlePlayerDeath;
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            PlayerController.OnPlayerDeath -= HandlePlayerDeath;
        }
        
        private void HandlePlayerDeath()
        {
            EndRun(false);
        }
        
        #endregion
        
        #region Utility Methods
        
        public void UpdateRun()
        {
            if (!runActive) return;
            
            // Update run duration
            if (currentRun != null)
            {
                currentRun.duration = Time.time - runStartTime;
            }
        }
        
        private string GetPlayerClass()
        {
            if (player != null && player.GetComponent<PlayerStats>() != null)
            {
                return player.GetComponent<PlayerStats>().playerClass.ToString();
            }
            return "Unknown";
        }
        
        public RunData GetCurrentRunData()
        {
            return currentRun;
        }
        
        public bool IsRunActive()
        {
            return runActive;
        }
        
        public int GetCurrentSeed()
        {
            return runSeed;
        }

        public float GetRunTime()
        {
            return Time.time - runStartTime;
        }

        #endregion
    }
    
    /// <summary>
    /// Données d'une run
    /// </summary>
    [System.Serializable]
    public class RunData
    {
        public string runId;
        public int seed;
        public float startTime;
        public float endTime;
        public float duration;
        public string playerClass;
        public bool completed;
        public int roomsCleared;
        public int enemiesKilled;
        public int itemsCollected;
        public int bossesDefeated;
        public int score;
        public List<string> itemsFound = new List<string>();
        public List<string> roomsVisited = new List<string>();
    }
}
