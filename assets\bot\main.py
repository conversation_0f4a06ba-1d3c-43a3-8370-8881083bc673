#!/usr/bin/env python3
"""
Point d'entrée principal du bot de publication automatisé.
Alternative au CLI pour un usage programmatique.
"""

import sys
import logging
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import ConfigManager, setup_logging, validate_environment
from src.content_manager import ContentManager
from src.publisher import Publisher
from src.scheduler import BotScheduler

def main():
    """Point d'entrée principal."""
    print("🤖 Bot de Publication Automatisé")
    print("=" * 40)
    
    # Charger la configuration
    config = ConfigManager()
    setup_logging(config)
    
    logger = logging.getLogger(__name__)
    
    # Valider l'environnement
    if not validate_environment():
        logger.error("❌ Erreur de configuration de l'environnement")
        return 1
    
    # Vérifier les clés API
    api_validation = config.validate_api_keys()
    missing_apis = [platform for platform, valid in api_validation.items() if not valid]
    
    if missing_apis:
        logger.warning(f"⚠️  Clés API manquantes pour: {', '.join(missing_apis)}")
        logger.info("💡 Ces plateformes seront désactivées")
    
    # Initialiser les composants
    logger.info("🔧 Initialisation des composants...")
    content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
    publisher = Publisher(config.config)
    scheduler = BotScheduler(config.config, content_manager, publisher)
    
    # Charger les posts
    posts = content_manager.load_posts()
    logger.info(f"📝 {len(posts)} post(s) chargé(s)")
    
    # Authentifier les plateformes
    logger.info("🔐 Authentification des plateformes...")
    auth_results = publisher.authenticate_all()
    
    authenticated_platforms = [p for p, success in auth_results.items() if success]
    failed_platforms = [p for p, success in auth_results.items() if not success]
    
    if authenticated_platforms:
        logger.info(f"✅ Plateformes authentifiées: {', '.join(authenticated_platforms)}")
    
    if failed_platforms:
        logger.warning(f"❌ Échec d'authentification: {', '.join(failed_platforms)}")
    
    if not authenticated_platforms:
        logger.error("❌ Aucune plateforme authentifiée, arrêt du bot")
        return 1
    
    # Vérifier s'il y a des posts programmés
    from datetime import datetime
    scheduled_posts = content_manager.get_scheduled_posts(datetime.now())
    
    if scheduled_posts:
        logger.info(f"📅 {len(scheduled_posts)} post(s) programmé(s) pour maintenant")
    
    # Démarrer le planificateur
    if config.get('scheduler.enabled', True):
        logger.info("⏰ Démarrage du planificateur...")
        try:
            scheduler.start(blocking=True)
        except KeyboardInterrupt:
            logger.info("\n⏹️  Arrêt du planificateur...")
            scheduler.stop()
            logger.info("👋 Au revoir!")
            return 0
    else:
        logger.info("⚠️  Planificateur désactivé dans la configuration")
        
        # Mode manuel : publier les posts programmés une seule fois
        if scheduled_posts:
            logger.info("📤 Publication manuelle des posts programmés...")
            for post in scheduled_posts:
                results = publisher.publish_to_all_platforms(post)
                success_count = sum(1 for r in results if r.success)
                logger.info(f"📊 Post '{post.id}': {success_count}/{len(results)} plateformes")
        else:
            logger.info("📭 Aucun post programmé pour le moment")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
