using UnityEngine;

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Configuration pour les tests Space Clicker
    /// </summary>
    [CreateAssetMenu(fileName = "TestConfiguration", menuName = "Space Clicker/Test Configuration")]
    public class TestConfiguration : ScriptableObject
    {
        [Header("Test Execution")]
        [Tooltip("Run tests automatically when the test scene loads")]
        public bool autoRunTests = true;
        
        [Tooltip("Stop execution on the first test failure")]
        public bool stopOnFirstFailure = false;
        
        [Tooltip("Show detailed test results in the UI")]
        public bool showDetailedResults = true;
        
        [Tooltip("Maximum time to wait for a single test (seconds)")]
        public float testTimeout = 30f;
        
        [Head<PERSON>("Performance Tests")]
        [Tooltip("Run performance benchmarks")]
        public bool runPerformanceTests = false;
        
        [Tooltip("Number of iterations for performance tests")]
        public int performanceTestIterations = 1000;
        
        [Tooltip("Maximum acceptable time for performance tests (ms)")]
        public float maxPerformanceTime = 100f;
        
        [Head<PERSON>("Integration Tests")]
        [Tooltip("Run full integration tests")]
        public bool runIntegrationTests = true;
        
        [Tooltip("Simulate real gameplay scenarios")]
        public bool simulateGameplay = true;
        
        [Tooltip("Test save/load functionality")]
        public bool testSaveLoad = true;
        
        [Header("UI Tests")]
        [Tooltip("Test UI components and interactions")]
        public bool testUIComponents = true;
        
        [Tooltip("Test animations and visual effects")]
        public bool testAnimations = false; // Disabled by default as they're visual
        
        [Header("Audio Tests")]
        [Tooltip("Test audio system functionality")]
        public bool testAudioSystem = false; // Disabled by default to avoid noise
        
        [Header("Test Data")]
        [Tooltip("Use mock data for testing")]
        public bool useMockData = true;
        
        [Tooltip("Test with large numbers")]
        public bool testLargeNumbers = true;
        
        [Tooltip("Test edge cases and boundary conditions")]
        public bool testEdgeCases = true;
        
        [Header("Logging")]
        [Tooltip("Log level for test output")]
        public LogLevel testLogLevel = LogLevel.Info;
        
        [Tooltip("Save test results to file")]
        public bool saveResultsToFile = false;
        
        [Tooltip("File path for test results (relative to persistent data path)")]
        public string resultsFilePath = "TestResults/space_clicker_tests.txt";
        
        public enum LogLevel
        {
            None,
            Error,
            Warning,
            Info,
            Verbose
        }
        
        #region Test Categories
        
        [System.Serializable]
        public class TestCategory
        {
            public string name;
            public bool enabled;
            public string description;
        }
        
        [Header("Test Categories")]
        public TestCategory[] testCategories = new TestCategory[]
        {
            new TestCategory { name = "Core Systems", enabled = true, description = "ResourceManager, UpgradeManager, GameManager" },
            new TestCategory { name = "Utilities", enabled = true, description = "BigInteger extensions, helper functions" },
            new TestCategory { name = "UI Components", enabled = true, description = "UI controllers and interactions" },
            new TestCategory { name = "Integration", enabled = true, description = "Full system integration tests" },
            new TestCategory { name = "Performance", enabled = false, description = "Performance and stress tests" },
            new TestCategory { name = "Audio", enabled = false, description = "Audio system tests" }
        };
        
        #endregion
        
        #region Mock Data Configuration
        
        [Header("Mock Data Settings")]
        [Tooltip("Starting resources for mock tests")]
        public MockResourceData mockStartingResources = new MockResourceData
        {
            energy = 1000,
            minerals = 500,
            researchData = 100,
            spaceCurrency = 50
        };
        
        [Tooltip("Mock upgrade configurations")]
        public MockUpgradeData[] mockUpgrades = new MockUpgradeData[]
        {
            new MockUpgradeData 
            { 
                id = "test_solar_upgrade", 
                name = "Test Solar Panel", 
                baseCost = 100, 
                maxLevel = 5,
                effectMultiplier = 1.5f
            },
            new MockUpgradeData 
            { 
                id = "test_mining_upgrade", 
                name = "Test Mining Drill", 
                baseCost = 200, 
                maxLevel = 3,
                effectMultiplier = 2.0f
            }
        };
        
        [System.Serializable]
        public class MockResourceData
        {
            public long energy;
            public long minerals;
            public long researchData;
            public long spaceCurrency;
        }
        
        [System.Serializable]
        public class MockUpgradeData
        {
            public string id;
            public string name;
            public long baseCost;
            public int maxLevel;
            public float effectMultiplier;
        }
        
        #endregion
        
        #region Validation
        
        private void OnValidate()
        {
            // Ensure reasonable values
            testTimeout = Mathf.Max(1f, testTimeout);
            performanceTestIterations = Mathf.Max(1, performanceTestIterations);
            maxPerformanceTime = Mathf.Max(1f, maxPerformanceTime);
            
            // Ensure file path is valid
            if (string.IsNullOrEmpty(resultsFilePath))
            {
                resultsFilePath = "TestResults/space_clicker_tests.txt";
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Check if a specific test category is enabled
        /// </summary>
        public bool IsCategoryEnabled(string categoryName)
        {
            foreach (var category in testCategories)
            {
                if (category.name == categoryName)
                {
                    return category.enabled;
                }
            }
            return false;
        }
        
        /// <summary>
        /// Get the full path for test results file
        /// </summary>
        public string GetResultsFilePath()
        {
            return System.IO.Path.Combine(Application.persistentDataPath, resultsFilePath);
        }
        
        /// <summary>
        /// Should log at the specified level
        /// </summary>
        public bool ShouldLog(LogLevel level)
        {
            return level <= testLogLevel;
        }
        
        /// <summary>
        /// Get mock starting resources as BigInteger values
        /// </summary>
        public System.Collections.Generic.Dictionary<SpaceClicker.Core.ResourceType, System.Numerics.BigInteger> GetMockStartingResources()
        {
            return new System.Collections.Generic.Dictionary<SpaceClicker.Core.ResourceType, System.Numerics.BigInteger>
            {
                { SpaceClicker.Core.ResourceType.Energy, new System.Numerics.BigInteger(mockStartingResources.energy) },
                { SpaceClicker.Core.ResourceType.Minerals, new System.Numerics.BigInteger(mockStartingResources.minerals) },
                { SpaceClicker.Core.ResourceType.ResearchData, new System.Numerics.BigInteger(mockStartingResources.researchData) },
                { SpaceClicker.Core.ResourceType.SpaceCurrency, new System.Numerics.BigInteger(mockStartingResources.spaceCurrency) }
            };
        }
        
        /// <summary>
        /// Create a default test configuration
        /// </summary>
        public static TestConfiguration CreateDefault()
        {
            var config = CreateInstance<TestConfiguration>();
            config.name = "Default Test Configuration";
            return config;
        }
        
        #endregion
    }
}
