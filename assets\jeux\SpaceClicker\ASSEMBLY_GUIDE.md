# 🚀 Space Clicker - Guide d'Assemblage Final

Ce guide vous accompagne dans l'assemblage final de Space Clicker dans Unity.

## 📋 Prérequis

- Unity 2022.3 LTS ou plus récent
- Tous les scripts Space Clicker importés
- Outils d'édition configurés

## 🔧 Étape 1 : Préparation du Projet Unity

### 1.1 Créer le Projet Unity
```
1. Ouvrir Unity Hub
2. Cliquer sur "New Project"
3. Sélectionner "2D Core" template
4. Nom : "SpaceClicker"
5. Localisation : Choisir le dossier assets/jeux/SpaceClicker/
6. Cliquer "Create Project"
```

### 1.2 Importer les Scripts
```
1. Copier tout le contenu de assets/jeux/SpaceClicker/Assets/Scripts/
2. Dans Unity : Assets > Import New Asset
3. Sélectionner tous les fichiers .cs
4. Attendre la compilation (vérifier qu'il n'y a pas d'erreurs dans Console)
```

### 1.3 Configuration des Packages
```
1. Window > Package Manager
2. Installer les packages requis :
   - TextMeshPro (Essential)
   - 2D Sprite (Essential)
   - Test Framework (pour les tests)
   - LeanTween (Asset Store - optionnel mais recommandé)
```

## 🎨 Étape 2 : Génération des Assets

### 2.1 Générer les Assets Visuels
```
1. Space Clicker > Asset Generator
2. Vérifier les chemins :
   - Sprites Path: Assets/Sprites/
   - Materials Path: Assets/Materials/
   - Textures Path: Assets/Textures/
3. Cliquer "Generate All Assets"
4. Attendre la fin de la génération
5. Vérifier dans Project Window que les dossiers sont créés
```

### 2.2 Générer les Prefabs
```
1. Space Clicker > Prefab Generator
2. Cocher toutes les options :
   ✅ Generate Core Managers
   ✅ Generate UI Elements
   ✅ Generate Modules
   ✅ Generate Effects
   ✅ Generate Audio
3. Prefab Path: Assets/Prefabs/
4. Cliquer "Generate All Prefabs"
5. Vérifier que tous les prefabs sont créés sans erreur
```

## 🏗️ Étape 3 : Configuration de la Scène

### 3.1 Créer la Scène Principale
```
1. Space Clicker > Scene Setup
2. Scene Name: "GameScene"
3. Prefab Path: Assets/Prefabs/
4. Cocher toutes les options :
   ✅ Setup Core Managers
   ✅ Setup UI Canvas
   ✅ Setup Game Modules
   ✅ Setup Effects
   ✅ Setup Audio
   ✅ Setup Camera
   ✅ Setup Event System
5. Cliquer "Create New Scene"
6. Sauvegarder la scène : Ctrl+S
```

### 3.2 Vérification de la Hiérarchie
Après la génération, votre hiérarchie devrait ressembler à :
```
GameScene
├── Main Camera
├── EventSystem
├── GameSceneManager
├── GameManager
├── ResourceManager
├── UpgradeManager
├── AudioManager
├── ClickEffectManager
├── UIAnimationController
└── MainCanvas
    ├── Background
    ├── ResourceDisplays
    ├── ModulesContainer
    │   ├── SolarPanelModule
    │   ├── MiningDrillModule
    │   ├── ResearchLabModule
    │   └── SpaceStationModule
    └── UpgradesContainer
        └── UpgradeScrollView
```

## 🔗 Étape 4 : Connexion des Références

### 4.1 GameSceneManager
```
1. Sélectionner GameSceneManager dans la hiérarchie
2. Dans l'Inspector, connecter :
   - Game Manager: Glisser GameManager
   - Resource Manager: Glisser ResourceManager
   - Upgrade Manager: Glisser UpgradeManager
   - Main UI Controller: Glisser MainCanvas
   - UI Animation Controller: Glisser UIAnimationController
   - Click Effect Manager: Glisser ClickEffectManager
   - Audio Manager: Glisser AudioManager
```

### 4.2 MainUIController
```
1. Sélectionner MainCanvas dans la hiérarchie
2. Dans l'Inspector MainUIController, connecter :
   - Energy Display: Créer un ResourceDisplay pour l'énergie
   - Minerals Display: Créer un ResourceDisplay pour les minéraux
   - Research Display: Créer un ResourceDisplay pour la recherche
   - Currency Display: Créer un ResourceDisplay pour la devise
   - Upgrade Container: Glisser UpgradeScrollView/Content
```

### 4.3 Modules Cliquables
Pour chaque module (SolarPanel, MiningDrill, etc.) :
```
1. Vérifier que le Resource Type est correct
2. Vérifier que les références sont connectées :
   - Module Image: Image du module
   - Production Text: Texte de production
   - Click Particles: Système de particules
```

## 🎮 Étape 5 : Test Initial

### 5.1 Premier Test
```
1. Appuyer sur Play ▶️
2. Vérifier dans Console qu'il n'y a pas d'erreurs
3. Tester les clics sur les modules
4. Vérifier que les ressources augmentent
5. Tester l'achat d'upgrades
```

### 5.2 Tests Automatisés
```
1. Window > General > Test Runner
2. Onglet "PlayMode"
3. Cliquer "Run All"
4. Vérifier que tous les tests passent
```

## ⚙️ Étape 6 : Configuration et Optimisation

### 6.1 Paramètres de Qualité
```
1. Edit > Project Settings > Quality
2. Sélectionner "High" quality level
3. Anti Aliasing: 4x Multi Sampling
4. Anisotropic Textures: Per Texture
```

### 6.2 Paramètres Audio
```
1. Edit > Project Settings > Audio
2. DSP Buffer Size: Best Performance
3. Sample Rate: 48000 Hz
```

### 6.3 Paramètres d'Affichage
```
1. Edit > Project Settings > Player
2. Resolution and Presentation:
   - Default Screen Width: 1280
   - Default Screen Height: 720
   - Resizable Window: ✅
3. Icon: Utiliser l'icône générée ou importer une personnalisée
```

## 🎨 Étape 7 : Personnalisation Visuelle

### 7.1 Améliorer les Sprites
```
1. Remplacer les sprites générés par des versions personnalisées
2. Importer dans Assets/Sprites/
3. Configurer comme Sprite (2D and UI)
4. Mettre à jour les prefabs
```

### 7.2 Ajouter des Effets
```
1. Améliorer les systèmes de particules
2. Ajouter des animations avec LeanTween
3. Configurer les couleurs dans GameConstants
```

## 🔊 Étape 8 : Audio

### 8.1 Importer les Fichiers Audio
```
1. Créer Assets/Audio/Music/, Assets/Audio/SFX/, Assets/Audio/Ambient/
2. Importer les fichiers audio (.wav, .mp3, .ogg)
3. Configurer les AudioClips dans AudioManager
```

### 8.2 Configuration Audio
```
1. Sélectionner AudioManager
2. Connecter les AudioClips :
   - Main Menu Music
   - Gameplay Music
   - Click Sound
   - Upgrade Sound
   - etc.
```

## 🚀 Étape 9 : Build Final

### 9.1 Préparation du Build
```
1. File > Build Settings
2. Ajouter la scène GameScene
3. Sélectionner la plateforme cible
4. Player Settings:
   - Company Name: Votre nom/studio
   - Product Name: Space Clicker
   - Version: 1.0.0
```

### 9.2 Build Automatique
```
1. Space Clicker > Build Manager
2. Configurer :
   - Build Path: Builds/
   - App Name: SpaceClicker
   - Version: 1.0.0
3. Sélectionner les plateformes
4. Cliquer "Build All Selected Platforms"
```

## ✅ Checklist Finale

Avant la release, vérifier :

### Fonctionnalités
- [ ] Les modules génèrent des ressources au clic
- [ ] Les upgrades s'achètent et améliorent la production
- [ ] L'interface affiche correctement les valeurs
- [ ] La sauvegarde/chargement fonctionne
- [ ] Les animations sont fluides
- [ ] L'audio fonctionne correctement

### Performance
- [ ] 60 FPS stable en gameplay
- [ ] Pas de fuites mémoire
- [ ] Temps de chargement acceptable
- [ ] Taille de build raisonnable

### Compatibilité
- [ ] Fonctionne sur la résolution cible
- [ ] Compatible avec les plateformes choisies
- [ ] Pas d'erreurs dans les logs

## 🎉 Félicitations !

Space Clicker est maintenant assemblé et prêt ! 

### Prochaines étapes possibles :
1. **Balancing** : Ajuster les valeurs de gameplay
2. **Polish** : Améliorer les visuels et animations
3. **Features** : Ajouter de nouvelles fonctionnalités
4. **Distribution** : Publier sur les plateformes

**Bon jeu ! 🚀**
