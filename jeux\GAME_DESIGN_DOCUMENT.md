# Game Design Document - Space Clicker

**Titre** : Space Clicker - Idle Space Exploration  
**Genre** : Clicker/Idle, Casual Mobile  
**Plateformes** : Android, iOS  
**Public** : 13+ ans, Casual Gamers  

## 1. Vision du Jeu

### 1.1 Pitch Elevator
"Devenez le commandant d'une base spatiale et explorez la galaxie ! Collectez des ressources, améliorez vos installations et découvrez de nouvelles planètes dans ce jeu idle addictif où votre empire spatial grandit même quand vous n'êtes pas là."

### 1.2 Piliers de Design
1. **Progression Satisfaisante** : Récompenses fréquentes et croissance visible
2. **Simplicité Accessible** : Mécaniques faciles à comprendre
3. **Profondeur Stratégique** : Optimisations et choix tactiques
4. **Respect du Temps** : Progression significative en sessions courtes

## 2. Mécaniques de Jeu Détaillées

### 2.1 Système de Clics

#### 2.1.1 Feedback Tactile
- **Haptic Feedback** : Vibration légère à chaque clic
- **Effets Visuels** : Particules d'énergie, flash lumineux
- **Audio** : Sons spatiaux distinctifs par type de ressource
- **Animation** : Bounce des modules, compteurs qui s'incrémentent

#### 2.1.2 Multiplicateurs de Clic
```
Base Click Value = 1
With Upgrades = Base × (1 + Upgrade Bonus) × Prestige Multiplier × Temporary Boosts
```

### 2.2 Production Automatique

#### 2.2.1 Formule de Production
```
Production/sec = Base Rate × Module Level × Efficiency Upgrades × Global Multipliers
```

#### 2.2.2 Modules de Production

| Module | Production Base | Ressource | Débloquage |
|--------|-----------------|-----------|------------|
| **Panneau Solaire** | 0.5/sec | Énergie | Niveau 1 |
| **Extracteur Minier** | 0.1/sec | Minéraux | Niveau 5 |
| **Satellite Recherche** | 0.05/sec | Données | Niveau 10 |
| **Réacteur Fusion** | 2.0/sec | Énergie | Niveau 25 |

### 2.3 Système d'Upgrades

#### 2.3.1 Catégories d'Upgrades
1. **Production** : Augmente le rendement des modules
2. **Efficacité** : Réduit les coûts ou améliore les ratios
3. **Automatisation** : Débloque des fonctionnalités auto
4. **Spécial** : Effets uniques (multiplicateurs, nouveaux modules)

#### 2.3.2 Courbe de Coût
```
Cost(level) = Base Cost × (1.15 ^ level)
```

## 3. Progression et Contenu

### 3.1 Structure de Progression

#### Niveaux 1-10 : Apprentissage
- **Objectif** : Maîtriser les mécaniques de base
- **Contenu** : Tutoriel, premiers upgrades, station orbitale
- **Durée** : 15-30 minutes

#### Niveaux 11-25 : Expansion
- **Objectif** : Optimiser la production
- **Contenu** : Déblocage Lune, nouveaux modules
- **Durée** : 1-2 heures

#### Niveaux 26-50 : Exploration
- **Objectif** : Découvrir de nouveaux mondes
- **Contenu** : Mars, technologies avancées
- **Durée** : 3-5 heures

#### Niveaux 51+ : Maîtrise
- **Objectif** : Optimisation avancée et prestige
- **Contenu** : Planètes lointaines, prestige
- **Durée** : Progression infinie

### 3.2 Système de Prestige

#### 3.2.1 Conditions de Prestige
- Atteindre 1 Million de chaque ressource de base
- Débloquer au moins 3 planètes
- Compléter 50 upgrades

#### 3.2.2 Récompenses de Prestige
- **Prestige Points** : 1 point par million de ressources
- **Multiplicateur Global** : +2% production par point
- **Nouvelles Technologies** : Upgrades exclusives au prestige
- **Cosmétiques** : Skins de station, effets visuels

## 4. Économie et Balancing

### 4.1 Équilibre des Ressources

#### 4.1.1 Ratios de Production Cibles
- **Énergie** : Ressource principale (100%)
- **Minéraux** : Ressource d'upgrade (10% de l'énergie)
- **Données** : Ressource de recherche (1% de l'énergie)

#### 4.1.2 Temps de Déblocage Cibles
- **Nouveau Module** : 5-10 minutes
- **Nouvelle Planète** : 30-60 minutes
- **Prestige** : 2-4 heures

### 4.2 Monétisation Équilibrée

#### 4.2.1 Valeur des IAP
- **Starter Pack (2,99€)** : Équivaut à 2h de progression
- **Explorer Pack (9,99€)** : Équivaut à 6h de progression
- **Commander Pack (19,99€)** : Équivaut à 12h + bonus exclusifs

#### 4.2.2 Publicités Récompensées
- **Fréquence** : Maximum 1 toutes les 5 minutes
- **Valeur** : 10-15 minutes de production
- **Limite** : 6 par jour pour éviter l'abus

## 5. Interface et Expérience Utilisateur

### 5.1 Principes de Design UI

#### 5.1.1 Hiérarchie Visuelle
1. **Ressources** : Toujours visibles en haut
2. **Actions Principales** : Modules cliquables au centre
3. **Navigation** : Menu en bas, accès rapide
4. **Informations** : Tooltips et détails à la demande

#### 5.1.2 Thème Visuel Spatial
- **Couleurs** : Bleu foncé, cyan, blanc, accents dorés
- **Typographie** : Police futuriste mais lisible
- **Iconographie** : Symboles spatiaux universels
- **Animations** : Fluides, évoquant l'apesanteur

### 5.2 Accessibilité

#### 5.2.1 Fonctionnalités d'Accessibilité
- **Taille de Police** : Ajustable (petit, moyen, grand)
- **Contraste** : Mode haut contraste disponible
- **Daltonisme** : Couleurs et formes distinctives
- **Motor Impairment** : Zones de clic généreuses

#### 5.2.2 Localisation
- **Langues Prioritaires** : Français, Anglais, Espagnol
- **Langues Secondaires** : Allemand, Italien, Portugais
- **Adaptation Culturelle** : Symboles et références appropriés

## 6. Systèmes Sociaux et Communauté

### 6.1 Fonctionnalités Sociales (Phase 2)

#### 6.1.1 Classements
- **Leaderboards Globaux** : Production totale, prestige
- **Classements Amis** : Comparaison avec contacts
- **Classements Temporaires** : Événements spéciaux

#### 6.1.2 Guildes (Future Update)
- **Création/Rejoindre** : Guildes de 20-50 membres
- **Objectifs Communs** : Missions de guilde
- **Récompenses Partagées** : Bonus pour tous les membres
- **Chat Intégré** : Communication simple

### 6.2 Événements et Contenu Live

#### 6.2.1 Événements Récurrents
- **Weekend Boost** : x2 production le weekend
- **Research Rush** : Temps de recherche réduit
- **Exploration Event** : Nouvelles planètes temporaires
- **Community Goals** : Objectifs globaux de tous les joueurs

#### 6.2.2 Saisons (Trimestrielles)
- **Thème Saisonnier** : Cosmétiques et contenu thématique
- **Pass de Saison** : Progression avec récompenses
- **Défis Exclusifs** : Missions spéciales de saison
- **Récompenses Limitées** : Objets collectibles

## 7. Métriques et Analytics

### 7.1 KPIs de Gameplay

#### 7.1.1 Engagement
- **Session Length** : Durée moyenne des sessions
- **Sessions per Day** : Fréquence de connexion
- **Click Rate** : Clics par minute
- **Upgrade Frequency** : Achats d'améliorations

#### 7.1.2 Progression
- **Level Progression** : Vitesse d'avancement
- **Prestige Rate** : Fréquence des prestiges
- **Content Completion** : Pourcentage de contenu exploré
- **Retention Funnel** : Abandon par niveau

### 7.2 KPIs de Monétisation

#### 7.2.1 Revenus
- **ARPU** : Revenu par utilisateur
- **ARPPU** : Revenu par utilisateur payant
- **Conversion Rate** : Pourcentage d'acheteurs
- **LTV** : Valeur vie client

#### 7.2.2 Publicités
- **Ad Completion Rate** : Taux de visionnage complet
- **Ad Revenue per User** : Revenus pub par utilisateur
- **Ad Frequency** : Nombre de pubs vues par session
- **Ad Engagement** : Interaction avec les récompenses

## 8. Plan de Lancement et Post-Lancement

### 8.1 Stratégie de Lancement

#### 8.1.1 Soft Launch (2 semaines)
- **Marchés Tests** : Canada, Australie, Pays-Bas
- **Objectifs** : Validation métriques, debugging
- **Critères de Succès** : Retention D1 > 40%, D7 > 15%

#### 8.1.2 Global Launch
- **Marketing** : ASO, influenceurs gaming mobile
- **PR** : Communiqués, articles spécialisés
- **Community** : Discord, réseaux sociaux

### 8.2 Roadmap Post-Lancement

#### Mois 1-2 : Stabilisation
- Corrections bugs critiques
- Équilibrage basé sur données
- Premières optimisations

#### Mois 3-4 : Contenu
- Nouvelles planètes
- Événements saisonniers
- Fonctionnalités sociales

#### Mois 5-6 : Expansion
- Mode PvP
- Système de guildes
- Contenu premium
