/**
 * API2CSV - Convertisseur JSO<PERSON> vers CSV
 * Outil gratuit pour convertir les réponses JSON d'API en fichiers CSV
 */

class API2CSV {
    constructor() {
        this.csvData = null;
        this.jsonData = null;
        this.conversionCount = 0;
        this.initializeElements();
        this.bindEvents();
        this.initializeDarkMode();
        this.initializeViralFeatures();
    }

    /**
     * Initialise les références aux éléments DOM
     */
    initializeElements() {
        this.elements = {
            jsonInput: document.getElementById('jsonInput'),
            convertBtn: document.getElementById('convertBtn'),
            downloadBtn: document.getElementById('downloadBtn'),
            clearBtn: document.getElementById('clearJson'),
            darkModeToggle: document.getElementById('darkModeToggle'),
            errorMessage: document.getElementById('errorMessage'),
            errorText: document.getElementById('errorText'),
            statsContainer: document.getElementById('statsContainer'),
            statsText: document.getElementById('statsText'),
            emptyState: document.getElementById('emptyState'),
            previewTable: document.getElementById('previewTable'),
            tableHeader: document.getElementById('tableHeader'),
            tableBody: document.getElementById('tableBody'),
            flattenObjects: document.getElementById('flattenObjects'),
            includeArrayIndex: document.getElementById('includeArrayIndex'),
            shareBtn: document.getElementById('shareBtn'),
            copyUrlBtn: document.getElementById('copyUrlBtn'),
            usageCounter: document.getElementById('usageCounter')
        };
    }

    /**
     * Lie les événements aux éléments
     */
    bindEvents() {
        // Boutons principaux
        this.elements.convertBtn.addEventListener('click', () => this.convertToCSV());
        this.elements.downloadBtn.addEventListener('click', () => this.downloadCSV());
        this.elements.clearBtn.addEventListener('click', () => this.clearInput());
        this.elements.darkModeToggle.addEventListener('click', () => this.toggleDarkMode());

        // Boutons viraux
        this.elements.shareBtn.addEventListener('click', () => this.shareApp());
        this.elements.copyUrlBtn.addEventListener('click', () => this.copyUrl());

        // Raccourcis clavier
        this.elements.jsonInput.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.convertToCSV();
            }
        });

        // Auto-paste sur focus (optionnel)
        this.elements.jsonInput.addEventListener('focus', () => {
            if (!this.elements.jsonInput.value.trim()) {
                this.tryAutoPaste();
            }
        });

        // Conversion automatique lors de la saisie (avec debounce)
        let debounceTimer;
        this.elements.jsonInput.addEventListener('input', () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                if (this.elements.jsonInput.value.trim()) {
                    this.convertToCSV();
                }
            }, 1000);
        });
    }

    /**
     * Initialise le mode sombre
     */
    initializeDarkMode() {
        const isDark = localStorage.getItem('darkMode') === 'true' || 
                      (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches);
        
        if (isDark) {
            document.documentElement.classList.add('dark');
        }
    }

    /**
     * Bascule le mode sombre
     */
    toggleDarkMode() {
        const isDark = document.documentElement.classList.toggle('dark');
        localStorage.setItem('darkMode', isDark);
    }

    /**
     * Initialise les fonctionnalités virales
     */
    initializeViralFeatures() {
        // Compteur d'utilisation animé
        this.animateUsageCounter();

        // Tracking des conversions
        this.loadConversionStats();
    }

    /**
     * Anime le compteur d'utilisation
     */
    animateUsageCounter() {
        const baseCount = 2847;
        const randomAdd = Math.floor(Math.random() * 50);
        const totalCount = baseCount + randomAdd;

        let current = baseCount;
        const increment = Math.ceil(randomAdd / 20);

        const timer = setInterval(() => {
            current += increment;
            if (current >= totalCount) {
                current = totalCount;
                clearInterval(timer);
            }
            this.elements.usageCounter.textContent = `${current.toLocaleString()} developers used this tool this week`;
        }, 100);
    }

    /**
     * Charge les statistiques de conversion
     */
    loadConversionStats() {
        const stored = localStorage.getItem('api2csv_conversions');
        this.conversionCount = stored ? parseInt(stored) : 0;
    }

    /**
     * Sauvegarde les statistiques de conversion
     */
    saveConversionStats() {
        this.conversionCount++;
        localStorage.setItem('api2csv_conversions', this.conversionCount.toString());
    }

    /**
     * Partage l'application
     */
    async shareApp() {
        const shareData = {
            title: 'API2CSV - Convert JSON to CSV Online',
            text: 'Check out this free tool to convert JSON to CSV instantly! No signup required.',
            url: window.location.href
        };

        try {
            if (navigator.share) {
                await navigator.share(shareData);
            } else {
                // Fallback pour les navigateurs sans Web Share API
                this.copyUrl();
            }
        } catch (error) {
            console.log('Sharing failed:', error);
            this.copyUrl();
        }
    }

    /**
     * Copie l'URL dans le presse-papiers
     */
    async copyUrl() {
        try {
            await navigator.clipboard.writeText(window.location.href);

            // Animation de feedback
            const originalText = this.elements.copyUrlBtn.textContent;
            this.elements.copyUrlBtn.textContent = '✅ Copied!';
            this.elements.copyUrlBtn.classList.add('bg-green-600');

            setTimeout(() => {
                this.elements.copyUrlBtn.textContent = originalText;
                this.elements.copyUrlBtn.classList.remove('bg-green-600');
            }, 2000);

        } catch (error) {
            // Fallback pour les navigateurs plus anciens
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            this.elements.copyUrlBtn.textContent = '✅ Copied!';
            setTimeout(() => {
                this.elements.copyUrlBtn.textContent = '🔗 Copy URL';
            }, 2000);
        }
    }

    /**
     * Tente de coller automatiquement depuis le presse-papiers
     */
    async tryAutoPaste() {
        try {
            if (navigator.clipboard && navigator.clipboard.readText) {
                const text = await navigator.clipboard.readText();
                if (text && this.isValidJSON(text)) {
                    this.elements.jsonInput.value = text;
                    this.convertToCSV();
                }
            }
        } catch (error) {
            // Silencieux - l'utilisateur peut coller manuellement
        }
    }

    /**
     * Vérifie si une chaîne est un JSON valide
     */
    isValidJSON(str) {
        try {
            JSON.parse(str);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Efface l'entrée et remet à zéro l'interface
     */
    clearInput() {
        this.elements.jsonInput.value = '';
        this.resetUI();
    }

    /**
     * Remet l'interface à son état initial
     */
    resetUI() {
        this.hideError();
        this.hideStats();
        this.showEmptyState();
        this.elements.downloadBtn.disabled = true;
        this.csvData = null;
        this.jsonData = null;
    }

    /**
     * Convertit le JSON en CSV
     */
    convertToCSV() {
        const jsonText = this.elements.jsonInput.value.trim();

        if (!jsonText) {
            this.resetUI();
            return;
        }

        // Animation de chargement
        this.showLoadingAnimation();

        try {
            // Parse du JSON
            this.jsonData = JSON.parse(jsonText);
            
            // Conversion en format tabulaire
            const flatData = this.flattenData(this.jsonData);
            
            if (!flatData || flatData.length === 0) {
                throw new Error('Aucune donnée à convertir');
            }

            // Génération du CSV avec PapaParse
            this.csvData = Papa.unparse(flatData, {
                header: true,
                delimiter: ',',
                quotes: true,
                quoteChar: '"',
                escapeChar: '"'
            });

            // Mise à jour de l'interface
            this.hideLoadingAnimation();
            this.showPreview(flatData);
            this.showStats(flatData);
            this.hideError();
            this.elements.downloadBtn.disabled = false;

            // Tracking viral
            this.saveConversionStats();
            this.showSuccessAnimation();

        } catch (error) {
            this.hideLoadingAnimation();
            this.showError(error.message);
            this.resetUI();
        }
    }

    /**
     * Aplatit les données JSON en format tabulaire
     */
    flattenData(data) {
        if (!data) return [];

        // Si c'est un tableau, on traite chaque élément
        if (Array.isArray(data)) {
            return data.map((item, index) => {
                const flattened = this.elements.flattenObjects.checked ? 
                    this.flattenObject(item) : item;
                
                if (this.elements.includeArrayIndex.checked) {
                    return { _index: index, ...flattened };
                }
                return flattened;
            });
        }

        // Si c'est un objet, on le traite comme un seul élément
        if (typeof data === 'object') {
            const flattened = this.elements.flattenObjects.checked ? 
                this.flattenObject(data) : data;
            return [flattened];
        }

        // Si c'est une valeur primitive, on la wrappe
        return [{ value: data }];
    }

    /**
     * Aplatit un objet imbriqué
     */
    flattenObject(obj, prefix = '') {
        const flattened = {};

        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const newKey = prefix ? `${prefix}.${key}` : key;
                const value = obj[key];

                if (value === null || value === undefined) {
                    flattened[newKey] = '';
                } else if (Array.isArray(value)) {
                    // Pour les tableaux, on peut soit les joindre soit les aplatir
                    if (value.every(item => typeof item !== 'object')) {
                        // Tableau de primitives
                        flattened[newKey] = value.join('; ');
                    } else {
                        // Tableau d'objets - on prend le premier ou on aplatit tous
                        value.forEach((item, index) => {
                            if (typeof item === 'object') {
                                const subFlattened = this.flattenObject(item, `${newKey}[${index}]`);
                                Object.assign(flattened, subFlattened);
                            } else {
                                flattened[`${newKey}[${index}]`] = item;
                            }
                        });
                    }
                } else if (typeof value === 'object') {
                    // Objet imbriqué
                    const subFlattened = this.flattenObject(value, newKey);
                    Object.assign(flattened, subFlattened);
                } else {
                    // Valeur primitive
                    flattened[newKey] = value;
                }
            }
        }

        return flattened;
    }

    /**
     * Affiche l'aperçu du tableau
     */
    showPreview(data) {
        if (!data || data.length === 0) {
            this.showEmptyState();
            return;
        }

        // Masquer l'état vide
        this.elements.emptyState.classList.add('hidden');
        this.elements.previewTable.classList.remove('hidden');

        // Obtenir les colonnes
        const columns = Object.keys(data[0]);
        
        // Créer l'en-tête
        this.elements.tableHeader.innerHTML = `
            <tr>
                ${columns.map(col => `
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                        ${this.escapeHtml(col)}
                    </th>
                `).join('')}
            </tr>
        `;

        // Créer le corps (limité aux 10 premières lignes pour l'aperçu)
        const previewData = data.slice(0, 10);
        this.elements.tableBody.innerHTML = previewData.map(row => `
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                ${columns.map(col => `
                    <td class="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600">
                        ${this.escapeHtml(String(row[col] || ''))}
                    </td>
                `).join('')}
            </tr>
        `).join('');
    }

    /**
     * Affiche l'état vide
     */
    showEmptyState() {
        this.elements.emptyState.classList.remove('hidden');
        this.elements.previewTable.classList.add('hidden');
    }

    /**
     * Affiche les statistiques
     */
    showStats(data) {
        const rowCount = data.length;
        const columnCount = data.length > 0 ? Object.keys(data[0]).length : 0;
        
        this.elements.statsText.textContent = 
            `Conversion réussie : ${rowCount} ligne${rowCount > 1 ? 's' : ''}, ${columnCount} colonne${columnCount > 1 ? 's' : ''}`;
        this.elements.statsContainer.classList.remove('hidden');
    }

    /**
     * Masque les statistiques
     */
    hideStats() {
        this.elements.statsContainer.classList.add('hidden');
    }

    /**
     * Affiche une erreur
     */
    showError(message) {
        this.elements.errorText.textContent = message;
        this.elements.errorMessage.classList.remove('hidden');
    }

    /**
     * Masque l'erreur
     */
    hideError() {
        this.elements.errorMessage.classList.add('hidden');
    }

    /**
     * Affiche l'animation de chargement
     */
    showLoadingAnimation() {
        this.elements.convertBtn.innerHTML = '⏳ Converting...';
        this.elements.convertBtn.disabled = true;
    }

    /**
     * Masque l'animation de chargement
     */
    hideLoadingAnimation() {
        this.elements.convertBtn.innerHTML = '🔄 Convertir en CSV';
        this.elements.convertBtn.disabled = false;
    }

    /**
     * Affiche l'animation de succès
     */
    showSuccessAnimation() {
        // Animation du bouton de téléchargement
        this.elements.downloadBtn.classList.add('animate-pulse');
        setTimeout(() => {
            this.elements.downloadBtn.classList.remove('animate-pulse');
        }, 2000);

        // Scroll vers l'aperçu sur mobile
        if (window.innerWidth < 1024) {
            this.elements.previewContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    /**
     * Échappe le HTML pour éviter les injections
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Télécharge le fichier CSV
     */
    downloadCSV() {
        if (!this.csvData) {
            this.showError('Aucune donnée CSV à télécharger');
            return;
        }

        try {
            // Créer le blob
            const blob = new Blob([this.csvData], { type: 'text/csv;charset=utf-8;' });
            
            // Créer le lien de téléchargement
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', `api2csv_export_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            
            // Déclencher le téléchargement
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Nettoyer l'URL
            URL.revokeObjectURL(url);
            
        } catch (error) {
            this.showError('Erreur lors du téléchargement : ' + error.message);
        }
    }
}

// Initialisation de l'application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new API2CSV();
});

// Fonction globale pour charger les exemples
window.loadExample = function(type) {
    if (!app || !window.API2CSV_EXAMPLES[type]) return;

    app.elements.jsonInput.value = window.API2CSV_EXAMPLES[type];
    app.convertToCSV();

    // Scroll vers le textarea pour mobile
    app.elements.jsonInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// Exemples de JSON pour les tests
window.API2CSV_EXAMPLES = {
    simple: `{
  "name": "John Doe",
  "age": 30,
  "city": "Paris"
}`,
    
    array: `[
  {"name": "Alice", "age": 25, "city": "Lyon"},
  {"name": "Bob", "age": 35, "city": "Marseille"},
  {"name": "Charlie", "age": 28, "city": "Toulouse"}
]`,
    
    nested: `{
  "user": {
    "profile": {
      "name": "Jane Smith",
      "contact": {
        "email": "<EMAIL>",
        "phone": "+33123456789"
      }
    },
    "preferences": {
      "theme": "dark",
      "notifications": true
    }
  },
  "data": [1, 2, 3, 4, 5]
}`
};
