using UnityEngine;
using ChronoForge.Player;
using ChronoForge.Combat;
using ChronoForge.Procedural;
using ChronoForge.UI;
using ChronoForge.Audio;
using ChronoForge.Effects;
using ChronoForge.Progression;

namespace ChronoForge.Core
{
    /// <summary>
    /// Intégration finale et orchestration de tous les systèmes de ChronoForge
    /// </summary>
    public class GameIntegration : MonoBehaviour
    {
        [Head<PERSON>("Core Systems")]
        public GameManager gameManager;
        public RunManager runManager;
        public SaveSystem saveSystem;
        
        [Header("Player Systems")]
        public PlayerController playerController;
        public PlayerCombat playerCombat;
        public PlayerStats playerStats;
        public WeaponSystem weaponSystem;
        
        [Header("World Systems")]
        public LevelGenerator levelGenerator;
        public Room[] rooms;
        
        [Header("UI Systems")]
        public UIManager uiManager;
        public HUDController hudController;
        public MinimapController minimapController;
        public NotificationController notificationController;
        
        [Header("Audio & Effects")]
        public AudioManager audioManager;
        public EffectsManager effectsManager;
        
        [Header("Progression Systems")]
        public ProgressionManager progressionManager;
        public AchievementSystem achievementSystem;
        
        [Header("Integration Settings")]
        public bool autoInitialize = true;
        public bool validateSystems = true;
        public bool showDebugInfo = false;
        
        // Integration state
        private bool isInitialized = false;
        private bool allSystemsReady = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            if (autoInitialize)
            {
                InitializeGameSystems();
            }
        }
        
        private void Start()
        {
            if (validateSystems)
            {
                ValidateAllSystems();
            }
            
            SetupSystemIntegrations();
            
            if (showDebugInfo)
            {
                LogSystemStatus();
            }
        }
        
        #endregion
        
        #region System Initialization
        
        public void InitializeGameSystems()
        {
            if (isInitialized) return;
            
            UnityEngine.Debug.Log("🚀 Starting ChronoForge system initialization...");
            
            // Initialize core systems first
            InitializeCoreSystems();
            
            // Initialize player systems
            InitializePlayerSystems();
            
            // Initialize world systems
            InitializeWorldSystems();
            
            // Initialize UI systems
            InitializeUISystems();
            
            // Initialize audio and effects
            InitializeAudioEffectsSystems();
            
            // Initialize progression systems
            InitializeProgressionSystems();
            
            isInitialized = true;
            
            UnityEngine.Debug.Log("✅ ChronoForge system initialization complete!");
        }
        
        private void InitializeCoreSystems()
        {
            // Game Manager
            if (gameManager == null)
                gameManager = FindFirstObjectByType<GameManager>();
            
            if (gameManager == null)
            {
                GameObject gmObj = new GameObject("GameManager");
                gameManager = gmObj.AddComponent<GameManager>();
            }
            
            // Run Manager
            if (runManager == null)
                runManager = FindFirstObjectByType<RunManager>();
            
            if (runManager == null)
            {
                GameObject rmObj = new GameObject("RunManager");
                runManager = rmObj.AddComponent<RunManager>();
            }
            
            // Save System
            if (saveSystem == null)
                saveSystem = FindFirstObjectByType<SaveSystem>();
            
            if (saveSystem == null)
            {
                GameObject ssObj = new GameObject("SaveSystem");
                saveSystem = ssObj.AddComponent<SaveSystem>();
            }
            
            UnityEngine.Debug.Log("🎮 Core systems initialized");
        }
        
        private void InitializePlayerSystems()
        {
            // Find player controller
            if (playerController == null)
                playerController = FindFirstObjectByType<PlayerController>();
            
            if (playerController != null)
            {
                // Get player components
                if (playerCombat == null)
                    playerCombat = playerController.GetComponent<PlayerCombat>();
                
                if (playerStats == null)
                    playerStats = playerController.GetComponent<PlayerStats>();
                
                if (weaponSystem == null)
                    weaponSystem = playerController.GetComponent<WeaponSystem>();
            }
            
            UnityEngine.Debug.Log("👤 Player systems initialized");
        }
        
        private void InitializeWorldSystems()
        {
            // Level Generator
            if (levelGenerator == null)
                levelGenerator = FindFirstObjectByType<LevelGenerator>();
            
            if (levelGenerator == null)
            {
                GameObject lgObj = new GameObject("LevelGenerator");
                levelGenerator = lgObj.AddComponent<LevelGenerator>();
            }
            
            // Find all rooms
            if (rooms == null || rooms.Length == 0)
            {
                rooms = FindObjectsByType<Room>(FindObjectsSortMode.None);
            }
            
            UnityEngine.Debug.Log("🌍 World systems initialized");
        }
        
        private void InitializeUISystems()
        {
            // UI Manager
            if (uiManager == null)
                uiManager = FindFirstObjectByType<UIManager>();
            
            if (uiManager != null)
            {
                // Get UI components
                if (hudController == null)
                    hudController = uiManager.GetHUDController();
                
                if (minimapController == null)
                    minimapController = uiManager.GetMinimapController();
                
                if (notificationController == null)
                    notificationController = uiManager.GetNotificationController();
            }
            
            UnityEngine.Debug.Log("🎨 UI systems initialized");
        }
        
        private void InitializeAudioEffectsSystems()
        {
            // Audio Manager
            if (audioManager == null)
                audioManager = AudioManager.Instance;
            
            // Effects Manager
            if (effectsManager == null)
                effectsManager = EffectsManager.Instance;
            
            UnityEngine.Debug.Log("🔊 Audio & Effects systems initialized");
        }
        
        private void InitializeProgressionSystems()
        {
            // Progression Manager
            if (progressionManager == null)
                progressionManager = ProgressionManager.Instance;
            
            // Achievement System
            if (achievementSystem == null)
                achievementSystem = AchievementSystem.Instance;
            
            UnityEngine.Debug.Log("📈 Progression systems initialized");
        }
        
        #endregion
        
        #region System Integration
        
        private void SetupSystemIntegrations()
        {
            UnityEngine.Debug.Log("🔗 Setting up system integrations...");
            
            // Player-Combat Integration
            SetupPlayerCombatIntegration();
            
            // UI-Player Integration
            SetupUIPlayerIntegration();
            
            // Audio-Effects Integration
            SetupAudioEffectsIntegration();
            
            // Progression Integration
            SetupProgressionIntegration();
            
            // World-Player Integration
            SetupWorldPlayerIntegration();
            
            allSystemsReady = true;
            
            UnityEngine.Debug.Log("✅ System integrations complete!");
        }
        
        private void SetupPlayerCombatIntegration()
        {
            if (playerCombat == null || effectsManager == null) return;
            
            // Subscribe to combat events for effects
            PlayerCombat.OnAttackPerformed += (damage, isCritical, position) =>
            {
                if (isCritical)
                {
                    effectsManager.PlayCriticalHitEffect(position, Vector3.forward);
                }
                else
                {
                    effectsManager.PlayHitEffect(position);
                }
            };
            
            PlayerCombat.OnSpecialUsed += (position) =>
            {
                effectsManager.PlayEffect("special_ability", position);
            };
            
            PlayerCombat.OnUltimateUsed += (position) =>
            {
                effectsManager.PlayExplosionEffect(position, 2f);
                effectsManager.ScreenShake(1.5f, 0.5f);
            };
        }
        
        private void SetupUIPlayerIntegration()
        {
            if (hudController == null || playerController == null) return;
            
            // Subscribe to player events for UI updates
            PlayerController.OnPlayerDeath += () =>
            {
                if (uiManager != null)
                    uiManager.ShowGameOver();
            };
            
            PlayerStats.OnRunLevelUp += (newLevel) =>
            {
                if (hudController != null)
                    hudController.ShowLevelUpEffect();
                
                if (effectsManager != null && playerController != null)
                    effectsManager.PlayLevelUpEffect(playerController.transform.position);
            };
        }
        
        private void SetupAudioEffectsIntegration()
        {
            if (audioManager == null || effectsManager == null) return;
            
            // Integrate audio with effects
            EffectsManager.OnEffectPlayed += (effectName, position) =>
            {
                string soundName = GetSoundForEffect(effectName);
                if (!string.IsNullOrEmpty(soundName))
                {
                    audioManager.PlaySFX(soundName, 1f, 1f, position);
                }
            };
        }
        
        private void SetupProgressionIntegration()
        {
            if (progressionManager == null || achievementSystem == null) return;
            
            // Subscribe to game events for progression tracking
            GameManager.OnEnemyKilled += () =>
            {
                progressionManager.RecordEnemyKill("generic");
                achievementSystem.IncrementAchievementProgress("enemy_slayer");
            };
            
            GameManager.OnRunCompleted += () =>
            {
                if (runManager != null)
                {
                    progressionManager.RecordRunCompletion(
                        true,
                        runManager.currentFloor,
                        runManager.enemiesKilled,
                        runManager.GetRunTime()
                    );
                }
                
                achievementSystem.IncrementAchievementProgress("run_master");
            };
            
            GameManager.OnItemCollected += (itemId) =>
            {
                progressionManager.RecordItemFound(itemId);
                achievementSystem.IncrementAchievementProgress("collector");
            };
        }
        
        private void SetupWorldPlayerIntegration()
        {
            if (levelGenerator == null || playerController == null) return;
            
            // Subscribe to room events
            LevelGenerator.OnRoomEntered += (roomData) =>
            {
                if (audioManager != null)
                {
                    // Play room-specific ambient music
                    string musicTrack = GetMusicForRoomType(roomData.roomType);
                    if (!string.IsNullOrEmpty(musicTrack))
                    {
                        audioManager.PlayMusic(musicTrack);
                    }
                }
            };
            
            LevelGenerator.OnRoomCleared += (roomData) =>
            {
                if (progressionManager != null)
                {
                    progressionManager.AddExperience(GetExperienceForRoomType(roomData.roomType));
                }
                
                if (effectsManager != null && playerController != null)
                {
                    effectsManager.PlayEffect("room_clear", playerController.transform.position);
                }
            };
        }
        
        #endregion
        
        #region System Validation
        
        private void ValidateAllSystems()
        {
            UnityEngine.Debug.Log("🔍 Validating all systems...");
            
            bool allValid = true;
            
            // Validate core systems
            allValid &= ValidateSystem("GameManager", gameManager);
            allValid &= ValidateSystem("RunManager", runManager);
            allValid &= ValidateSystem("SaveSystem", saveSystem);
            
            // Validate player systems
            allValid &= ValidateSystem("PlayerController", playerController);
            allValid &= ValidateSystem("PlayerCombat", playerCombat);
            allValid &= ValidateSystem("PlayerStats", playerStats);
            
            // Validate world systems
            allValid &= ValidateSystem("LevelGenerator", levelGenerator);
            
            // Validate UI systems
            allValid &= ValidateSystem("UIManager", uiManager);
            
            // Validate progression systems
            allValid &= ValidateSystem("ProgressionManager", progressionManager);
            allValid &= ValidateSystem("AchievementSystem", achievementSystem);
            
            if (allValid)
            {
                UnityEngine.Debug.Log("✅ All systems validated successfully!");
            }
            else
            {
                UnityEngine.Debug.LogWarning("⚠️ Some systems failed validation. Check logs for details.");
            }
        }
        
        private bool ValidateSystem(string systemName, Object system)
        {
            if (system == null)
            {
                UnityEngine.Debug.LogError($"❌ {systemName} is missing!");
                return false;
            }
            
            UnityEngine.Debug.Log($"✅ {systemName} validated");
            return true;
        }
        
        #endregion
        
        #region Utility Methods
        
        private string GetSoundForEffect(string effectName)
        {
            switch (effectName)
            {
                case "hit": return "impact_hit";
                case "explosion": return "explosion_large";
                case "heal": return "heal_sound";
                case "levelup": return "level_up";
                case "dash": return "dash_whoosh";
                case "critical": return "critical_hit";
                default: return "";
            }
        }
        
        private string GetMusicForRoomType(RoomType roomType)
        {
            switch (roomType)
            {
                case RoomType.Combat: return "combat_music";
                case RoomType.Boss: return "boss_music";
                case RoomType.Rest: return "peaceful_music";
                case RoomType.Shop: return "shop_music";
                default: return "ambient_music";
            }
        }
        
        private int GetExperienceForRoomType(RoomType roomType)
        {
            switch (roomType)
            {
                case RoomType.Combat: return 25;
                case RoomType.Boss: return 100;
                case RoomType.Treasure: return 15;
                case RoomType.Secret: return 50;
                default: return 10;
            }
        }
        
        private void LogSystemStatus()
        {
            UnityEngine.Debug.Log("📊 ChronoForge System Status:");
            UnityEngine.Debug.Log($"   🎮 Core Systems: {(gameManager != null && runManager != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   👤 Player Systems: {(playerController != null && playerCombat != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   🌍 World Systems: {(levelGenerator != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   🎨 UI Systems: {(uiManager != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   🔊 Audio/Effects: {(audioManager != null && effectsManager != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   📈 Progression: {(progressionManager != null && achievementSystem != null ? "✅" : "❌")}");
            UnityEngine.Debug.Log($"   🔗 Integration: {(allSystemsReady ? "✅" : "❌")}");
        }
        
        #endregion
        
        #region Public Methods
        
        public bool AreAllSystemsReady()
        {
            return isInitialized && allSystemsReady;
        }
        
        public void ForceSystemValidation()
        {
            ValidateAllSystems();
        }
        
        public void RestartGame()
        {
            if (gameManager != null)
            {
                gameManager.RestartGame();
            }
        }
        
        public void StartNewRun()
        {
            if (runManager != null && AreAllSystemsReady())
            {
                runManager.StartNewRun();
            }
        }
        
        public void SaveGameState()
        {
            if (saveSystem != null)
            {
                saveSystem.SaveGame();
            }
        }
        
        public void LoadGameState()
        {
            if (saveSystem != null)
            {
                saveSystem.LoadGame();
            }
        }
        
        #endregion
    }
}
