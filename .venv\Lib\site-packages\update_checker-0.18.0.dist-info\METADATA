Metadata-Version: 2.1
Name: update-checker
Version: 0.18.0
Summary: A python module that will check for package updates.
Home-page: https://github.com/bboe/update_checker
Author: <PERSON>
Author-email: bb<PERSON><PERSON><PERSON><PERSON>@gmail.com
License: Simplified BSD License
Platform: UNKNOWN
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Description-Content-Type: text/markdown
Requires-Dist: requests (>=2.3.0)
Provides-Extra: dev
Requires-Dist: black ; extra == 'dev'
Requires-Dist: flake8 ; extra == 'dev'
Requires-Dist: pytest (>=2.7.3) ; extra == 'dev'
Provides-Extra: lint
Requires-Dist: black ; extra == 'lint'
Requires-Dist: flake8 ; extra == 'lint'
Provides-Extra: test
Requires-Dist: pytest (>=2.7.3) ; extra == 'test'

[![Build Status](https://travis-ci.org/bboe/update_checker.png)](https://travis-ci.org/bboe/update_checker)

# update_checker

A python module that will check for package updates.

Only whitelisted packages can be checked for updates. Contact update_checker's
author for information on adding a package to the whitelist.

### Installation

The update_checker module can be installed via:

    pip install update_checker

### Usage

To simply output when there is a newer version of the `praw` package, you can
use the following bit of code:

```python
from update_checker import update_check
update_check('praw', '0.0.1')
```

If you need more control, such as performing operations conditionally when
there is an update you can use the following approach:

```python
from update_checker import UpdateChecker
checker = UpdateChecker()
result = checker.check('praw', '0.0.1')
if result:  # result is None when an update was not found or a failure occured
   # result is a UpdateResult object that contains the following attributes:
   # * available_version
   # * package_name
   # * running_version
   # * release_date (is None if the information isn't available)
   print(result)
   # Conditionally perform other actions
```


