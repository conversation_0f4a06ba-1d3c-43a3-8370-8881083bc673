using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System;

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Test runner simple pour exécuter les tests en mode Play
    /// </summary>
    public class TestRunner : MonoBehaviour
    {
        [Header("Test Configuration")]
        public bool runTestsOnStart = true;
        public bool showDetailedResults = true;
        public bool stopOnFirstFailure = false;
        
        [Header("UI References")]
        public TMPro.TextMeshProUGUI resultsText;
        public UnityEngine.UI.Button runTestsButton;
        public UnityEngine.UI.Slider progressSlider;
        
        // Test results
        private List<TestResult> testResults = new List<TestResult>();
        private int totalTests = 0;
        private int passedTests = 0;
        private int failedTests = 0;
        
        private struct TestResult
        {
            public string testName;
            public bool passed;
            public string message;
            public float executionTime;
        }
        
        #region Unity Lifecycle
        
        private void Start()
        {
            if (runTestsButton != null)
            {
                runTestsButton.onClick.AddListener(RunAllTests);
            }
            
            if (runTestsOnStart)
            {
                StartCoroutine(RunTestsCoroutine());
            }
        }
        
        #endregion
        
        #region Test Execution
        
        public void RunAllTests()
        {
            StartCoroutine(RunTestsCoroutine());
        }
        
        private IEnumerator RunTestsCoroutine()
        {
            Debug.Log("🧪 Starting Space Clicker Tests...");
            UpdateUI("Running tests...", 0f);
            
            // Reset results
            testResults.Clear();
            totalTests = 0;
            passedTests = 0;
            failedTests = 0;
            
            // Get all test classes
            Type[] testClasses = {
                typeof(ResourceManagerTests),
                typeof(UpgradeManagerTests),
                typeof(BigIntegerExtensionsTests),
                typeof(GameIntegrationTests)
            };
            
            float totalProgress = 0f;
            float progressPerClass = 1f / testClasses.Length;
            
            foreach (Type testClass in testClasses)
            {
                yield return StartCoroutine(RunTestClass(testClass));
                totalProgress += progressPerClass;
                UpdateProgress(totalProgress);
                yield return new WaitForSeconds(0.1f); // Small delay between classes
            }
            
            // Display final results
            DisplayFinalResults();
            
            Debug.Log($"🧪 Tests completed: {passedTests}/{totalTests} passed");
        }
        
        private IEnumerator RunTestClass(Type testClass)
        {
            Debug.Log($"Running tests for {testClass.Name}...");
            
            // Get all test methods
            MethodInfo[] methods = testClass.GetMethods(BindingFlags.Public | BindingFlags.Instance);
            List<MethodInfo> testMethods = new List<MethodInfo>();
            
            foreach (MethodInfo method in methods)
            {
                if (method.GetCustomAttribute<NUnit.Framework.TestAttribute>() != null ||
                    method.GetCustomAttribute<UnityEngine.TestTools.UnityTestAttribute>() != null)
                {
                    testMethods.Add(method);
                }
            }
            
            if (testMethods.Count == 0)
            {
                Debug.LogWarning($"No test methods found in {testClass.Name}");
                yield break;
            }
            
            // Create instance of test class
            object testInstance = null;
            try
            {
                testInstance = Activator.CreateInstance(testClass);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to create instance of {testClass.Name}: {e.Message}");
                yield break;
            }
            
            // Run Setup if it exists
            MethodInfo setupMethod = testClass.GetMethod("Setup", BindingFlags.Public | BindingFlags.Instance);
            if (setupMethod != null)
            {
                try
                {
                    setupMethod.Invoke(testInstance, null);
                }
                catch (Exception e)
                {
                    Debug.LogError($"Setup failed for {testClass.Name}: {e.Message}");
                }
            }
            
            // Run each test method
            foreach (MethodInfo testMethod in testMethods)
            {
                yield return StartCoroutine(RunSingleTest(testInstance, testMethod, testClass.Name));
                
                if (stopOnFirstFailure && failedTests > 0)
                {
                    Debug.LogWarning("Stopping tests due to failure (stopOnFirstFailure = true)");
                    break;
                }
            }
            
            // Run TearDown if it exists
            MethodInfo tearDownMethod = testClass.GetMethod("TearDown", BindingFlags.Public | BindingFlags.Instance);
            if (tearDownMethod != null)
            {
                try
                {
                    tearDownMethod.Invoke(testInstance, null);
                }
                catch (Exception e)
                {
                    Debug.LogError($"TearDown failed for {testClass.Name}: {e.Message}");
                }
            }
        }
        
        private IEnumerator RunSingleTest(object testInstance, MethodInfo testMethod, string className)
        {
            string testName = $"{className}.{testMethod.Name}";
            float startTime = Time.realtimeSinceStartup;
            
            TestResult result = new TestResult
            {
                testName = testName,
                passed = false,
                message = "",
                executionTime = 0f
            };
            
            try
            {
                // Check if it's a UnityTest (coroutine)
                if (testMethod.GetCustomAttribute<UnityEngine.TestTools.UnityTestAttribute>() != null)
                {
                    // Run as coroutine
                    IEnumerator coroutine = (IEnumerator)testMethod.Invoke(testInstance, null);
                    yield return StartCoroutine(coroutine);
                }
                else
                {
                    // Run as regular method
                    testMethod.Invoke(testInstance, null);
                }
                
                result.passed = true;
                result.message = "PASSED";
                passedTests++;
                
                Debug.Log($"✅ {testName} - PASSED");
            }
            catch (Exception e)
            {
                result.passed = false;
                result.message = GetExceptionMessage(e);
                failedTests++;
                
                Debug.LogError($"❌ {testName} - FAILED: {result.message}");
            }
            
            result.executionTime = Time.realtimeSinceStartup - startTime;
            testResults.Add(result);
            totalTests++;
            
            // Update UI with current progress
            UpdateUI($"Running {testName}...", (float)totalTests / GetEstimatedTotalTests());
            
            yield return null; // Allow UI to update
        }
        
        #endregion
        
        #region UI Updates
        
        private void UpdateUI(string message, float progress)
        {
            if (resultsText != null)
            {
                resultsText.text = message;
            }
            
            UpdateProgress(progress);
        }
        
        private void UpdateProgress(float progress)
        {
            if (progressSlider != null)
            {
                progressSlider.value = Mathf.Clamp01(progress);
            }
        }
        
        private void DisplayFinalResults()
        {
            string results = $"🧪 TEST RESULTS 🧪\n\n";
            results += $"Total Tests: {totalTests}\n";
            results += $"✅ Passed: {passedTests}\n";
            results += $"❌ Failed: {failedTests}\n";
            results += $"Success Rate: {(totalTests > 0 ? (passedTests * 100 / totalTests) : 0)}%\n\n";
            
            if (showDetailedResults)
            {
                results += "DETAILED RESULTS:\n";
                results += "==================\n";
                
                foreach (TestResult result in testResults)
                {
                    string status = result.passed ? "✅" : "❌";
                    results += $"{status} {result.testName}\n";
                    
                    if (!result.passed)
                    {
                        results += $"   Error: {result.message}\n";
                    }
                    
                    results += $"   Time: {result.executionTime:F3}s\n\n";
                }
            }
            
            if (resultsText != null)
            {
                resultsText.text = results;
            }
            
            // Log summary to console
            if (failedTests == 0)
            {
                Debug.Log($"🎉 All tests passed! ({passedTests}/{totalTests})");
            }
            else
            {
                Debug.LogWarning($"⚠️ {failedTests} test(s) failed out of {totalTests}");
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        private string GetExceptionMessage(Exception e)
        {
            if (e.InnerException != null)
            {
                return e.InnerException.Message;
            }
            return e.Message;
        }
        
        private int GetEstimatedTotalTests()
        {
            // Rough estimate for progress bar
            return 50; // Adjust based on actual number of tests
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Get the current test results
        /// </summary>
        public string GetTestSummary()
        {
            return $"Tests: {passedTests}/{totalTests} passed ({failedTests} failed)";
        }
        
        /// <summary>
        /// Check if all tests passed
        /// </summary>
        public bool AllTestsPassed()
        {
            return totalTests > 0 && failedTests == 0;
        }
        
        /// <summary>
        /// Get detailed test results
        /// </summary>
        public List<(string name, bool passed, string message, float time)> GetDetailedResults()
        {
            List<(string, bool, string, float)> results = new List<(string, bool, string, float)>();
            
            foreach (TestResult result in testResults)
            {
                results.Add((result.testName, result.passed, result.message, result.executionTime));
            }
            
            return results;
        }
        
        #endregion
    }
}
