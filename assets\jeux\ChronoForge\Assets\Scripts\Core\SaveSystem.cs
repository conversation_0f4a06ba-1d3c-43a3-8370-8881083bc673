using UnityEngine;
using System.IO;
using System.Collections.Generic;

namespace ChronoForge.Core
{
    /// <summary>
    /// Système de sauvegarde pour ChronoForge
    /// </summary>
    public class SaveSystem : MonoBehaviour
    {
        [Header("Save Settings")]
        public bool autoSave = true;
        public float autoSaveInterval = 60f; // seconds
        public int maxSaveSlots = 3;
        public bool encryptSaves = false;
        
        [Header("Save Paths")]
        public string saveFileName = "chronoforge_save";
        public string saveExtension = ".json";
        
        // Events
        public static System.Action OnGameSaved;
        public static System.Action OnGameLoaded;
        public static System.Action<string> OnSaveError;
        
        // Private fields
        private string savePath;
        private float lastAutoSave;
        private ProgressionManager progressionManager;
        private RunManager runManager;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeSaveSystem();
        }
        
        private void Start()
        {
            FindManagers();
            lastAutoSave = Time.time;
        }
        
        private void Update()
        {
            HandleAutoSave();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeSaveSystem()
        {
            // Set save path
            savePath = Path.Combine(Application.persistentDataPath, "Saves");
            
            // Create save directory if it doesn't exist
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
                Debug.Log($"📁 Created save directory: {savePath}");
            }
            
            Debug.Log("💾 SaveSystem initialized");
        }
        
        private void FindManagers()
        {
            if (progressionManager == null)
                progressionManager = FindObjectOfType<ProgressionManager>();
            
            if (runManager == null)
                runManager = FindObjectOfType<RunManager>();
        }
        
        #endregion
        
        #region Auto Save
        
        private void HandleAutoSave()
        {
            if (!autoSave) return;
            
            if (Time.time - lastAutoSave >= autoSaveInterval)
            {
                SaveGame();
                lastAutoSave = Time.time;
            }
        }
        
        public void SetAutoSave(bool enabled)
        {
            autoSave = enabled;
            Debug.Log($"🔄 Auto-save {(enabled ? "enabled" : "disabled")}");
        }
        
        #endregion
        
        #region Save Game
        
        public bool SaveGame(int slot = 0)
        {
            try
            {
                SaveData saveData = CreateSaveData();
                string json = JsonUtility.ToJson(saveData, true);
                
                if (encryptSaves)
                {
                    json = EncryptString(json);
                }
                
                string fileName = GetSaveFileName(slot);
                string fullPath = Path.Combine(savePath, fileName);
                
                File.WriteAllText(fullPath, json);
                
                Debug.Log($"💾 Game saved to slot {slot}: {fullPath}");
                OnGameSaved?.Invoke();
                
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to save game: {e.Message}");
                OnSaveError?.Invoke($"Save failed: {e.Message}");
                return false;
            }
        }
        
        private SaveData CreateSaveData()
        {
            SaveData saveData = new SaveData();
            
            // Game info
            saveData.gameVersion = Application.version;
            saveData.saveTime = System.DateTime.Now.ToBinary();
            saveData.totalPlayTime = progressionManager != null ? progressionManager.totalPlayTime : 0f;
            
            // Progression data
            if (progressionManager != null)
            {
                saveData.playerLevel = progressionManager.playerLevel;
                saveData.currentExperience = progressionManager.currentExperience;
                saveData.arcaneShards = progressionManager.arcaneShards;
                saveData.memoryFragments = progressionManager.memoryFragments;
                saveData.nanoSouls = progressionManager.nanoSouls;
                
                saveData.unlockedClasses = new List<string>(progressionManager.unlockedClasses);
                saveData.unlockedWeapons = new List<string>(progressionManager.unlockedWeapons);
                saveData.unlockedArtifacts = new List<string>(progressionManager.unlockedArtifacts);
                saveData.unlockedBiomes = new List<string>(progressionManager.unlockedBiomes);
                saveData.unlockedAchievements = new List<string>(progressionManager.unlockedAchievements);
                
                saveData.totalRuns = progressionManager.totalRuns;
                saveData.successfulRuns = progressionManager.successfulRuns;
                saveData.totalDeaths = progressionManager.totalDeaths;
                saveData.totalEnemiesKilled = progressionManager.totalEnemiesKilled;
                saveData.totalBossesDefeated = progressionManager.totalBossesDefeated;
            }
            
            // Current run data (if in run)
            if (runManager != null && runManager.IsRunActive())
            {
                saveData.hasActiveRun = true;
                saveData.currentRunData = runManager.GetCurrentRunData();
            }
            
            // Settings
            saveData.settings = CreateSettingsData();
            
            return saveData;
        }
        
        private SettingsData CreateSettingsData()
        {
            return new SettingsData
            {
                masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f),
                musicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f),
                sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 0.8f),
                fullscreen = Screen.fullScreen,
                resolution = $"{Screen.width}x{Screen.height}",
                quality = QualitySettings.GetQualityLevel(),
                autoSave = autoSave
            };
        }
        
        #endregion
        
        #region Load Game
        
        public bool LoadGame(int slot = 0)
        {
            try
            {
                string fileName = GetSaveFileName(slot);
                string fullPath = Path.Combine(savePath, fileName);
                
                if (!File.Exists(fullPath))
                {
                    Debug.LogWarning($"⚠️ Save file not found: {fullPath}");
                    return false;
                }
                
                string json = File.ReadAllText(fullPath);
                
                if (encryptSaves)
                {
                    json = DecryptString(json);
                }
                
                SaveData saveData = JsonUtility.FromJson<SaveData>(json);
                
                if (saveData == null)
                {
                    Debug.LogError("❌ Failed to parse save data");
                    return false;
                }
                
                ApplySaveData(saveData);
                
                Debug.Log($"📂 Game loaded from slot {slot}");
                OnGameLoaded?.Invoke();
                
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to load game: {e.Message}");
                OnSaveError?.Invoke($"Load failed: {e.Message}");
                return false;
            }
        }
        
        private void ApplySaveData(SaveData saveData)
        {
            // Apply progression data
            if (progressionManager != null)
            {
                progressionManager.playerLevel = saveData.playerLevel;
                progressionManager.currentExperience = saveData.currentExperience;
                progressionManager.arcaneShards = saveData.arcaneShards;
                progressionManager.memoryFragments = saveData.memoryFragments;
                progressionManager.nanoSouls = saveData.nanoSouls;
                
                progressionManager.unlockedClasses = saveData.unlockedClasses ?? new List<string>();
                progressionManager.unlockedWeapons = saveData.unlockedWeapons ?? new List<string>();
                progressionManager.unlockedArtifacts = saveData.unlockedArtifacts ?? new List<string>();
                progressionManager.unlockedBiomes = saveData.unlockedBiomes ?? new List<string>();
                progressionManager.unlockedAchievements = saveData.unlockedAchievements ?? new List<string>();
                
                progressionManager.totalRuns = saveData.totalRuns;
                progressionManager.successfulRuns = saveData.successfulRuns;
                progressionManager.totalDeaths = saveData.totalDeaths;
                progressionManager.totalPlayTime = saveData.totalPlayTime;
                progressionManager.totalEnemiesKilled = saveData.totalEnemiesKilled;
                progressionManager.totalBossesDefeated = saveData.totalBossesDefeated;
            }
            
            // Apply settings
            if (saveData.settings != null)
            {
                ApplySettingsData(saveData.settings);
            }
            
            // Handle active run
            if (saveData.hasActiveRun && saveData.currentRunData != null)
            {
                // Note: In a real implementation, you might want to ask the player
                // if they want to continue their run or start fresh
                Debug.Log("🎮 Active run found in save data");
            }
        }
        
        private void ApplySettingsData(SettingsData settings)
        {
            PlayerPrefs.SetFloat("MasterVolume", settings.masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", settings.musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", settings.sfxVolume);
            
            // Apply graphics settings
            if (settings.fullscreen != Screen.fullScreen)
            {
                Screen.fullScreen = settings.fullscreen;
            }
            
            QualitySettings.SetQualityLevel(settings.quality);
            
            autoSave = settings.autoSave;
        }
        
        #endregion
        
        #region Save Management
        
        public bool SaveExists(int slot = 0)
        {
            string fileName = GetSaveFileName(slot);
            string fullPath = Path.Combine(savePath, fileName);
            return File.Exists(fullPath);
        }
        
        public SaveInfo GetSaveInfo(int slot = 0)
        {
            if (!SaveExists(slot))
                return null;
            
            try
            {
                string fileName = GetSaveFileName(slot);
                string fullPath = Path.Combine(savePath, fileName);
                
                FileInfo fileInfo = new FileInfo(fullPath);
                string json = File.ReadAllText(fullPath);
                
                if (encryptSaves)
                {
                    json = DecryptString(json);
                }
                
                SaveData saveData = JsonUtility.FromJson<SaveData>(json);
                
                return new SaveInfo
                {
                    slot = slot,
                    saveTime = System.DateTime.FromBinary(saveData.saveTime),
                    playerLevel = saveData.playerLevel,
                    totalPlayTime = saveData.totalPlayTime,
                    totalRuns = saveData.totalRuns,
                    gameVersion = saveData.gameVersion,
                    fileSize = fileInfo.Length
                };
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to get save info for slot {slot}: {e.Message}");
                return null;
            }
        }
        
        public bool DeleteSave(int slot = 0)
        {
            try
            {
                string fileName = GetSaveFileName(slot);
                string fullPath = Path.Combine(savePath, fileName);
                
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    Debug.Log($"🗑️ Deleted save slot {slot}");
                    return true;
                }
                
                return false;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to delete save slot {slot}: {e.Message}");
                return false;
            }
        }
        
        public List<SaveInfo> GetAllSaves()
        {
            List<SaveInfo> saves = new List<SaveInfo>();
            
            for (int i = 0; i < maxSaveSlots; i++)
            {
                SaveInfo saveInfo = GetSaveInfo(i);
                if (saveInfo != null)
                {
                    saves.Add(saveInfo);
                }
            }
            
            return saves;
        }
        
        #endregion
        
        #region Utility Methods
        
        private string GetSaveFileName(int slot)
        {
            return $"{saveFileName}_{slot:D2}{saveExtension}";
        }
        
        private string EncryptString(string text)
        {
            // Simple XOR encryption (not secure, just obfuscation)
            byte[] data = System.Text.Encoding.UTF8.GetBytes(text);
            byte key = 0x42; // Simple key
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = (byte)(data[i] ^ key);
            }
            
            return System.Convert.ToBase64String(data);
        }
        
        private string DecryptString(string encryptedText)
        {
            try
            {
                byte[] data = System.Convert.FromBase64String(encryptedText);
                byte key = 0x42; // Same key as encryption
                
                for (int i = 0; i < data.Length; i++)
                {
                    data[i] = (byte)(data[i] ^ key);
                }
                
                return System.Text.Encoding.UTF8.GetString(data);
            }
            catch
            {
                return encryptedText; // Return as-is if decryption fails
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void QuickSave()
        {
            SaveGame(0); // Save to slot 0
        }
        
        public void QuickLoad()
        {
            LoadGame(0); // Load from slot 0
        }
        
        public string GetSavePath()
        {
            return savePath;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Structure des données de sauvegarde
    /// </summary>
    [System.Serializable]
    public class SaveData
    {
        public string gameVersion;
        public long saveTime;
        public float totalPlayTime;
        
        // Progression
        public int playerLevel;
        public int currentExperience;
        public int arcaneShards;
        public int memoryFragments;
        public int nanoSouls;
        
        // Unlocks
        public List<string> unlockedClasses;
        public List<string> unlockedWeapons;
        public List<string> unlockedArtifacts;
        public List<string> unlockedBiomes;
        public List<string> unlockedAchievements;
        
        // Statistics
        public int totalRuns;
        public int successfulRuns;
        public int totalDeaths;
        public int totalEnemiesKilled;
        public int totalBossesDefeated;
        
        // Current run
        public bool hasActiveRun;
        public RunData currentRunData;
        
        // Settings
        public SettingsData settings;
    }
    
    /// <summary>
    /// Données des paramètres
    /// </summary>
    [System.Serializable]
    public class SettingsData
    {
        public float masterVolume;
        public float musicVolume;
        public float sfxVolume;
        public bool fullscreen;
        public string resolution;
        public int quality;
        public bool autoSave;
    }
    
    /// <summary>
    /// Informations sur une sauvegarde
    /// </summary>
    public class SaveInfo
    {
        public int slot;
        public System.DateTime saveTime;
        public int playerLevel;
        public float totalPlayTime;
        public int totalRuns;
        public string gameVersion;
        public long fileSize;
    }
}
