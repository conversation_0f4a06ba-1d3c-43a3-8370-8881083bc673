# 🚀 Guide de Déploiement API2CSV

## 📋 Prérequis

- Compte GitHub
- Git installé localement
- Navigateur web moderne

## 🔧 Déploiement sur GitHub Pages

### 1. Créer le repository GitHub

```bash
# Créer un nouveau repository sur GitHub nommé "api2csv"
# Puis cloner localement
git clone https://github.com/votre-username/api2csv.git
cd api2csv
```

### 2. Ajouter les fichiers

```bash
# Copier tous les fichiers du projet dans le dossier
# Puis ajouter au git
git add .
git commit -m "Initial commit: API2CSV v1.0"
git push origin main
```

### 3. Activer GitHub Pages

1. Aller sur votre repository GitHub
2. Cliquer sur **Settings**
3. Scroller jusqu'à **Pages**
4. Dans **Source**, sélectionner **Deploy from a branch**
5. Choisir **main** branch et **/ (root)**
6. <PERSON><PERSON>r **Save**

### 4. Configurer le domaine personnalisé (optionnel)

Si vous avez un domaine personnalisé :

1. Dans **Pages** settings, ajouter votre domaine dans **Custom domain**
2. Créer un fichier `CNAME` à la racine avec votre domaine :
```
api2csv.votre-domaine.com
```

### 5. Vérifier le déploiement

- L'URL sera : `https://votre-username.github.io/api2csv/`
- Le déploiement prend 5-10 minutes
- Vérifier que l'application fonctionne correctement

## 🔄 Mise à jour

Pour mettre à jour l'application :

```bash
# Modifier les fichiers
git add .
git commit -m "Update: description des changements"
git push origin main
```

GitHub Pages se met à jour automatiquement.

## 🌐 Autres options de déploiement

### Netlify

1. Connecter votre repository GitHub à Netlify
2. Build settings : laisser vide (site statique)
3. Publish directory : `/` (racine)
4. Deploy automatique à chaque push

### Vercel

1. Importer le repository depuis GitHub
2. Framework preset : **Other**
3. Build command : laisser vide
4. Output directory : `./`
5. Deploy automatique

### Surge.sh

```bash
# Installer surge
npm install -g surge

# Déployer
cd votre-projet
surge

# Suivre les instructions pour choisir le domaine
```

## 📊 Analytics et Monitoring

### Google Analytics

Ajouter dans `index.html` avant `</head>` :

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### Plausible Analytics (alternative privacy-friendly)

```html
<script defer data-domain="votre-domaine.com" src="https://plausible.io/js/script.js"></script>
```

## 🔒 Sécurité

### Content Security Policy

Ajouter dans `index.html` :

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com;
  style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;
  img-src 'self' data:;
  connect-src 'self';
">
```

### HTTPS

- GitHub Pages active HTTPS automatiquement
- Pour domaines personnalisés, activer **Enforce HTTPS** dans les settings

## 📈 SEO et Performance

### Sitemap

Créer `sitemap.xml` :

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://votre-username.github.io/api2csv/</loc>
    <lastmod>2025-01-20</lastmod>
    <changefreq>monthly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>
```

### robots.txt

Créer `robots.txt` :

```
User-agent: *
Allow: /

Sitemap: https://votre-username.github.io/api2csv/sitemap.xml
```

## 🐛 Debugging

### Erreurs communes

1. **404 sur GitHub Pages** : Vérifier que `index.html` est à la racine
2. **CSS/JS ne se charge pas** : Vérifier les chemins relatifs
3. **CORS errors** : Normal pour les CDN, ignorer si l'app fonctionne
4. **Build failed** : Vérifier la syntaxe des fichiers YAML

### Logs GitHub Pages

- Aller dans **Actions** tab du repository
- Voir les logs de déploiement en cas d'erreur

## 📞 Support

En cas de problème :

1. Vérifier la [documentation GitHub Pages](https://docs.github.com/en/pages)
2. Ouvrir une issue sur le repository
3. Contacter le support GitHub si nécessaire

## ✅ Checklist de déploiement

- [ ] Repository créé et configuré
- [ ] GitHub Pages activé
- [ ] Application accessible via l'URL
- [ ] Toutes les fonctionnalités testées
- [ ] Analytics configuré (optionnel)
- [ ] Domaine personnalisé configuré (optionnel)
- [ ] SEO optimisé (sitemap, robots.txt)
- [ ] Liens sociaux mis à jour dans README
- [ ] Promotion lancée (Reddit, Dev.to, etc.)

---

**🎉 Félicitations ! Votre application API2CSV est maintenant en ligne !**
