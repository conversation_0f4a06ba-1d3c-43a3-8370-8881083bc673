# Space Clicker - Unity Editor Tools

Ce dossier contient tous les outils d'édition Unity pour Space Clicker.

## 🛠️ Outils Disponibles

### 1. **PrefabGenerator.cs**
Génère automatiquement tous les prefabs nécessaires au jeu.

**Utilisation :**
1. <PERSON><PERSON> dans `Space Clicker > Prefab Generator`
2. Sélectionner les types de prefabs à générer
3. C<PERSON>r sur "Generate All Prefabs"

**Prefabs générés :**
- Core Managers (GameManager, ResourceManager, UpgradeManager)
- UI Elements (Canvas, boutons, panneaux)
- Modules de jeu (SolarPanel, MiningDrill, etc.)
- Effets visuels (particules, animations)
- Audio (AudioManager, SFXPlayer)

### 2. **SceneSetup.cs**
Configure automatiquement la scène principale du jeu.

**Utilisation :**
1. Aller dans `Space Clicker > Scene Setup`
2. Choisir les éléments à configurer
3. Cliquer sur "Create New Scene" ou "Setup Current Scene"

**Éléments configurés :**
- Caméra principale
- EventSystem pour l'UI
- Managers de jeu
- Canvas avec tous les éléments UI
- Modules positionnés
- Système audio

### 3. **AssetGenerator.cs**
Génère les assets visuels de base (textures, sprites, matériaux).

**Utilisation :**
1. Aller dans `Space Clicker > Asset Generator`
2. Configurer les chemins de destination
3. Cliquer sur "Generate All Assets"

**Assets générés :**
- Textures (backgrounds, noise, particules)
- Sprites (modules, UI, icônes)
- Matériaux (UI, effets)
- Structure de dossiers

### 4. **BuildManager.cs**
Gère la compilation du jeu pour différentes plateformes.

**Utilisation :**
1. Aller dans `Space Clicker > Build Manager`
2. Sélectionner les plateformes cibles
3. Configurer les options de build
4. Cliquer sur "Build All Selected Platforms"

**Plateformes supportées :**
- Windows (x64)
- macOS
- Linux
- Android
- WebGL

## 📋 Guide d'Utilisation Rapide

### Première Configuration
1. **Générer les Assets** : `Asset Generator > Generate All Assets`
2. **Créer les Prefabs** : `Prefab Generator > Generate All Prefabs`
3. **Configurer la Scène** : `Scene Setup > Create New Scene`
4. **Tester le Jeu** : Appuyer sur Play dans Unity

### Workflow de Développement
1. Modifier les scripts dans `/Scripts/`
2. Régénérer les prefabs si nécessaire
3. Tester les changements
4. Builder pour les plateformes cibles

### Avant la Release
1. Vérifier tous les tests : `Tests/TestRunner`
2. Configurer les paramètres de build
3. Builder toutes les plateformes
4. Tester les builds

## 🎯 Configuration Recommandée

### Paramètres Unity
- **Scripting Backend** : IL2CPP (pour les builds finales)
- **Api Compatibility Level** : .NET Standard 2.1
- **Color Space** : Linear (pour de meilleurs visuels)

### Paramètres de Build
- **Development Build** : Activé pour les tests
- **Auto Run Build** : Activé pour tester rapidement
- **Compression** : Activée pour réduire la taille

### Structure de Projet
```
Assets/
├── Scripts/           # Tous les scripts C#
├── Prefabs/          # Prefabs générés
├── Sprites/          # Sprites et icônes
├── Materials/        # Matériaux
├── Textures/         # Textures
├── Audio/            # Fichiers audio
├── Scenes/           # Scènes Unity
└── Tests/            # Tests unitaires
```

## 🔧 Dépannage

### Erreurs Communes

**"Prefab not found"**
- Vérifier que les prefabs ont été générés
- Vérifier le chemin dans PrefabGenerator

**"Script missing"**
- Recompiler les scripts (Ctrl+R)
- Vérifier les références de namespace

**"Build failed"**
- Vérifier les paramètres de plateforme
- Nettoyer le dossier de build
- Vérifier les scènes dans Build Settings

### Performance

**Si Unity est lent :**
- Fermer les fenêtres d'outils non utilisées
- Réduire la qualité dans Game View
- Utiliser "Fast" dans Lighting Settings

**Si les builds sont lentes :**
- Désactiver "Development Build"
- Utiliser la compression
- Exclure les assets non utilisés

## 📝 Notes Importantes

1. **Toujours sauvegarder** avant d'utiliser les générateurs
2. **Tester après chaque génération** pour vérifier le bon fonctionnement
3. **Versionner les changements** avec Git
4. **Documenter les modifications** personnalisées

## 🚀 Prochaines Étapes

Après avoir configuré le projet :
1. Personnaliser les sprites et textures
2. Ajouter des fichiers audio
3. Ajuster les valeurs de gameplay
4. Optimiser les performances
5. Tester sur les plateformes cibles

## 📞 Support

En cas de problème :
1. Vérifier la console Unity pour les erreurs
2. Consulter la documentation Unity
3. Vérifier les logs dans `Console Window`
4. Redémarrer Unity si nécessaire
