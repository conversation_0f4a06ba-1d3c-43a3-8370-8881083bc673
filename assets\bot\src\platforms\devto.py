"""
Connecteur Dev.to utilisant l'API REST.
"""

import requests
import os
from typing import Dict, Any, Optional
from .base import PlatformConnector, PublicationResult
import logging

logger = logging.getLogger(__name__)

class DevtoConnector(PlatformConnector):
    """Connecteur pour Dev.to."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = None
        self.base_url = "https://dev.to/api"
        self.default_tags = config.get('default_tags', ['webdev', 'javascript'])
        
    def authenticate(self) -> bool:
        """Authentifie avec l'API Dev.to."""
        try:
            self.api_key = os.getenv('DEVTO_API_KEY')
            if not self.api_key:
                logger.error("Clé API Dev.to manquante")
                return False
            
            # Test de connexion - récupérer le profil utilisateur
            headers = {
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/users/me",
                headers=headers
            )
            
            if response.status_code == 200:
                user_data = response.json()
                username = user_data.get('username', 'unknown')
                logger.info(f"Authentifié sur Dev.to en tant que @{username}")
                self.authenticated = True
                return True
            else:
                logger.error(f"Échec de l'authentification Dev.to: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur d'authentification Dev.to: {e}")
            return False
    
    def validate_post(self, title: str, body: str, **kwargs) -> bool:
        """Valide qu'un post respecte les contraintes Dev.to."""
        # Vérifier la longueur du titre
        if len(title) > 128:
            logger.warning(f"Titre Dev.to trop long: {len(title)} caractères (max: 128)")
            return False
        
        # Vérifier les tags
        tags = kwargs.get('tags', [])
        if len(tags) > 4:
            logger.warning(f"Trop de tags pour Dev.to: {len(tags)} (max: 4)")
            return False
            
        return True
    
    def publish_post(self, title: str, body: str, **kwargs) -> PublicationResult:
        """Publie un article sur Dev.to."""
        if not self.authenticated:
            return PublicationResult(
                success=False,
                platform="devto",
                error="Non authentifié"
            )
        
        try:
            # Préparer les tags
            tags = kwargs.get('tags', [])
            if not tags:
                tags = self.default_tags
            
            # Limiter à 4 tags maximum
            tags = tags[:4]
            
            # Préparer le contenu en Markdown
            article_body = self._prepare_devto_article(title, body)
            
            # Préparer la payload
            article_data = {
                "article": {
                    "title": title,
                    "published": True,
                    "body_markdown": article_body,
                    "tags": tags,
                    "series": kwargs.get('series'),
                    "canonical_url": kwargs.get('canonical_url')
                }
            }
            
            # Publier l'article
            headers = {
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                f"{self.base_url}/articles",
                json=article_data,
                headers=headers
            )
            
            if response.status_code == 201:
                response_data = response.json()
                article_id = response_data.get('id')
                article_url = response_data.get('url')
                
                self.log_success(kwargs.get('post_id', 'unknown'), response_data)
                
                return PublicationResult(
                    success=True,
                    platform="devto",
                    post_id=str(article_id),
                    response_data={
                        'url': article_url,
                        'slug': response_data.get('slug')
                    }
                )
            else:
                error_msg = f"Erreur HTTP {response.status_code}: {response.text}"
                return PublicationResult(
                    success=False,
                    platform="devto",
                    error=error_msg
                )
                
        except Exception as e:
            self.log_error(kwargs.get('post_id', 'unknown'), e)
            return PublicationResult(
                success=False,
                platform="devto",
                error=str(e)
            )
    
    def _prepare_devto_article(self, title: str, body: str) -> str:
        """Prépare le contenu de l'article en Markdown pour Dev.to."""
        # Dev.to attend du Markdown, on peut structurer le contenu
        article_body = body
        
        # Si le body ne contient pas déjà le titre, on peut l'ajouter comme sous-titre
        if title.lower() not in body.lower():
            article_body = f"## {title}\n\n{body}"
        
        # Ajouter une signature ou call-to-action
        article_body += "\n\n---\n\n*Qu'en pensez-vous ? N'hésitez pas à partager vos retours en commentaire !*"
        
        return article_body
    
    def get_article_stats(self, article_id: str) -> Dict[str, Any]:
        """Récupère les statistiques d'un article."""
        try:
            headers = {
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/articles/{article_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                article_data = response.json()
                return {
                    'views': article_data.get('page_views_count', 0),
                    'reactions': article_data.get('public_reactions_count', 0),
                    'comments': article_data.get('comments_count', 0),
                    'url': article_data.get('url'),
                    'published_at': article_data.get('published_at')
                }
            else:
                logger.error(f"Erreur lors de la récupération des stats: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des stats de l'article {article_id}: {e}")
            return {}
    
    def list_my_articles(self, per_page: int = 30) -> list:
        """Liste les articles de l'utilisateur."""
        try:
            headers = {
                'api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/articles/me?per_page={per_page}",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Erreur lors de la récupération des articles: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des articles: {e}")
            return []
