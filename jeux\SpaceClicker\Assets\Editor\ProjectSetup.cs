#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Script d'automatisation pour configurer le projet Unity
    /// </summary>
    public class ProjectSetup : EditorWindow
    {
        private bool setupComplete = false;
        private string companyName = "YourCompany";
        private string productName = "Space Clicker";
        private string bundleIdentifier = "com.yourcompany.spaceclicker";
        
        [MenuItem("Space Clicker/Project Setup")]
        public static void ShowWindow()
        {
            GetWindow<ProjectSetup>("Space Clicker Setup");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker - Configuration du Projet", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            if (!setupComplete)
            {
                DrawSetupUI();
            }
            else
            {
                DrawCompletedUI();
            }
        }
        
        private void DrawSetupUI()
        {
            GUILayout.Label("Configuration de base :", EditorStyles.boldLabel);
            
            companyName = EditorGUILayout.TextField("Company Name:", companyName);
            productName = EditorGUILayout.TextField("Product Name:", productName);
            bundleIdentifier = EditorGUILayout.TextField("Bundle Identifier:", bundleIdentifier);
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Configurer le Projet", GUILayout.Height(30)))
            {
                SetupProject();
            }
            
            GUILayout.Space(10);
            
            GUILayout.Label("Actions individuelles :", EditorStyles.boldLabel);
            
            if (GUILayout.Button("Configurer Player Settings"))
            {
                ConfigurePlayerSettings();
            }
            
            if (GUILayout.Button("Créer les Dossiers"))
            {
                CreateFolderStructure();
            }
            
            if (GUILayout.Button("Configurer les Layers"))
            {
                ConfigureLayers();
            }
            
            if (GUILayout.Button("Configurer les Tags"))
            {
                ConfigureTags();
            }
            
            if (GUILayout.Button("Importer les Packages"))
            {
                ImportRequiredPackages();
            }
        }
        
        private void DrawCompletedUI()
        {
            GUILayout.Label("✅ Configuration terminée !", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            GUILayout.Label("Prochaines étapes :");
            GUILayout.Label("1. Configurer Firebase (voir README.md)");
            GUILayout.Label("2. Ajouter vos assets (sprites, audio)");
            GUILayout.Label("3. Créer la scène principale");
            GUILayout.Label("4. Tester le build");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Réinitialiser"))
            {
                setupComplete = false;
            }
        }
        
        private void SetupProject()
        {
            EditorUtility.DisplayProgressBar("Configuration", "Configuration du projet...", 0f);
            
            try
            {
                ConfigurePlayerSettings();
                EditorUtility.DisplayProgressBar("Configuration", "Création des dossiers...", 0.2f);
                
                CreateFolderStructure();
                EditorUtility.DisplayProgressBar("Configuration", "Configuration des layers...", 0.4f);
                
                ConfigureLayers();
                EditorUtility.DisplayProgressBar("Configuration", "Configuration des tags...", 0.6f);
                
                ConfigureTags();
                EditorUtility.DisplayProgressBar("Configuration", "Import des packages...", 0.8f);
                
                ImportRequiredPackages();
                EditorUtility.DisplayProgressBar("Configuration", "Finalisation...", 1f);
                
                setupComplete = true;
                
                Debug.Log("✅ Configuration du projet Space Clicker terminée !");
                EditorUtility.DisplayDialog("Configuration terminée", 
                    "Le projet Space Clicker a été configuré avec succès !\n\n" +
                    "Consultez le README.md pour les prochaines étapes.", "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Erreur lors de la configuration : {e.Message}");
                EditorUtility.DisplayDialog("Erreur", $"Erreur lors de la configuration :\n{e.Message}", "OK");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }
        
        private void ConfigurePlayerSettings()
        {
            // Configuration générale
            PlayerSettings.companyName = companyName;
            PlayerSettings.productName = productName;
            
            // Android
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, bundleIdentifier);
            PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel21;
            PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevelAuto;
            
            // iOS
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, bundleIdentifier);
            PlayerSettings.iOS.targetOSVersionString = "12.0";
            
            // Configuration commune
            PlayerSettings.defaultInterfaceOrientation = UIOrientation.Portrait;
            PlayerSettings.allowedAutorotateToPortrait = true;
            PlayerSettings.allowedAutorotateToPortraitUpsideDown = false;
            PlayerSettings.allowedAutorotateToLandscapeLeft = false;
            PlayerSettings.allowedAutorotateToLandscapeRight = false;
            
            // Optimisations
            PlayerSettings.stripEngineCode = true;
            PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Android, ManagedStrippingLevel.Medium);
            PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.iOS, ManagedStrippingLevel.Medium);
            
            Debug.Log("✅ Player Settings configurés");
        }
        
        private void CreateFolderStructure()
        {
            string[] folders = {
                "Assets/Scenes",
                "Assets/Scripts/Core",
                "Assets/Scripts/UI",
                "Assets/Scripts/Save",
                "Assets/Scripts/Utils",
                "Assets/Scripts/Monetization",
                "Assets/Scripts/Analytics",
                "Assets/Prefabs/UI",
                "Assets/Prefabs/Effects",
                "Assets/Prefabs/Modules",
                "Assets/Resources/Audio/SFX",
                "Assets/Resources/Audio/Music",
                "Assets/Resources/Sprites/UI",
                "Assets/Resources/Sprites/Icons",
                "Assets/Resources/Sprites/Backgrounds",
                "Assets/Resources/Data",
                "Assets/StreamingAssets",
                "Assets/Editor",
                "Assets/Plugins"
            };
            
            foreach (string folder in folders)
            {
                if (!AssetDatabase.IsValidFolder(folder))
                {
                    string parentFolder = Path.GetDirectoryName(folder);
                    string folderName = Path.GetFileName(folder);
                    AssetDatabase.CreateFolder(parentFolder, folderName);
                }
            }
            
            AssetDatabase.Refresh();
            Debug.Log("✅ Structure de dossiers créée");
        }
        
        private void ConfigureLayers()
        {
            // Ajouter les layers personnalisés
            string[] customLayers = {
                "UI",
                "Modules",
                "Effects",
                "Background"
            };
            
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty layersProp = tagManager.FindProperty("layers");
            
            for (int i = 0; i < customLayers.Length; i++)
            {
                int layerIndex = 8 + i; // Commencer après les layers par défaut
                if (layerIndex < 32)
                {
                    SerializedProperty layerProp = layersProp.GetArrayElementAtIndex(layerIndex);
                    if (string.IsNullOrEmpty(layerProp.stringValue))
                    {
                        layerProp.stringValue = customLayers[i];
                    }
                }
            }
            
            tagManager.ApplyModifiedProperties();
            Debug.Log("✅ Layers configurés");
        }
        
        private void ConfigureTags()
        {
            // Ajouter les tags personnalisés
            string[] customTags = {
                "EnergyModule",
                "MineralsModule", 
                "ResearchModule",
                "UpgradeButton",
                "ClickEffect"
            };
            
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");
            
            foreach (string tag in customTags)
            {
                bool tagExists = false;
                for (int i = 0; i < tagsProp.arraySize; i++)
                {
                    if (tagsProp.GetArrayElementAtIndex(i).stringValue == tag)
                    {
                        tagExists = true;
                        break;
                    }
                }
                
                if (!tagExists)
                {
                    tagsProp.InsertArrayElementAtIndex(tagsProp.arraySize);
                    tagsProp.GetArrayElementAtIndex(tagsProp.arraySize - 1).stringValue = tag;
                }
            }
            
            tagManager.ApplyModifiedProperties();
            Debug.Log("✅ Tags configurés");
        }
        
        private void ImportRequiredPackages()
        {
            // Liste des packages requis
            string[] requiredPackages = {
                "com.unity.textmeshpro",
                "com.unity.analytics",
                "com.unity.ads",
                "com.unity.purchasing"
            };
            
            foreach (string package in requiredPackages)
            {
                UnityEditor.PackageManager.Client.Add(package);
            }
            
            Debug.Log("✅ Import des packages lancé (peut prendre quelques minutes)");
        }
        
        [MenuItem("Space Clicker/Create Main Scene")]
        public static void CreateMainScene()
        {
            // Créer une nouvelle scène
            var scene = UnityEngine.SceneManagement.SceneManager.CreateScene("MainGame");
            
            // Créer les objets de base
            GameObject gameManager = new GameObject("GameManager");
            gameManager.AddComponent<SpaceClicker.Core.GameManager>();
            
            GameObject resourceManager = new GameObject("ResourceManager");
            resourceManager.AddComponent<SpaceClicker.Core.ResourceManager>();
            
            GameObject upgradeManager = new GameObject("UpgradeManager");
            upgradeManager.AddComponent<SpaceClicker.Core.UpgradeManager>();
            
            GameObject saveSystem = new GameObject("SaveSystem");
            saveSystem.AddComponent<SpaceClicker.Core.SaveSystem>();
            
            // Créer le Canvas UI
            GameObject canvas = new GameObject("Canvas");
            canvas.AddComponent<Canvas>();
            canvas.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvas.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            GameObject uiManager = new GameObject("UIManager");
            uiManager.transform.SetParent(canvas.transform);
            uiManager.AddComponent<SpaceClicker.Core.UIManager>();
            
            // Sauvegarder la scène
            UnityEditor.SceneManagement.EditorSceneManager.SaveScene(scene, "Assets/Scenes/MainGame.unity");
            
            Debug.Log("✅ Scène principale créée : Assets/Scenes/MainGame.unity");
        }
    }
}
#endif
