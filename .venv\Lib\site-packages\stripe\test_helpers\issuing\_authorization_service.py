# -*- coding: utf-8 -*-
# File generated from our OpenAPI spec
from stripe._request_options import RequestOptions
from stripe._stripe_service import StripeService
from stripe._util import sanitize_id
from stripe.issuing._authorization import Authorization
from typing import List, cast
from typing_extensions import Literal, NotRequired, TypedDict


class AuthorizationService(StripeService):
    class CaptureParams(TypedDict):
        capture_amount: NotRequired[int]
        """
        The amount to capture from the authorization. If not provided, the full amount of the authorization will be captured. This amount is in the authorization currency and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        close_authorization: NotRequired[bool]
        """
        Whether to close the authorization after capture. Defaults to true. Set to false to enable multi-capture flows.
        """
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        purchase_details: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetails"
        ]
        """
        Additional purchase information that is optionally provided by the merchant.
        """

    class CaptureParamsPurchaseDetails(TypedDict):
        fleet: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleet"
        ]
        """
        Fleet-specific information for transactions using Fleet cards.
        """
        flight: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFlight"
        ]
        """
        Information about the flight that was purchased with this transaction.
        """
        fuel: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFuel"
        ]
        """
        Information about fuel that was purchased with this transaction.
        """
        lodging: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsLodging"
        ]
        """
        Information about lodging that was purchased with this transaction.
        """
        receipt: NotRequired[
            List["AuthorizationService.CaptureParamsPurchaseDetailsReceipt"]
        ]
        """
        The line items in the purchase.
        """
        reference: NotRequired[str]
        """
        A merchant-specific order number.
        """

    class CaptureParamsPurchaseDetailsFleet(TypedDict):
        cardholder_prompt_data: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleetCardholderPromptData"
        ]
        """
        Answers to prompts presented to the cardholder at the point of sale. Prompted fields vary depending on the configuration of your physical fleet cards. Typical points of sale support only numeric entry.
        """
        purchase_type: NotRequired[
            Literal[
                "fuel_and_non_fuel_purchase",
                "fuel_purchase",
                "non_fuel_purchase",
            ]
        ]
        """
        The type of purchase. One of `fuel_purchase`, `non_fuel_purchase`, or `fuel_and_non_fuel_purchase`.
        """
        reported_breakdown: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleetReportedBreakdown"
        ]
        """
        More information about the total amount. This information is not guaranteed to be accurate as some merchants may provide unreliable data.
        """
        service_type: NotRequired[
            Literal["full_service", "non_fuel_transaction", "self_service"]
        ]
        """
        The type of fuel service. One of `non_fuel_transaction`, `full_service`, or `self_service`.
        """

    class CaptureParamsPurchaseDetailsFleetCardholderPromptData(TypedDict):
        driver_id: NotRequired[str]
        """
        Driver ID.
        """
        odometer: NotRequired[int]
        """
        Odometer reading.
        """
        unspecified_id: NotRequired[str]
        """
        An alphanumeric ID. This field is used when a vehicle ID, driver ID, or generic ID is entered by the cardholder, but the merchant or card network did not specify the prompt type.
        """
        user_id: NotRequired[str]
        """
        User ID.
        """
        vehicle_number: NotRequired[str]
        """
        Vehicle number.
        """

    class CaptureParamsPurchaseDetailsFleetReportedBreakdown(TypedDict):
        fuel: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleetReportedBreakdownFuel"
        ]
        """
        Breakdown of fuel portion of the purchase.
        """
        non_fuel: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleetReportedBreakdownNonFuel"
        ]
        """
        Breakdown of non-fuel portion of the purchase.
        """
        tax: NotRequired[
            "AuthorizationService.CaptureParamsPurchaseDetailsFleetReportedBreakdownTax"
        ]
        """
        Information about tax included in this transaction.
        """

    class CaptureParamsPurchaseDetailsFleetReportedBreakdownFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross fuel amount that should equal Fuel Volume multipled by Fuel Unit Cost, inclusive of taxes.
        """

    class CaptureParamsPurchaseDetailsFleetReportedBreakdownNonFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross non-fuel amount that should equal the sum of the line items, inclusive of taxes.
        """

    class CaptureParamsPurchaseDetailsFleetReportedBreakdownTax(TypedDict):
        local_amount_decimal: NotRequired[str]
        """
        Amount of state or provincial Sales Tax included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """
        national_amount_decimal: NotRequired[str]
        """
        Amount of national Sales Tax or VAT included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """

    class CaptureParamsPurchaseDetailsFlight(TypedDict):
        departure_at: NotRequired[int]
        """
        The time that the flight departed.
        """
        passenger_name: NotRequired[str]
        """
        The name of the passenger.
        """
        refundable: NotRequired[bool]
        """
        Whether the ticket is refundable.
        """
        segments: NotRequired[
            List[
                "AuthorizationService.CaptureParamsPurchaseDetailsFlightSegment"
            ]
        ]
        """
        The legs of the trip.
        """
        travel_agency: NotRequired[str]
        """
        The travel agency that issued the ticket.
        """

    class CaptureParamsPurchaseDetailsFlightSegment(TypedDict):
        arrival_airport_code: NotRequired[str]
        """
        The three-letter IATA airport code of the flight's destination.
        """
        carrier: NotRequired[str]
        """
        The airline carrier code.
        """
        departure_airport_code: NotRequired[str]
        """
        The three-letter IATA airport code that the flight departed from.
        """
        flight_number: NotRequired[str]
        """
        The flight number.
        """
        service_class: NotRequired[str]
        """
        The flight's service class.
        """
        stopover_allowed: NotRequired[bool]
        """
        Whether a stopover is allowed on this flight.
        """

    class CaptureParamsPurchaseDetailsFuel(TypedDict):
        industry_product_code: NotRequired[str]
        """
        [Conexxus Payment System Product Code](https://www.conexxus.org/conexxus-payment-system-product-codes) identifying the primary fuel product purchased.
        """
        quantity_decimal: NotRequired[str]
        """
        The quantity of `unit`s of fuel that was dispensed, represented as a decimal string with at most 12 decimal places.
        """
        type: NotRequired[
            Literal[
                "diesel",
                "other",
                "unleaded_plus",
                "unleaded_regular",
                "unleaded_super",
            ]
        ]
        """
        The type of fuel that was purchased. One of `diesel`, `unleaded_plus`, `unleaded_regular`, `unleaded_super`, or `other`.
        """
        unit: NotRequired[
            Literal[
                "charging_minute",
                "imperial_gallon",
                "kilogram",
                "kilowatt_hour",
                "liter",
                "other",
                "pound",
                "us_gallon",
            ]
        ]
        """
        The units for `quantity_decimal`. One of `charging_minute`, `imperial_gallon`, `kilogram`, `kilowatt_hour`, `liter`, `pound`, `us_gallon`, or `other`.
        """
        unit_cost_decimal: NotRequired[str]
        """
        The cost in cents per each unit of fuel, represented as a decimal string with at most 12 decimal places.
        """

    class CaptureParamsPurchaseDetailsLodging(TypedDict):
        check_in_at: NotRequired[int]
        """
        The time of checking into the lodging.
        """
        nights: NotRequired[int]
        """
        The number of nights stayed at the lodging.
        """

    class CaptureParamsPurchaseDetailsReceipt(TypedDict):
        description: NotRequired[str]
        quantity: NotRequired[str]
        total: NotRequired[int]
        unit_cost: NotRequired[int]

    class CreateParams(TypedDict):
        amount: NotRequired[int]
        """
        The total amount to attempt to authorize. This amount is in the provided currency, or defaults to the card's currency, and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        amount_details: NotRequired[
            "AuthorizationService.CreateParamsAmountDetails"
        ]
        """
        Detailed breakdown of amount components. These amounts are denominated in `currency` and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        authorization_method: NotRequired[
            Literal["chip", "contactless", "keyed_in", "online", "swipe"]
        ]
        """
        How the card details were provided. Defaults to online.
        """
        card: str
        """
        Card associated with this authorization.
        """
        currency: NotRequired[str]
        """
        The currency of the authorization. If not provided, defaults to the currency of the card. Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
        """
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        fleet: NotRequired["AuthorizationService.CreateParamsFleet"]
        """
        Fleet-specific information for authorizations using Fleet cards.
        """
        fuel: NotRequired["AuthorizationService.CreateParamsFuel"]
        """
        Information about fuel that was purchased with this transaction.
        """
        is_amount_controllable: NotRequired[bool]
        """
        If set `true`, you may provide [amount](https://stripe.com/docs/api/issuing/authorizations/approve#approve_issuing_authorization-amount) to control how much to hold for the authorization.
        """
        merchant_amount: NotRequired[int]
        """
        The total amount to attempt to authorize. This amount is in the provided merchant currency, and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        merchant_currency: NotRequired[str]
        """
        The currency of the authorization. If not provided, defaults to the currency of the card. Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
        """
        merchant_data: NotRequired[
            "AuthorizationService.CreateParamsMerchantData"
        ]
        """
        Details about the seller (grocery store, e-commerce website, etc.) where the card authorization happened.
        """
        network_data: NotRequired[
            "AuthorizationService.CreateParamsNetworkData"
        ]
        """
        Details about the authorization, such as identifiers, set by the card network.
        """
        verification_data: NotRequired[
            "AuthorizationService.CreateParamsVerificationData"
        ]
        """
        Verifications that Stripe performed on information that the cardholder provided to the merchant.
        """
        wallet: NotRequired[Literal["apple_pay", "google_pay", "samsung_pay"]]
        """
        The digital wallet used for this transaction. One of `apple_pay`, `google_pay`, or `samsung_pay`. Will populate as `null` when no digital wallet was utilized.
        """

    class CreateParamsAmountDetails(TypedDict):
        atm_fee: NotRequired[int]
        """
        The ATM withdrawal fee.
        """
        cashback_amount: NotRequired[int]
        """
        The amount of cash requested by the cardholder.
        """

    class CreateParamsFleet(TypedDict):
        cardholder_prompt_data: NotRequired[
            "AuthorizationService.CreateParamsFleetCardholderPromptData"
        ]
        """
        Answers to prompts presented to the cardholder at the point of sale. Prompted fields vary depending on the configuration of your physical fleet cards. Typical points of sale support only numeric entry.
        """
        purchase_type: NotRequired[
            Literal[
                "fuel_and_non_fuel_purchase",
                "fuel_purchase",
                "non_fuel_purchase",
            ]
        ]
        """
        The type of purchase. One of `fuel_purchase`, `non_fuel_purchase`, or `fuel_and_non_fuel_purchase`.
        """
        reported_breakdown: NotRequired[
            "AuthorizationService.CreateParamsFleetReportedBreakdown"
        ]
        """
        More information about the total amount. This information is not guaranteed to be accurate as some merchants may provide unreliable data.
        """
        service_type: NotRequired[
            Literal["full_service", "non_fuel_transaction", "self_service"]
        ]
        """
        The type of fuel service. One of `non_fuel_transaction`, `full_service`, or `self_service`.
        """

    class CreateParamsFleetCardholderPromptData(TypedDict):
        driver_id: NotRequired[str]
        """
        Driver ID.
        """
        odometer: NotRequired[int]
        """
        Odometer reading.
        """
        unspecified_id: NotRequired[str]
        """
        An alphanumeric ID. This field is used when a vehicle ID, driver ID, or generic ID is entered by the cardholder, but the merchant or card network did not specify the prompt type.
        """
        user_id: NotRequired[str]
        """
        User ID.
        """
        vehicle_number: NotRequired[str]
        """
        Vehicle number.
        """

    class CreateParamsFleetReportedBreakdown(TypedDict):
        fuel: NotRequired[
            "AuthorizationService.CreateParamsFleetReportedBreakdownFuel"
        ]
        """
        Breakdown of fuel portion of the purchase.
        """
        non_fuel: NotRequired[
            "AuthorizationService.CreateParamsFleetReportedBreakdownNonFuel"
        ]
        """
        Breakdown of non-fuel portion of the purchase.
        """
        tax: NotRequired[
            "AuthorizationService.CreateParamsFleetReportedBreakdownTax"
        ]
        """
        Information about tax included in this transaction.
        """

    class CreateParamsFleetReportedBreakdownFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross fuel amount that should equal Fuel Volume multipled by Fuel Unit Cost, inclusive of taxes.
        """

    class CreateParamsFleetReportedBreakdownNonFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross non-fuel amount that should equal the sum of the line items, inclusive of taxes.
        """

    class CreateParamsFleetReportedBreakdownTax(TypedDict):
        local_amount_decimal: NotRequired[str]
        """
        Amount of state or provincial Sales Tax included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """
        national_amount_decimal: NotRequired[str]
        """
        Amount of national Sales Tax or VAT included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """

    class CreateParamsFuel(TypedDict):
        industry_product_code: NotRequired[str]
        """
        [Conexxus Payment System Product Code](https://www.conexxus.org/conexxus-payment-system-product-codes) identifying the primary fuel product purchased.
        """
        quantity_decimal: NotRequired[str]
        """
        The quantity of `unit`s of fuel that was dispensed, represented as a decimal string with at most 12 decimal places.
        """
        type: NotRequired[
            Literal[
                "diesel",
                "other",
                "unleaded_plus",
                "unleaded_regular",
                "unleaded_super",
            ]
        ]
        """
        The type of fuel that was purchased. One of `diesel`, `unleaded_plus`, `unleaded_regular`, `unleaded_super`, or `other`.
        """
        unit: NotRequired[
            Literal[
                "charging_minute",
                "imperial_gallon",
                "kilogram",
                "kilowatt_hour",
                "liter",
                "other",
                "pound",
                "us_gallon",
            ]
        ]
        """
        The units for `quantity_decimal`. One of `charging_minute`, `imperial_gallon`, `kilogram`, `kilowatt_hour`, `liter`, `pound`, `us_gallon`, or `other`.
        """
        unit_cost_decimal: NotRequired[str]
        """
        The cost in cents per each unit of fuel, represented as a decimal string with at most 12 decimal places.
        """

    class CreateParamsMerchantData(TypedDict):
        category: NotRequired[
            Literal[
                "ac_refrigeration_repair",
                "accounting_bookkeeping_services",
                "advertising_services",
                "agricultural_cooperative",
                "airlines_air_carriers",
                "airports_flying_fields",
                "ambulance_services",
                "amusement_parks_carnivals",
                "antique_reproductions",
                "antique_shops",
                "aquariums",
                "architectural_surveying_services",
                "art_dealers_and_galleries",
                "artists_supply_and_craft_shops",
                "auto_and_home_supply_stores",
                "auto_body_repair_shops",
                "auto_paint_shops",
                "auto_service_shops",
                "automated_cash_disburse",
                "automated_fuel_dispensers",
                "automobile_associations",
                "automotive_parts_and_accessories_stores",
                "automotive_tire_stores",
                "bail_and_bond_payments",
                "bakeries",
                "bands_orchestras",
                "barber_and_beauty_shops",
                "betting_casino_gambling",
                "bicycle_shops",
                "billiard_pool_establishments",
                "boat_dealers",
                "boat_rentals_and_leases",
                "book_stores",
                "books_periodicals_and_newspapers",
                "bowling_alleys",
                "bus_lines",
                "business_secretarial_schools",
                "buying_shopping_services",
                "cable_satellite_and_other_pay_television_and_radio",
                "camera_and_photographic_supply_stores",
                "candy_nut_and_confectionery_stores",
                "car_and_truck_dealers_new_used",
                "car_and_truck_dealers_used_only",
                "car_rental_agencies",
                "car_washes",
                "carpentry_services",
                "carpet_upholstery_cleaning",
                "caterers",
                "charitable_and_social_service_organizations_fundraising",
                "chemicals_and_allied_products",
                "child_care_services",
                "childrens_and_infants_wear_stores",
                "chiropodists_podiatrists",
                "chiropractors",
                "cigar_stores_and_stands",
                "civic_social_fraternal_associations",
                "cleaning_and_maintenance",
                "clothing_rental",
                "colleges_universities",
                "commercial_equipment",
                "commercial_footwear",
                "commercial_photography_art_and_graphics",
                "commuter_transport_and_ferries",
                "computer_network_services",
                "computer_programming",
                "computer_repair",
                "computer_software_stores",
                "computers_peripherals_and_software",
                "concrete_work_services",
                "construction_materials",
                "consulting_public_relations",
                "correspondence_schools",
                "cosmetic_stores",
                "counseling_services",
                "country_clubs",
                "courier_services",
                "court_costs",
                "credit_reporting_agencies",
                "cruise_lines",
                "dairy_products_stores",
                "dance_hall_studios_schools",
                "dating_escort_services",
                "dentists_orthodontists",
                "department_stores",
                "detective_agencies",
                "digital_goods_applications",
                "digital_goods_games",
                "digital_goods_large_volume",
                "digital_goods_media",
                "direct_marketing_catalog_merchant",
                "direct_marketing_combination_catalog_and_retail_merchant",
                "direct_marketing_inbound_telemarketing",
                "direct_marketing_insurance_services",
                "direct_marketing_other",
                "direct_marketing_outbound_telemarketing",
                "direct_marketing_subscription",
                "direct_marketing_travel",
                "discount_stores",
                "doctors",
                "door_to_door_sales",
                "drapery_window_covering_and_upholstery_stores",
                "drinking_places",
                "drug_stores_and_pharmacies",
                "drugs_drug_proprietaries_and_druggist_sundries",
                "dry_cleaners",
                "durable_goods",
                "duty_free_stores",
                "eating_places_restaurants",
                "educational_services",
                "electric_razor_stores",
                "electric_vehicle_charging",
                "electrical_parts_and_equipment",
                "electrical_services",
                "electronics_repair_shops",
                "electronics_stores",
                "elementary_secondary_schools",
                "emergency_services_gcas_visa_use_only",
                "employment_temp_agencies",
                "equipment_rental",
                "exterminating_services",
                "family_clothing_stores",
                "fast_food_restaurants",
                "financial_institutions",
                "fines_government_administrative_entities",
                "fireplace_fireplace_screens_and_accessories_stores",
                "floor_covering_stores",
                "florists",
                "florists_supplies_nursery_stock_and_flowers",
                "freezer_and_locker_meat_provisioners",
                "fuel_dealers_non_automotive",
                "funeral_services_crematories",
                "furniture_home_furnishings_and_equipment_stores_except_appliances",
                "furniture_repair_refinishing",
                "furriers_and_fur_shops",
                "general_services",
                "gift_card_novelty_and_souvenir_shops",
                "glass_paint_and_wallpaper_stores",
                "glassware_crystal_stores",
                "golf_courses_public",
                "government_licensed_horse_dog_racing_us_region_only",
                "government_licensed_online_casions_online_gambling_us_region_only",
                "government_owned_lotteries_non_us_region",
                "government_owned_lotteries_us_region_only",
                "government_services",
                "grocery_stores_supermarkets",
                "hardware_equipment_and_supplies",
                "hardware_stores",
                "health_and_beauty_spas",
                "hearing_aids_sales_and_supplies",
                "heating_plumbing_a_c",
                "hobby_toy_and_game_shops",
                "home_supply_warehouse_stores",
                "hospitals",
                "hotels_motels_and_resorts",
                "household_appliance_stores",
                "industrial_supplies",
                "information_retrieval_services",
                "insurance_default",
                "insurance_underwriting_premiums",
                "intra_company_purchases",
                "jewelry_stores_watches_clocks_and_silverware_stores",
                "landscaping_services",
                "laundries",
                "laundry_cleaning_services",
                "legal_services_attorneys",
                "luggage_and_leather_goods_stores",
                "lumber_building_materials_stores",
                "manual_cash_disburse",
                "marinas_service_and_supplies",
                "marketplaces",
                "masonry_stonework_and_plaster",
                "massage_parlors",
                "medical_and_dental_labs",
                "medical_dental_ophthalmic_and_hospital_equipment_and_supplies",
                "medical_services",
                "membership_organizations",
                "mens_and_boys_clothing_and_accessories_stores",
                "mens_womens_clothing_stores",
                "metal_service_centers",
                "miscellaneous_apparel_and_accessory_shops",
                "miscellaneous_auto_dealers",
                "miscellaneous_business_services",
                "miscellaneous_food_stores",
                "miscellaneous_general_merchandise",
                "miscellaneous_general_services",
                "miscellaneous_home_furnishing_specialty_stores",
                "miscellaneous_publishing_and_printing",
                "miscellaneous_recreation_services",
                "miscellaneous_repair_shops",
                "miscellaneous_specialty_retail",
                "mobile_home_dealers",
                "motion_picture_theaters",
                "motor_freight_carriers_and_trucking",
                "motor_homes_dealers",
                "motor_vehicle_supplies_and_new_parts",
                "motorcycle_shops_and_dealers",
                "motorcycle_shops_dealers",
                "music_stores_musical_instruments_pianos_and_sheet_music",
                "news_dealers_and_newsstands",
                "non_fi_money_orders",
                "non_fi_stored_value_card_purchase_load",
                "nondurable_goods",
                "nurseries_lawn_and_garden_supply_stores",
                "nursing_personal_care",
                "office_and_commercial_furniture",
                "opticians_eyeglasses",
                "optometrists_ophthalmologist",
                "orthopedic_goods_prosthetic_devices",
                "osteopaths",
                "package_stores_beer_wine_and_liquor",
                "paints_varnishes_and_supplies",
                "parking_lots_garages",
                "passenger_railways",
                "pawn_shops",
                "pet_shops_pet_food_and_supplies",
                "petroleum_and_petroleum_products",
                "photo_developing",
                "photographic_photocopy_microfilm_equipment_and_supplies",
                "photographic_studios",
                "picture_video_production",
                "piece_goods_notions_and_other_dry_goods",
                "plumbing_heating_equipment_and_supplies",
                "political_organizations",
                "postal_services_government_only",
                "precious_stones_and_metals_watches_and_jewelry",
                "professional_services",
                "public_warehousing_and_storage",
                "quick_copy_repro_and_blueprint",
                "railroads",
                "real_estate_agents_and_managers_rentals",
                "record_stores",
                "recreational_vehicle_rentals",
                "religious_goods_stores",
                "religious_organizations",
                "roofing_siding_sheet_metal",
                "secretarial_support_services",
                "security_brokers_dealers",
                "service_stations",
                "sewing_needlework_fabric_and_piece_goods_stores",
                "shoe_repair_hat_cleaning",
                "shoe_stores",
                "small_appliance_repair",
                "snowmobile_dealers",
                "special_trade_services",
                "specialty_cleaning",
                "sporting_goods_stores",
                "sporting_recreation_camps",
                "sports_and_riding_apparel_stores",
                "sports_clubs_fields",
                "stamp_and_coin_stores",
                "stationary_office_supplies_printing_and_writing_paper",
                "stationery_stores_office_and_school_supply_stores",
                "swimming_pools_sales",
                "t_ui_travel_germany",
                "tailors_alterations",
                "tax_payments_government_agencies",
                "tax_preparation_services",
                "taxicabs_limousines",
                "telecommunication_equipment_and_telephone_sales",
                "telecommunication_services",
                "telegraph_services",
                "tent_and_awning_shops",
                "testing_laboratories",
                "theatrical_ticket_agencies",
                "timeshares",
                "tire_retreading_and_repair",
                "tolls_bridge_fees",
                "tourist_attractions_and_exhibits",
                "towing_services",
                "trailer_parks_campgrounds",
                "transportation_services",
                "travel_agencies_tour_operators",
                "truck_stop_iteration",
                "truck_utility_trailer_rentals",
                "typesetting_plate_making_and_related_services",
                "typewriter_stores",
                "u_s_federal_government_agencies_or_departments",
                "uniforms_commercial_clothing",
                "used_merchandise_and_secondhand_stores",
                "utilities",
                "variety_stores",
                "veterinary_services",
                "video_amusement_game_supplies",
                "video_game_arcades",
                "video_tape_rental_stores",
                "vocational_trade_schools",
                "watch_jewelry_repair",
                "welding_repair",
                "wholesale_clubs",
                "wig_and_toupee_stores",
                "wires_money_orders",
                "womens_accessory_and_specialty_shops",
                "womens_ready_to_wear_stores",
                "wrecking_and_salvage_yards",
            ]
        ]
        """
        A categorization of the seller's type of business. See our [merchant categories guide](https://stripe.com/docs/issuing/merchant-categories) for a list of possible values.
        """
        city: NotRequired[str]
        """
        City where the seller is located
        """
        country: NotRequired[str]
        """
        Country where the seller is located
        """
        name: NotRequired[str]
        """
        Name of the seller
        """
        network_id: NotRequired[str]
        """
        Identifier assigned to the seller by the card network. Different card networks may assign different network_id fields to the same merchant.
        """
        postal_code: NotRequired[str]
        """
        Postal code where the seller is located
        """
        state: NotRequired[str]
        """
        State where the seller is located
        """
        terminal_id: NotRequired[str]
        """
        An ID assigned by the seller to the location of the sale.
        """
        url: NotRequired[str]
        """
        URL provided by the merchant on a 3DS request
        """

    class CreateParamsNetworkData(TypedDict):
        acquiring_institution_id: NotRequired[str]
        """
        Identifier assigned to the acquirer by the card network.
        """

    class CreateParamsVerificationData(TypedDict):
        address_line1_check: NotRequired[
            Literal["match", "mismatch", "not_provided"]
        ]
        """
        Whether the cardholder provided an address first line and if it matched the cardholder's `billing.address.line1`.
        """
        address_postal_code_check: NotRequired[
            Literal["match", "mismatch", "not_provided"]
        ]
        """
        Whether the cardholder provided a postal code and if it matched the cardholder's `billing.address.postal_code`.
        """
        authentication_exemption: NotRequired[
            "AuthorizationService.CreateParamsVerificationDataAuthenticationExemption"
        ]
        """
        The exemption applied to this authorization.
        """
        cvc_check: NotRequired[Literal["match", "mismatch", "not_provided"]]
        """
        Whether the cardholder provided a CVC and if it matched Stripe's record.
        """
        expiry_check: NotRequired[Literal["match", "mismatch", "not_provided"]]
        """
        Whether the cardholder provided an expiry date and if it matched Stripe's record.
        """
        three_d_secure: NotRequired[
            "AuthorizationService.CreateParamsVerificationDataThreeDSecure"
        ]
        """
        3D Secure details.
        """

    class CreateParamsVerificationDataAuthenticationExemption(TypedDict):
        claimed_by: Literal["acquirer", "issuer"]
        """
        The entity that requested the exemption, either the acquiring merchant or the Issuing user.
        """
        type: Literal[
            "low_value_transaction", "transaction_risk_analysis", "unknown"
        ]
        """
        The specific exemption claimed for this authorization.
        """

    class CreateParamsVerificationDataThreeDSecure(TypedDict):
        result: Literal[
            "attempt_acknowledged", "authenticated", "failed", "required"
        ]
        """
        The outcome of the 3D Secure authentication request.
        """

    class ExpireParams(TypedDict):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """

    class FinalizeAmountParams(TypedDict):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        final_amount: int
        """
        The final authorization amount that will be captured by the merchant. This amount is in the authorization currency and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        fleet: NotRequired["AuthorizationService.FinalizeAmountParamsFleet"]
        """
        Fleet-specific information for authorizations using Fleet cards.
        """
        fuel: NotRequired["AuthorizationService.FinalizeAmountParamsFuel"]
        """
        Information about fuel that was purchased with this transaction.
        """

    class FinalizeAmountParamsFleet(TypedDict):
        cardholder_prompt_data: NotRequired[
            "AuthorizationService.FinalizeAmountParamsFleetCardholderPromptData"
        ]
        """
        Answers to prompts presented to the cardholder at the point of sale. Prompted fields vary depending on the configuration of your physical fleet cards. Typical points of sale support only numeric entry.
        """
        purchase_type: NotRequired[
            Literal[
                "fuel_and_non_fuel_purchase",
                "fuel_purchase",
                "non_fuel_purchase",
            ]
        ]
        """
        The type of purchase. One of `fuel_purchase`, `non_fuel_purchase`, or `fuel_and_non_fuel_purchase`.
        """
        reported_breakdown: NotRequired[
            "AuthorizationService.FinalizeAmountParamsFleetReportedBreakdown"
        ]
        """
        More information about the total amount. This information is not guaranteed to be accurate as some merchants may provide unreliable data.
        """
        service_type: NotRequired[
            Literal["full_service", "non_fuel_transaction", "self_service"]
        ]
        """
        The type of fuel service. One of `non_fuel_transaction`, `full_service`, or `self_service`.
        """

    class FinalizeAmountParamsFleetCardholderPromptData(TypedDict):
        driver_id: NotRequired[str]
        """
        Driver ID.
        """
        odometer: NotRequired[int]
        """
        Odometer reading.
        """
        unspecified_id: NotRequired[str]
        """
        An alphanumeric ID. This field is used when a vehicle ID, driver ID, or generic ID is entered by the cardholder, but the merchant or card network did not specify the prompt type.
        """
        user_id: NotRequired[str]
        """
        User ID.
        """
        vehicle_number: NotRequired[str]
        """
        Vehicle number.
        """

    class FinalizeAmountParamsFleetReportedBreakdown(TypedDict):
        fuel: NotRequired[
            "AuthorizationService.FinalizeAmountParamsFleetReportedBreakdownFuel"
        ]
        """
        Breakdown of fuel portion of the purchase.
        """
        non_fuel: NotRequired[
            "AuthorizationService.FinalizeAmountParamsFleetReportedBreakdownNonFuel"
        ]
        """
        Breakdown of non-fuel portion of the purchase.
        """
        tax: NotRequired[
            "AuthorizationService.FinalizeAmountParamsFleetReportedBreakdownTax"
        ]
        """
        Information about tax included in this transaction.
        """

    class FinalizeAmountParamsFleetReportedBreakdownFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross fuel amount that should equal Fuel Volume multipled by Fuel Unit Cost, inclusive of taxes.
        """

    class FinalizeAmountParamsFleetReportedBreakdownNonFuel(TypedDict):
        gross_amount_decimal: NotRequired[str]
        """
        Gross non-fuel amount that should equal the sum of the line items, inclusive of taxes.
        """

    class FinalizeAmountParamsFleetReportedBreakdownTax(TypedDict):
        local_amount_decimal: NotRequired[str]
        """
        Amount of state or provincial Sales Tax included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """
        national_amount_decimal: NotRequired[str]
        """
        Amount of national Sales Tax or VAT included in the transaction amount. Null if not reported by merchant or not subject to tax.
        """

    class FinalizeAmountParamsFuel(TypedDict):
        industry_product_code: NotRequired[str]
        """
        [Conexxus Payment System Product Code](https://www.conexxus.org/conexxus-payment-system-product-codes) identifying the primary fuel product purchased.
        """
        quantity_decimal: NotRequired[str]
        """
        The quantity of `unit`s of fuel that was dispensed, represented as a decimal string with at most 12 decimal places.
        """
        type: NotRequired[
            Literal[
                "diesel",
                "other",
                "unleaded_plus",
                "unleaded_regular",
                "unleaded_super",
            ]
        ]
        """
        The type of fuel that was purchased. One of `diesel`, `unleaded_plus`, `unleaded_regular`, `unleaded_super`, or `other`.
        """
        unit: NotRequired[
            Literal[
                "charging_minute",
                "imperial_gallon",
                "kilogram",
                "kilowatt_hour",
                "liter",
                "other",
                "pound",
                "us_gallon",
            ]
        ]
        """
        The units for `quantity_decimal`. One of `charging_minute`, `imperial_gallon`, `kilogram`, `kilowatt_hour`, `liter`, `pound`, `us_gallon`, or `other`.
        """
        unit_cost_decimal: NotRequired[str]
        """
        The cost in cents per each unit of fuel, represented as a decimal string with at most 12 decimal places.
        """

    class IncrementParams(TypedDict):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        increment_amount: int
        """
        The amount to increment the authorization by. This amount is in the authorization currency and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """
        is_amount_controllable: NotRequired[bool]
        """
        If set `true`, you may provide [amount](https://stripe.com/docs/api/issuing/authorizations/approve#approve_issuing_authorization-amount) to control how much to hold for the authorization.
        """

    class RespondParams(TypedDict):
        confirmed: bool
        """
        Whether to simulate the user confirming that the transaction was legitimate (true) or telling Stripe that it was fraudulent (false).
        """
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """

    class ReverseParams(TypedDict):
        expand: NotRequired[List[str]]
        """
        Specifies which fields in the response should be expanded.
        """
        reverse_amount: NotRequired[int]
        """
        The amount to reverse from the authorization. If not provided, the full amount of the authorization will be reversed. This amount is in the authorization currency and in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal).
        """

    def create(
        self,
        params: "AuthorizationService.CreateParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Create a test-mode authorization.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations",
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def create_async(
        self,
        params: "AuthorizationService.CreateParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Create a test-mode authorization.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations",
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def capture(
        self,
        authorization: str,
        params: "AuthorizationService.CaptureParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Capture a test-mode authorization.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/capture".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def capture_async(
        self,
        authorization: str,
        params: "AuthorizationService.CaptureParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Capture a test-mode authorization.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/capture".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def expire(
        self,
        authorization: str,
        params: "AuthorizationService.ExpireParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Expire a test-mode Authorization.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/expire".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def expire_async(
        self,
        authorization: str,
        params: "AuthorizationService.ExpireParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Expire a test-mode Authorization.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/expire".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def finalize_amount(
        self,
        authorization: str,
        params: "AuthorizationService.FinalizeAmountParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Finalize the amount on an Authorization prior to capture, when the initial authorization was for an estimated amount.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def finalize_amount_async(
        self,
        authorization: str,
        params: "AuthorizationService.FinalizeAmountParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Finalize the amount on an Authorization prior to capture, when the initial authorization was for an estimated amount.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def respond(
        self,
        authorization: str,
        params: "AuthorizationService.RespondParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Respond to a fraud challenge on a testmode Issuing authorization, simulating either a confirmation of fraud or a correction of legitimacy.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def respond_async(
        self,
        authorization: str,
        params: "AuthorizationService.RespondParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Respond to a fraud challenge on a testmode Issuing authorization, simulating either a confirmation of fraud or a correction of legitimacy.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def increment(
        self,
        authorization: str,
        params: "AuthorizationService.IncrementParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Increment a test-mode Authorization.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/increment".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def increment_async(
        self,
        authorization: str,
        params: "AuthorizationService.IncrementParams",
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Increment a test-mode Authorization.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/increment".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    def reverse(
        self,
        authorization: str,
        params: "AuthorizationService.ReverseParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Reverse a test-mode Authorization.
        """
        return cast(
            Authorization,
            self._request(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/reverse".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )

    async def reverse_async(
        self,
        authorization: str,
        params: "AuthorizationService.ReverseParams" = {},
        options: RequestOptions = {},
    ) -> Authorization:
        """
        Reverse a test-mode Authorization.
        """
        return cast(
            Authorization,
            await self._request_async(
                "post",
                "/v1/test_helpers/issuing/authorizations/{authorization}/reverse".format(
                    authorization=sanitize_id(authorization),
                ),
                base_address="api",
                params=params,
                options=options,
            ),
        )
