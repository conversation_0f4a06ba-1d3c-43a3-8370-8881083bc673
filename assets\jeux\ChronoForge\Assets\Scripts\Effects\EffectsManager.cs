using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using ChronoForge.Audio;

namespace ChronoForge.Effects
{
    /// <summary>
    /// Gestionnaire d'effets visuels pour ChronoForge
    /// </summary>
    public class EffectsManager : MonoBehaviour
    {
        [Header("Effect Prefabs")]
        public GameObject hitEffectPrefab;
        public GameObject explosionEffectPrefab;
        public GameObject healEffectPrefab;
        public GameObject levelUpEffectPrefab;
        public GameObject dashEffectPrefab;
        public GameObject criticalHitEffectPrefab;
        
        [Header("Particle Systems")]
        public ParticleSystem bloodParticles;
        public ParticleSystem sparkParticles;
        public ParticleSystem smokeParticles;
        public ParticleSystem magicParticles;
        
        [Header("Screen Effects")]
        public GameObject screenFlashPrefab;
        public GameObject screenShakePrefab;
        public Camera mainCamera;
        
        [Header("Pool Settings")]
        public int effectPoolSize = 50;
        public float effectLifetime = 5f;
        
        // Effect pools
        private Dictionary<string, Queue<GameObject>> effectPools = new Dictionary<string, Queue<GameObject>>();
        private List<GameObject> activeEffects = new List<GameObject>();
        
        // Screen effects
        private bool isScreenShaking = false;
        private Vector3 originalCameraPosition;
        
        // Singleton
        public static EffectsManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                InitializeEffectsManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupCamera();
        }
        
        private void Update()
        {
            UpdateActiveEffects();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeEffectsManager()
        {
            // Create default effect prefabs if not assigned
            CreateDefaultEffects();
            
            // Initialize effect pools
            InitializeEffectPools();
            
            Debug.Log("✨ EffectsManager initialized");
        }
        
        private void CreateDefaultEffects()
        {
            // Create default hit effect
            if (hitEffectPrefab == null)
            {
                hitEffectPrefab = CreateDefaultHitEffect();
            }
            
            // Create default explosion effect
            if (explosionEffectPrefab == null)
            {
                explosionEffectPrefab = CreateDefaultExplosionEffect();
            }
            
            // Create default heal effect
            if (healEffectPrefab == null)
            {
                healEffectPrefab = CreateDefaultHealEffect();
            }
            
            // Create default level up effect
            if (levelUpEffectPrefab == null)
            {
                levelUpEffectPrefab = CreateDefaultLevelUpEffect();
            }
            
            // Create default dash effect
            if (dashEffectPrefab == null)
            {
                dashEffectPrefab = CreateDefaultDashEffect();
            }
            
            // Create default critical hit effect
            if (criticalHitEffectPrefab == null)
            {
                criticalHitEffectPrefab = CreateDefaultCriticalHitEffect();
            }
        }
        
        private GameObject CreateDefaultHitEffect()
        {
            GameObject effect = new GameObject("HitEffect");
            
            // Add particle system
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 0.5f;
            main.startSpeed = 5f;
            main.startSize = 0.1f;
            main.startColor = Color.red;
            main.maxParticles = 20;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 20)
            });
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 0.5f;
            
            // Add auto-destroy component
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private GameObject CreateDefaultExplosionEffect()
        {
            GameObject effect = new GameObject("ExplosionEffect");
            
            // Add particle system
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1f;
            main.startSpeed = 8f;
            main.startSize = 0.2f;
            main.startColor = Color.yellow;
            main.maxParticles = 50;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 50)
            });
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 1f;
            
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private GameObject CreateDefaultHealEffect()
        {
            GameObject effect = new GameObject("HealEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 2f;
            main.startSpeed = 2f;
            main.startSize = 0.1f;
            main.startColor = Color.green;
            main.maxParticles = 30;
            
            var emission = particles.emission;
            emission.rateOverTime = 15;
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Circle;
            shape.radius = 1f;
            
            var velocityOverLifetime = particles.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.y = new ParticleSystem.MinMaxCurve(3f);
            
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private GameObject CreateDefaultLevelUpEffect()
        {
            GameObject effect = new GameObject("LevelUpEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 3f;
            main.startSpeed = 5f;
            main.startSize = 0.3f;
            main.startColor = Color.cyan;
            main.maxParticles = 100;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 100)
            });
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 2f;
            
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private GameObject CreateDefaultDashEffect()
        {
            GameObject effect = new GameObject("DashEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 0.3f;
            main.startSpeed = 10f;
            main.startSize = 0.05f;
            main.startColor = Color.white;
            main.maxParticles = 50;
            
            var emission = particles.emission;
            emission.rateOverTime = 200;
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Box;
            shape.scale = new Vector3(0.5f, 0.5f, 0.1f);
            
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private GameObject CreateDefaultCriticalHitEffect()
        {
            GameObject effect = new GameObject("CriticalHitEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1f;
            main.startSpeed = 15f;
            main.startSize = 0.2f;
            main.startColor = Color.yellow;
            main.maxParticles = 30;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 30)
            });
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Cone;
            shape.angle = 45f;
            
            effect.AddComponent<AutoDestroyEffect>();
            
            return effect;
        }
        
        private void InitializeEffectPools()
        {
            // Initialize pools for each effect type
            InitializePool("hit", hitEffectPrefab);
            InitializePool("explosion", explosionEffectPrefab);
            InitializePool("heal", healEffectPrefab);
            InitializePool("levelup", levelUpEffectPrefab);
            InitializePool("dash", dashEffectPrefab);
            InitializePool("critical", criticalHitEffectPrefab);
        }
        
        private void InitializePool(string poolName, GameObject prefab)
        {
            if (prefab == null) return;
            
            Queue<GameObject> pool = new Queue<GameObject>();
            
            for (int i = 0; i < effectPoolSize; i++)
            {
                GameObject obj = Instantiate(prefab);
                obj.SetActive(false);
                obj.transform.SetParent(transform);
                pool.Enqueue(obj);
            }
            
            effectPools[poolName] = pool;
        }
        
        private void SetupCamera()
        {
            if (mainCamera == null)
            {
                mainCamera = Camera.main;
                if (mainCamera == null)
                    mainCamera = FindObjectOfType<Camera>();
            }
            
            if (mainCamera != null)
            {
                originalCameraPosition = mainCamera.transform.position;
            }
        }
        
        #endregion
        
        #region Effect Spawning
        
        public void PlayEffect(string effectName, Vector3 position, Quaternion rotation = default)
        {
            GameObject effect = GetPooledEffect(effectName);
            if (effect == null) return;
            
            // Position and activate effect
            effect.transform.position = position;
            effect.transform.rotation = rotation == default ? Quaternion.identity : rotation;
            effect.SetActive(true);
            
            // Add to active effects
            activeEffects.Add(effect);
            
            // Auto-return to pool after lifetime
            StartCoroutine(ReturnEffectToPool(effect, effectName, effectLifetime));
        }
        
        public void PlayHitEffect(Vector3 position, Color color = default)
        {
            GameObject effect = GetPooledEffect("hit");
            if (effect == null) return;
            
            effect.transform.position = position;
            
            // Customize color if specified
            if (color != default)
            {
                ParticleSystem particles = effect.GetComponent<ParticleSystem>();
                if (particles != null)
                {
                    var main = particles.main;
                    main.startColor = color;
                }
            }
            
            effect.SetActive(true);
            activeEffects.Add(effect);
            
            StartCoroutine(ReturnEffectToPool(effect, "hit", 1f));
        }
        
        public void PlayExplosionEffect(Vector3 position, float scale = 1f)
        {
            GameObject effect = GetPooledEffect("explosion");
            if (effect == null) return;
            
            effect.transform.position = position;
            effect.transform.localScale = Vector3.one * scale;
            effect.SetActive(true);
            activeEffects.Add(effect);
            
            StartCoroutine(ReturnEffectToPool(effect, "explosion", 2f));
        }
        
        public void PlayHealEffect(Vector3 position)
        {
            PlayEffect("heal", position);
        }
        
        public void PlayLevelUpEffect(Vector3 position)
        {
            PlayEffect("levelup", position);
        }
        
        public void PlayDashEffect(Vector3 position, Vector3 direction)
        {
            GameObject effect = GetPooledEffect("dash");
            if (effect == null) return;
            
            effect.transform.position = position;
            effect.transform.rotation = Quaternion.LookRotation(direction);
            effect.SetActive(true);
            activeEffects.Add(effect);
            
            StartCoroutine(ReturnEffectToPool(effect, "dash", 0.5f));
        }
        
        public void PlayCriticalHitEffect(Vector3 position, Vector3 direction)
        {
            GameObject effect = GetPooledEffect("critical");
            if (effect == null) return;
            
            effect.transform.position = position;
            effect.transform.rotation = Quaternion.LookRotation(direction);
            effect.SetActive(true);
            activeEffects.Add(effect);
            
            StartCoroutine(ReturnEffectToPool(effect, "critical", 1.5f));
        }
        
        #endregion
        
        #region Screen Effects
        
        public void ScreenFlash(Color color, float duration = 0.1f)
        {
            StartCoroutine(ScreenFlashCoroutine(color, duration));
        }
        
        public void ScreenShake(float intensity = 1f, float duration = 0.3f)
        {
            if (!isScreenShaking)
            {
                StartCoroutine(ScreenShakeCoroutine(intensity, duration));
            }
        }
        
        private IEnumerator ScreenFlashCoroutine(Color color, float duration)
        {
            // Create flash overlay
            GameObject flashObj = new GameObject("ScreenFlash");
            Canvas canvas = flashObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 1000;
            
            UnityEngine.UI.Image flashImage = flashObj.AddComponent<UnityEngine.UI.Image>();
            flashImage.color = color;
            flashImage.raycastTarget = false;
            
            // Fade out
            float elapsed = 0f;
            Color startColor = color;
            Color endColor = new Color(color.r, color.g, color.b, 0f);
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                flashImage.color = Color.Lerp(startColor, endColor, progress);
                yield return null;
            }
            
            Destroy(flashObj);
        }
        
        private IEnumerator ScreenShakeCoroutine(float intensity, float duration)
        {
            if (mainCamera == null) yield break;
            
            isScreenShaking = true;
            Vector3 originalPos = mainCamera.transform.position;
            
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                
                float progress = 1f - (elapsed / duration);
                float currentIntensity = intensity * progress;
                
                Vector3 randomOffset = Random.insideUnitSphere * currentIntensity;
                randomOffset.z = 0f; // Keep camera on same Z plane
                
                mainCamera.transform.position = originalPos + randomOffset;
                
                yield return null;
            }
            
            mainCamera.transform.position = originalPos;
            isScreenShaking = false;
        }
        
        #endregion
        
        #region Pool Management
        
        private GameObject GetPooledEffect(string effectName)
        {
            if (!effectPools.ContainsKey(effectName) || effectPools[effectName].Count == 0)
            {
                Debug.LogWarning($"No available effects in pool: {effectName}");
                return null;
            }
            
            return effectPools[effectName].Dequeue();
        }
        
        private IEnumerator ReturnEffectToPool(GameObject effect, string poolName, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (effect != null)
            {
                effect.SetActive(false);
                activeEffects.Remove(effect);
                
                if (effectPools.ContainsKey(poolName))
                {
                    effectPools[poolName].Enqueue(effect);
                }
            }
        }
        
        private void UpdateActiveEffects()
        {
            // Clean up any null references
            activeEffects.RemoveAll(effect => effect == null);
        }
        
        #endregion
        
        #region Audio Integration
        
        public void PlayEffectWithSound(string effectName, Vector3 position, string soundName = "")
        {
            // Play visual effect
            PlayEffect(effectName, position);
            
            // Play sound effect
            if (!string.IsNullOrEmpty(soundName) && AudioManager.Instance != null)
            {
                AudioManager.Instance.PlaySFX(soundName, 1f, 1f, position);
            }
        }
        
        #endregion
        
        #region Public Utility Methods
        
        public void ClearAllEffects()
        {
            foreach (GameObject effect in activeEffects)
            {
                if (effect != null)
                {
                    effect.SetActive(false);
                }
            }
            
            activeEffects.Clear();
        }
        
        public void SetEffectQuality(int quality)
        {
            // Adjust particle counts based on quality setting
            float qualityMultiplier = quality / 3f; // Assuming 0-3 quality scale
            
            foreach (var pool in effectPools.Values)
            {
                foreach (GameObject effect in pool)
                {
                    ParticleSystem particles = effect.GetComponent<ParticleSystem>();
                    if (particles != null)
                    {
                        var main = particles.main;
                        main.maxParticles = Mathf.RoundToInt(main.maxParticles * qualityMultiplier);
                    }
                }
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Composant pour détruire automatiquement les effets
    /// </summary>
    public class AutoDestroyEffect : MonoBehaviour
    {
        public float lifetime = 5f;
        
        private void OnEnable()
        {
            StartCoroutine(DestroyAfterTime());
        }
        
        private IEnumerator DestroyAfterTime()
        {
            yield return new WaitForSeconds(lifetime);
            
            if (gameObject != null)
            {
                gameObject.SetActive(false);
            }
        }
    }
}
