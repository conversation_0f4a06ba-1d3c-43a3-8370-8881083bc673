# Pull Request - API2CSV

## 📝 Description

Décrivez brièvement les changements apportés dans cette PR.

## 🎯 Type de changement

- [ ] 🐛 Correction de bug
- [ ] ✨ Nouvelle fonctionnalité
- [ ] 💄 Amélioration UI/UX
- [ ] ⚡ Amélioration de performance
- [ ] 📖 Mise à jour documentation
- [ ] 🧪 Ajout de tests
- [ ] 🔧 Refactoring
- [ ] 🔒 Sécurité

## 🧪 Tests effectués

Décrivez les tests que vous avez effectués :

- [ ] Tests sur Chrome
- [ ] Tests sur Firefox
- [ ] Tests sur Safari
- [ ] Tests sur Edge
- [ ] Tests avec petits fichiers JSON (< 1MB)
- [ ] Tests avec gros fichiers JSON (> 10MB)
- [ ] Tests avec JSON complexes (objets imbriqués)
- [ ] Tests d'accessibilité

## 📋 Checklist

- [ ] Mon code suit les standards du projet
- [ ] J'ai effectué une auto-review de mon code
- [ ] J'ai commenté mon code, particulièrement les parties complexes
- [ ] J'ai mis à jour la documentation si nécessaire
- [ ] Mes changements ne génèrent pas de nouveaux warnings
- [ ] J'ai testé que ma fonctionnalité fonctionne comme attendu
- [ ] Les tests existants passent toujours

## 📸 Captures d'écran (si applicable)

Ajoutez des captures d'écran pour montrer les changements visuels.

## 🔗 Issues liées

Fixes #(numéro de l'issue)

## 📝 Notes supplémentaires

Ajoutez toute information supplémentaire utile pour les reviewers.
