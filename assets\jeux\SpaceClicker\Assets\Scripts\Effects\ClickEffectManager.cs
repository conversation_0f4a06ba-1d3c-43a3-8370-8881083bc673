using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace SpaceClicker.Effects
{
    /// <summary>
    /// Gestionnaire d'effets de clic pour SpaceClicker
    /// </summary>
    public class ClickEffectManager : MonoBehaviour
    {
        [Header("Click Effects")]
        public GameObject clickEffectPrefab;
        public GameObject criticalClickEffectPrefab;
        public GameObject comboEffectPrefab;
        
        [Header("Particle Effects")]
        public ParticleSystem clickParticles;
        public ParticleSystem criticalParticles;
        public ParticleSystem comboParticles;
        
        [Header("Settings")]
        public int maxEffects = 20;
        public float effectLifetime = 2f;
        public float criticalChance = 0.05f;
        public int comboThreshold = 10;
        
        [Header("Audio")]
        public AudioClip clickSound;
        public AudioClip criticalSound;
        public AudioClip comboSound;
        
        // Effect pools
        private Queue<GameObject> clickEffectPool = new Queue<GameObject>();
        private Queue<GameObject> criticalEffectPool = new Queue<GameObject>();
        private Queue<GameObject> comboEffectPool = new Queue<GameObject>();
        
        // Active effects
        private List<GameObject> activeEffects = new List<GameObject>();
        
        // Click tracking
        private int consecutiveClicks = 0;
        private float lastClickTime = 0f;
        private float comboTimeWindow = 1f;
        
        // Components
        private AudioSource audioSource;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeEffectManager();
        }
        
        private void Start()
        {
            SetupAudio();
        }
        
        private void Update()
        {
            UpdateComboTimer();
            CleanupEffects();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeEffectManager()
        {
            CreateDefaultEffects();
            InitializeEffectPools();
            
            Debug.Log("✨ ClickEffectManager initialized");
        }
        
        private void CreateDefaultEffects()
        {
            if (clickEffectPrefab == null)
            {
                clickEffectPrefab = CreateDefaultClickEffect();
            }
            
            if (criticalClickEffectPrefab == null)
            {
                criticalClickEffectPrefab = CreateDefaultCriticalEffect();
            }
            
            if (comboEffectPrefab == null)
            {
                comboEffectPrefab = CreateDefaultComboEffect();
            }
        }
        
        private GameObject CreateDefaultClickEffect()
        {
            GameObject effect = new GameObject("ClickEffect");
            
            // Add particle system
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 0.5f;
            main.startSpeed = 2f;
            main.startSize = 0.1f;
            main.startColor = Color.cyan;
            main.maxParticles = 10;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 10)
            });
            
            return effect;
        }
        
        private GameObject CreateDefaultCriticalEffect()
        {
            GameObject effect = new GameObject("CriticalClickEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1f;
            main.startSpeed = 5f;
            main.startSize = 0.2f;
            main.startColor = Color.yellow;
            main.maxParticles = 20;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 20)
            });
            
            return effect;
        }
        
        private GameObject CreateDefaultComboEffect()
        {
            GameObject effect = new GameObject("ComboEffect");
            
            ParticleSystem particles = effect.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1.5f;
            main.startSpeed = 8f;
            main.startSize = 0.3f;
            main.startColor = Color.magenta;
            main.maxParticles = 30;
            
            var emission = particles.emission;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0.0f, 30)
            });
            
            return effect;
        }
        
        private void InitializeEffectPools()
        {
            // Create pools
            for (int i = 0; i < maxEffects / 3; i++)
            {
                GameObject clickEffect = Instantiate(clickEffectPrefab);
                clickEffect.SetActive(false);
                clickEffectPool.Enqueue(clickEffect);
                
                GameObject criticalEffect = Instantiate(criticalClickEffectPrefab);
                criticalEffect.SetActive(false);
                criticalEffectPool.Enqueue(criticalEffect);
                
                GameObject comboEffect = Instantiate(comboEffectPrefab);
                comboEffect.SetActive(false);
                comboEffectPool.Enqueue(comboEffect);
            }
        }
        
        private void SetupAudio()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        #endregion
        
        #region Click Effects
        
        public void PlayClickEffect(Vector3 position, bool isCritical = false)
        {
            // Update click tracking
            UpdateClickTracking();
            
            // Determine effect type
            bool shouldBeCritical = isCritical || (Random.value < criticalChance);
            bool isCombo = consecutiveClicks >= comboThreshold;
            
            if (isCombo)
            {
                PlayComboEffect(position);
            }
            else if (shouldBeCritical)
            {
                PlayCriticalEffect(position);
            }
            else
            {
                PlayNormalClickEffect(position);
            }
        }
        
        private void PlayNormalClickEffect(Vector3 position)
        {
            GameObject effect = GetPooledEffect(clickEffectPool);
            if (effect != null)
            {
                effect.transform.position = position;
                effect.SetActive(true);
                activeEffects.Add(effect);
                
                StartCoroutine(ReturnEffectToPool(effect, clickEffectPool, effectLifetime));
            }
            
            PlaySound(clickSound);
        }
        
        private void PlayCriticalEffect(Vector3 position)
        {
            GameObject effect = GetPooledEffect(criticalEffectPool);
            if (effect != null)
            {
                effect.transform.position = position;
                effect.SetActive(true);
                activeEffects.Add(effect);
                
                StartCoroutine(ReturnEffectToPool(effect, criticalEffectPool, effectLifetime * 1.5f));
            }
            
            PlaySound(criticalSound);
            
            Debug.Log("💥 Critical click!");
        }
        
        private void PlayComboEffect(Vector3 position)
        {
            GameObject effect = GetPooledEffect(comboEffectPool);
            if (effect != null)
            {
                effect.transform.position = position;
                effect.SetActive(true);
                activeEffects.Add(effect);
                
                StartCoroutine(ReturnEffectToPool(effect, comboEffectPool, effectLifetime * 2f));
            }
            
            PlaySound(comboSound);
            
            Debug.Log($"🔥 Combo x{consecutiveClicks}!");
        }
        
        #endregion
        
        #region Click Tracking
        
        private void UpdateClickTracking()
        {
            float currentTime = Time.time;
            
            if (currentTime - lastClickTime <= comboTimeWindow)
            {
                consecutiveClicks++;
            }
            else
            {
                consecutiveClicks = 1;
            }
            
            lastClickTime = currentTime;
        }
        
        private void UpdateComboTimer()
        {
            if (consecutiveClicks > 0 && Time.time - lastClickTime > comboTimeWindow)
            {
                consecutiveClicks = 0;
            }
        }
        
        #endregion
        
        #region Effect Management
        
        private GameObject GetPooledEffect(Queue<GameObject> pool)
        {
            if (pool.Count > 0)
            {
                return pool.Dequeue();
            }
            return null;
        }
        
        private IEnumerator ReturnEffectToPool(GameObject effect, Queue<GameObject> pool, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (effect != null)
            {
                effect.SetActive(false);
                activeEffects.Remove(effect);
                pool.Enqueue(effect);
            }
        }
        
        private void CleanupEffects()
        {
            // Remove null references
            activeEffects.RemoveAll(effect => effect == null);
        }
        
        #endregion
        
        #region Audio
        
        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetCriticalChance(float chance)
        {
            criticalChance = Mathf.Clamp01(chance);
        }
        
        public void SetComboThreshold(int threshold)
        {
            comboThreshold = Mathf.Max(1, threshold);
        }
        
        public int GetCurrentCombo()
        {
            return consecutiveClicks;
        }
        
        public bool IsInCombo()
        {
            return consecutiveClicks >= comboThreshold;
        }
        
        public void ResetCombo()
        {
            consecutiveClicks = 0;
        }
        
        #endregion
    }
}
