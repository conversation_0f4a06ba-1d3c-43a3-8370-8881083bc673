# Cahier des Charges Technique - Space Clicker

**Projet** : <PERSON><PERSON>/Idle - Exploration Spatiale  
**Version** : 1.0  
**Date** : 24 juin 2025  

## 1. Architecture Technique

### 1.1 Stack Technologique

| Composant | Technologie | Justification |
|-----------|-------------|---------------|
| **Engine** | Unity 2023.3 LTS (C#) | Export multiplateforme, gestion animations 2D/3D |
| **Backend** | Firebase | Sauvegarde cloud, analytics, notifications |
| **Base de données** | Firestore + PlayerPrefs | Cloud sync + stockage local |
| **Monétisation** | Unity Ads + Unity IAP | Intégration native Unity |
| **Notifications** | Firebase Cloud Messaging | Push notifications cross-platform |
| **Analytics** | Unity Analytics + Firebase | Suivi comportement utilisateur |

### 1.2 Architecture Système

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Unity Client  │◄──►│   Firebase      │◄──►│  Unity Services │
│                 │    │                 │    │                 │
│ • Game Logic    │    │ • Firestore     │    │ • Analytics     │
│ • UI/UX         │    │ • Auth          │    │ • Ads           │
│ • Local Save    │    │ • FCM           │    │ • IAP           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.3 Structure de Données

#### Ressources Principales
```csharp
[System.Serializable]
public class GameResources
{
    public BigInteger energy;           // Énergie (production base)
    public BigInteger minerals;        // Minéraux rares (upgrades)
    public BigInteger researchData;    // Données recherche (technologies)
    public BigInteger spaceCurrency;   // Monnaie premium (IAP)
}
```

#### Système d'Upgrades
```csharp
[System.Serializable]
public class UpgradeData
{
    public string upgradeId;
    public string name;
    public string description;
    public BigInteger baseCost;
    public float costMultiplier;
    public int currentLevel;
    public UpgradeType type;
    public float effectValue;
}
```

## 2. Modules Techniques

### 2.1 Gestionnaire de Ressources
```csharp
public class ResourceManager : MonoBehaviour
{
    public static ResourceManager Instance;
    
    [Header("Production Settings")]
    public float baseProductionRate = 1.0f;
    public float idleMultiplier = 1.0f;
    
    private Coroutine idleProductionCoroutine;
    
    // Méthodes principales
    public void AddResource(ResourceType type, BigInteger amount);
    public bool CanAfford(ResourceType type, BigInteger cost);
    public void SpendResource(ResourceType type, BigInteger amount);
    public BigInteger GetProductionRate(ResourceType type);
}
```

### 2.2 Système de Sauvegarde
```csharp
public class SaveSystem : MonoBehaviour
{
    [Header("Save Settings")]
    public float autoSaveInterval = 30f;
    public bool useCloudSave = true;
    
    // Sauvegarde locale
    public void SaveToLocal();
    public GameData LoadFromLocal();
    
    // Sauvegarde cloud
    public async Task SaveToCloud();
    public async Task<GameData> LoadFromCloud();
    
    // Gestion conflits
    public GameData ResolveConflict(GameData local, GameData cloud);
}
```

### 2.3 Gestionnaire de Monétisation
```csharp
public class MonetizationManager : MonoBehaviour
{
    [Header("Ad Settings")]
    public float rewardedAdCooldown = 300f; // 5 minutes
    public float productionBoostDuration = 600f; // 10 minutes
    
    // Unity Ads
    public void ShowRewardedAd(System.Action<bool> callback);
    public void ShowInterstitialAd();
    
    // Unity IAP
    public void PurchaseSpaceCurrency(string productId);
    public void PurchaseBooster(string productId);
}
```

## 3. Spécifications Techniques Détaillées

### 3.1 Performance et Optimisation

| Aspect | Spécification | Implémentation |
|--------|---------------|----------------|
| **FPS Target** | 60 FPS mobile | Profiler Unity, optimisation draw calls |
| **Mémoire** | < 200MB RAM | Object pooling, texture compression |
| **Batterie** | Optimisé idle | Réduction fréquence updates en arrière-plan |
| **Stockage** | < 100MB install | Compression assets, streaming |

### 3.2 Système de Timer Idle
```csharp
public class IdleTimer : MonoBehaviour
{
    private DateTime lastSaveTime;
    private float offlineProductionCap = 3600f; // 1 heure max
    
    public void CalculateOfflineProgress()
    {
        if (PlayerPrefs.HasKey("LastSaveTime"))
        {
            string lastSave = PlayerPrefs.GetString("LastSaveTime");
            DateTime lastTime = DateTime.Parse(lastSave);
            TimeSpan offlineTime = DateTime.Now - lastTime;
            
            float offlineSeconds = Mathf.Min((float)offlineTime.TotalSeconds, offlineProductionCap);
            BigInteger offlineProduction = CalculateOfflineRewards(offlineSeconds);
            
            ShowOfflineRewardsPopup(offlineProduction);
        }
    }
}
```

### 3.3 Système de Notifications
```csharp
public class NotificationManager : MonoBehaviour
{
    [Header("Notification Settings")]
    public float resourceReadyNotificationDelay = 3600f; // 1 heure
    public float missionCompleteNotificationDelay = 1800f; // 30 minutes
    
    public void ScheduleResourceNotification()
    {
        var notification = new AndroidNotification()
        {
            Title = "Ressources Prêtes!",
            Text = "Vos extracteurs ont collecté des ressources",
            FireTime = System.DateTime.Now.AddSeconds(resourceReadyNotificationDelay)
        };
        
        AndroidNotificationCenter.SendNotification(notification, "resource_channel");
    }
}
```

## 4. Configuration Firebase

### 4.1 Structure Firestore
```
users/{userId}/
├── gameData/
│   ├── resources/
│   ├── upgrades/
│   ├── buildings/
│   └── settings/
├── analytics/
│   ├── sessions/
│   └── purchases/
└── metadata/
    ├── lastSave
    ├── version
    └── platform
```

### 4.2 Règles de Sécurité Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 5. Pipeline de Développement

### 5.1 Environnements
- **Development** : Unity Editor + Firebase Emulator
- **Staging** : Firebase Test Project + TestFlight/Internal Testing
- **Production** : Firebase Production + App Stores

### 5.2 Build Configuration
```csharp
// BuildSettings.cs
public static class BuildSettings
{
    #if DEVELOPMENT_BUILD
    public const string FIREBASE_CONFIG = "firebase-dev";
    public const bool ENABLE_CHEATS = true;
    #elif STAGING
    public const string FIREBASE_CONFIG = "firebase-staging";
    public const bool ENABLE_CHEATS = false;
    #else
    public const string FIREBASE_CONFIG = "firebase-prod";
    public const bool ENABLE_CHEATS = false;
    #endif
}
```

### 5.3 Tests et Validation
- **Unit Tests** : NUnit pour logique métier
- **Integration Tests** : Firebase SDK, Unity Services
- **Performance Tests** : Unity Profiler, Memory Profiler
- **Device Testing** : Android (API 21+), iOS (12.0+)

## 6. Sécurité et Anti-Triche

### 6.1 Validation Côté Serveur
```csharp
public class AntiCheatValidator
{
    public bool ValidateResourceGain(BigInteger amount, float timeElapsed)
    {
        BigInteger maxPossible = CalculateMaxProduction(timeElapsed);
        return amount <= maxPossible * 1.1f; // 10% tolérance
    }
    
    public bool ValidateUpgradePurchase(string upgradeId, BigInteger cost)
    {
        UpgradeData upgrade = GetUpgradeData(upgradeId);
        BigInteger expectedCost = CalculateUpgradeCost(upgrade);
        return cost >= expectedCost * 0.9f; // 10% tolérance
    }
}
```

### 6.2 Chiffrement des Sauvegardes
```csharp
public static class SaveEncryption
{
    private const string ENCRYPTION_KEY = "SpaceClicker2025";
    
    public static string EncryptSaveData(string jsonData)
    {
        // Implémentation AES encryption
        return EncryptAES(jsonData, ENCRYPTION_KEY);
    }
    
    public static string DecryptSaveData(string encryptedData)
    {
        return DecryptAES(encryptedData, ENCRYPTION_KEY);
    }
}
```

## 7. Monitoring et Analytics

### 7.1 Événements Clés à Tracker
- **Gameplay** : Clics, upgrades achetés, temps de session
- **Monétisation** : Vues pub, achats IAP, conversions
- **Rétention** : Sessions quotidiennes, progression
- **Performance** : Crashes, temps de chargement, FPS

### 7.2 KPIs Principaux
- **DAU/MAU** : Utilisateurs actifs
- **ARPU** : Revenu par utilisateur
- **Retention D1/D7/D30** : Rétention utilisateurs
- **Conversion Rate** : Taux conversion IAP

## 8. Roadmap de Développement

### Phase 1 : Prototype (Semaine 1)
- **Objectif** : Prouver le concept de base
- **Livrables** :
  - Collecte manuelle de ressources (clic)
  - Interface basique avec compteurs
  - Premier système d'upgrade simple
  - Sauvegarde locale PlayerPrefs

### Phase 2 : Idle Mechanics (Semaine 2)
- **Objectif** : Mécaniques de production automatique
- **Livrables** :
  - Production automatique en arrière-plan
  - Système de timer et coroutines
  - Calcul progression hors ligne
  - Upgrades de production

### Phase 3 : Monétisation (Semaine 3)
- **Objectif** : Intégration revenus
- **Livrables** :
  - Unity Ads (rewarded + interstitial)
  - Unity IAP (packs de base)
  - Système de boosters temporaires
  - Analytics de base

### Phase 4 : Cloud Save (Semaine 4)
- **Objectif** : Persistance et synchronisation
- **Livrables** :
  - Firebase Authentication
  - Firestore integration
  - Résolution conflits sauvegarde
  - Migration données locales

### Phase 5 : Notifications (Semaine 5)
- **Objectif** : Rétention utilisateurs
- **Livrables** :
  - Firebase Cloud Messaging
  - Notifications locales
  - Système de scheduling intelligent
  - Préférences utilisateur

### Phase 6 : Polish & UI/UX (Semaine 6)
- **Objectif** : Expérience utilisateur finale
- **Livrables** :
  - Animations et effets visuels
  - Sound design spatial
  - Optimisations performance
  - Tutoriel interactif

### Phase 7 : Publication (Semaine 7)
- **Objectif** : Lancement sur stores
- **Livrables** :
  - Tests finaux multi-devices
  - Store listings (screenshots, descriptions)
  - Soumission Google Play + App Store
  - Monitoring post-lancement
