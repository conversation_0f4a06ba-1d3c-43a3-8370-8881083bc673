using System.Numerics;
using UnityEngine;

namespace SpaceClicker.Utils
{
    /// <summary>
    /// Extensions pour BigInteger pour faciliter l'utilisation dans Unity
    /// </summary>
    public static class BigIntegerExtensions
    {
        /// <summary>
        /// Convertit un BigInteger en string formaté pour l'affichage
        /// </summary>
        public static string ToDisplayString(this BigInteger value)
        {
            if (value < 1000)
                return value.ToString();
            
            double doubleValue = (double)value;
            string[] suffixes = { "", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc" };
            
            int suffixIndex = 0;
            while (doubleValue >= 1000 && suffixIndex < suffixes.Length - 1)
            {
                doubleValue /= 1000;
                suffixIndex++;
            }
            
            return $"{doubleValue:F2}{suffixes[suffixIndex]}";
        }
        
        /// <summary>
        /// Convertit un BigInteger en string compact (sans décimales si possible)
        /// </summary>
        public static string ToCompactString(this BigInteger value)
        {
            if (value < 1000)
                return value.ToString();
            
            double doubleValue = (double)value;
            string[] suffixes = { "", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc" };
            
            int suffixIndex = 0;
            while (doubleValue >= 1000 && suffixIndex < suffixes.Length - 1)
            {
                doubleValue /= 1000;
                suffixIndex++;
            }
            
            // Afficher sans décimales si c'est un nombre entier
            if (doubleValue == (int)doubleValue)
                return $"{(int)doubleValue}{suffixes[suffixIndex]}";
            else
                return $"{doubleValue:F1}{suffixes[suffixIndex]}";
        }
        
        /// <summary>
        /// Vérifie si un BigInteger est supérieur ou égal à un autre
        /// </summary>
        public static bool IsGreaterOrEqual(this BigInteger value, BigInteger other)
        {
            return value >= other;
        }
        
        /// <summary>
        /// Retourne le maximum entre deux BigInteger
        /// </summary>
        public static BigInteger Max(BigInteger a, BigInteger b)
        {
            return a > b ? a : b;
        }
        
        /// <summary>
        /// Retourne le minimum entre deux BigInteger
        /// </summary>
        public static BigInteger Min(BigInteger a, BigInteger b)
        {
            return a < b ? a : b;
        }
        
        /// <summary>
        /// Convertit un float en BigInteger
        /// </summary>
        public static BigInteger ToBigInteger(this float value)
        {
            return new BigInteger(Mathf.Max(0, value));
        }
        
        /// <summary>
        /// Convertit un double en BigInteger
        /// </summary>
        public static BigInteger ToBigInteger(this double value)
        {
            return new BigInteger(System.Math.Max(0, value));
        }
        
        /// <summary>
        /// Ajoute un pourcentage à un BigInteger
        /// </summary>
        public static BigInteger AddPercentage(this BigInteger value, float percentage)
        {
            double result = (double)value * (1.0 + percentage / 100.0);
            return new BigInteger(result);
        }
        
        /// <summary>
        /// Multiplie un BigInteger par un pourcentage
        /// </summary>
        public static BigInteger MultiplyByPercentage(this BigInteger value, float percentage)
        {
            double result = (double)value * (percentage / 100.0);
            return new BigInteger(result);
        }
    }
}
