#!/usr/bin/env python3
"""
Script pour corriger les erreurs restantes dans ChronoForge
"""

import os
import re

def fix_file(file_path):
    """Corrige un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix GetTotalAllocatedMemory parameter
        content = re.sub(r'GetTotalAllocatedMemory\(true\)', 'GetTotalAllocatedMemory(false)', content)
        
        # Add missing methods to GameManager
        if 'GameManager.cs' in file_path:
            if 'public void RestartGame()' not in content:
                # Add RestartGame method before the last closing brace
                content = content.replace(
                    '        #endregion\n    }\n    \n    /// <summary>',
                    '''        public void RestartGame()
        {
            RestartRun();
        }
        
        #endregion
    }
    
    /// <summary>'''
                )
        
        # Add missing methods to UIManager
        if 'UIManager.cs' in file_path:
            if 'public void ToggleDebugUI()' not in content:
                # Add ToggleDebugUI method
                content = content.replace(
                    '        #endregion\n    }\n}',
                    '''        public void ToggleDebugUI()
        {
            // Toggle debug UI visibility
            if (debugConsole != null)
            {
                debugConsole.ToggleConsole();
            }
        }
        
        #endregion
    }
}'''
                )
        
        # Add missing methods to InventoryController
        if 'InventoryController.cs' in file_path:
            if 'public void RefreshInventory()' not in content:
                content = content.replace(
                    '        #endregion\n    }\n}',
                    '''        public void RefreshInventory()
        {
            UpdateInventoryDisplay();
        }
        
        #endregion
    }
}'''
                )
        
        # Add missing methods to SettingsController
        if 'SettingsController.cs' in file_path:
            if 'public void RefreshSettings()' not in content:
                content = content.replace(
                    '        #endregion\n    }\n}',
                    '''        public void RefreshSettings()
        {
            LoadSettings();
            UpdateUI();
        }
        
        #endregion
    }
}'''
                )
        
        # Add missing methods to ProgressionManager
        if 'ProgressionManager.cs' in file_path:
            if 'public void RecordRunCompletion(' not in content:
                content = content.replace(
                    '        #endregion\n    }\n    \n    /// <summary>',
                    '''        public void RecordRunCompletion(bool success, int floor, float time)
        {
            if (success)
            {
                successfulRuns++;
            }
            totalRuns++;
            
            UnityEngine.Debug.Log($"📊 Run completion recorded: Success={success}, Floor={floor}, Time={time:F1}s");
        }
        
        #endregion
    }
    
    /// <summary>'''
                )
        
        # Make ShowLevelUpEffect public in HUDController
        if 'HUDController.cs' in file_path:
            content = re.sub(r'private void ShowLevelUpEffect\(\)', 'public void ShowLevelUpEffect()', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
    
    return False

def main():
    """Fonction principale"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(script_dir, "Assets", "Scripts")
    
    if not os.path.exists(assets_dir):
        print(f"❌ Directory not found: {assets_dir}")
        return
    
    fixed_count = 0
    
    # Parcourir tous les fichiers .cs
    for root, dirs, files in os.walk(assets_dir):
        for file in files:
            if file.endswith('.cs'):
                file_path = os.path.join(root, file)
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} files!")

if __name__ == "__main__":
    main()
