using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using SpaceClicker.Core;
using SpaceClicker.Utils;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Contrôleur principal de l'interface utilisateur
    /// </summary>
    public class MainUIController : MonoBehaviour
    {
        [Header("Resource Displays")]
        public ResourceDisplay energyDisplay;
        public ResourceDisplay mineralsDisplay;
        public ResourceDisplay researchDisplay;
        public ResourceDisplay currencyDisplay;
        
        [Header("Module Panels")]
        public GameObject solarPanelModule;
        public GameObject miningDrillModule;
        public GameObject researchLabModule;
        public GameObject spaceStationModule;
        
        [Header("Upgrade Panels")]
        public Transform upgradeContainer;
        public GameObject upgradeButtonPrefab;
        
        [Header("Info Panel")]
        public GameObject infoPanel;
        public TextMeshProUGUI infoTitle;
        public TextMeshProUGUI infoDescription;
        public Button infoCloseButton;
        
        [Header("Settings Panel")]
        public GameObject settingsPanel;
        public Slider musicVolumeSlider;
        public Slider sfxVolumeSlider;
        public Toggle autoSaveToggle;
        public Button settingsCloseButton;
        
        [Header("Top Bar")]
        public Button settingsButton;
        public Button infoButton;
        public TextMeshProUGUI gameVersionText;
        
        [Header("Background")]
        public ParticleSystem starsParticles;
        public Image backgroundImage;
        
        // Private fields
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        private GameManager gameManager;
        private List<UpgradeButton> upgradeButtons = new List<UpgradeButton>();
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeReferences();
            SetupEventListeners();
        }
        
        private void Start()
        {
            InitializeUI();
            SetupUpgradeButtons();
        }
        
        private void OnDestroy()
        {
            CleanupEventListeners();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeReferences()
        {
            // Get manager references
            resourceManager = FindObjectOfType<ResourceManager>();
            upgradeManager = FindObjectOfType<UpgradeManager>();
            gameManager = FindObjectOfType<GameManager>();
            
            if (resourceManager == null)
                Debug.LogError("ResourceManager not found!");
            
            if (upgradeManager == null)
                Debug.LogError("UpgradeManager not found!");
            
            if (gameManager == null)
                Debug.LogError("GameManager not found!");
        }
        
        private void SetupEventListeners()
        {
            // Button listeners
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OpenSettings);
            
            if (infoButton != null)
                infoButton.onClick.AddListener(OpenInfo);
            
            if (infoCloseButton != null)
                infoCloseButton.onClick.AddListener(CloseInfo);
            
            if (settingsCloseButton != null)
                settingsCloseButton.onClick.AddListener(CloseSettings);
            
            // Settings listeners
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
            
            if (autoSaveToggle != null)
                autoSaveToggle.onValueChanged.AddListener(OnAutoSaveToggled);
            
            // Game events
            UpgradeManager.OnUpgradePurchased += OnUpgradePurchased;
            ResourceManager.OnResourceChanged += OnResourceChanged;
        }
        
        private void CleanupEventListeners()
        {
            // Cleanup button listeners
            if (settingsButton != null)
                settingsButton.onClick.RemoveAllListeners();
            
            if (infoButton != null)
                infoButton.onClick.RemoveAllListeners();
            
            if (infoCloseButton != null)
                infoCloseButton.onClick.RemoveAllListeners();
            
            if (settingsCloseButton != null)
                settingsCloseButton.onClick.RemoveAllListeners();
            
            // Cleanup settings listeners
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.RemoveAllListeners();
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.RemoveAllListeners();
            
            if (autoSaveToggle != null)
                autoSaveToggle.onValueChanged.RemoveAllListeners();
            
            // Cleanup game events
            UpgradeManager.OnUpgradePurchased -= OnUpgradePurchased;
            ResourceManager.OnResourceChanged -= OnResourceChanged;
        }
        
        #endregion
        
        #region UI Initialization
        
        private void InitializeUI()
        {
            // Setup resource displays
            if (energyDisplay != null)
                energyDisplay.SetResourceType(ResourceType.Energy);
            
            if (mineralsDisplay != null)
                mineralsDisplay.SetResourceType(ResourceType.Minerals);
            
            if (researchDisplay != null)
                researchDisplay.SetResourceType(ResourceType.ResearchData);
            
            if (currencyDisplay != null)
                currencyDisplay.SetResourceType(ResourceType.SpaceCurrency);
            
            // Setup version text
            if (gameVersionText != null)
                gameVersionText.text = $"v{GameConstants.GAME_VERSION}";
            
            // Initialize panels as closed
            if (infoPanel != null)
                infoPanel.SetActive(false);
            
            if (settingsPanel != null)
                settingsPanel.SetActive(false);
            
            // Setup background effects
            SetupBackgroundEffects();
            
            // Load settings
            LoadSettings();
        }
        
        private void SetupBackgroundEffects()
        {
            // Configure star particles
            if (starsParticles != null)
            {
                var main = starsParticles.main;
                main.startLifetime = 10f;
                main.startSpeed = 2f;
                main.maxParticles = 100;
                
                var emission = starsParticles.emission;
                emission.rateOverTime = 10f;
                
                var shape = starsParticles.shape;
                shape.shapeType = ParticleSystemShapeType.Rectangle;
                shape.scale = new Vector3(Screen.width, Screen.height, 1);
            }
        }
        
        private void SetupUpgradeButtons()
        {
            if (upgradeManager == null || upgradeContainer == null || upgradeButtonPrefab == null)
                return;
            
            // Clear existing buttons
            foreach (Transform child in upgradeContainer)
            {
                Destroy(child.gameObject);
            }
            upgradeButtons.Clear();
            
            // Create upgrade buttons
            var availableUpgrades = upgradeManager.GetAvailableUpgrades();
            
            foreach (var upgrade in availableUpgrades)
            {
                GameObject buttonObj = Instantiate(upgradeButtonPrefab, upgradeContainer);
                UpgradeButton upgradeButton = buttonObj.GetComponent<UpgradeButton>();
                
                if (upgradeButton != null)
                {
                    upgradeButton.SetupUpgrade(upgrade);
                    upgradeButtons.Add(upgradeButton);
                }
            }
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnUpgradePurchased(UpgradeData upgrade)
        {
            // Refresh all upgrade buttons
            foreach (var button in upgradeButtons)
            {
                button.RefreshDisplay();
            }
            
            // Update module visuals based on upgrade
            UpdateModuleVisuals(upgrade);
        }
        
        private void OnResourceChanged(ResourceType type, System.Numerics.BigInteger amount)
        {
            // Resource displays handle their own updates
            // This is for any additional UI updates needed
        }
        
        #endregion
        
        #region Panel Management
        
        public void OpenSettings()
        {
            if (settingsPanel != null)
            {
                settingsPanel.SetActive(true);
                PlayPanelOpenAnimation(settingsPanel);
            }
        }
        
        public void CloseSettings()
        {
            if (settingsPanel != null)
            {
                PlayPanelCloseAnimation(settingsPanel, () => {
                    settingsPanel.SetActive(false);
                });
            }
        }
        
        public void OpenInfo()
        {
            if (infoPanel != null)
            {
                UpdateInfoContent();
                infoPanel.SetActive(true);
                PlayPanelOpenAnimation(infoPanel);
            }
        }
        
        public void CloseInfo()
        {
            if (infoPanel != null)
            {
                PlayPanelCloseAnimation(infoPanel, () => {
                    infoPanel.SetActive(false);
                });
            }
        }
        
        private void UpdateInfoContent()
        {
            if (infoTitle != null)
                infoTitle.text = "Space Clicker";
            
            if (infoDescription != null)
            {
                infoDescription.text = 
                    "Welcome to Space Clicker!\n\n" +
                    "• Click modules to generate resources\n" +
                    "• Purchase upgrades to increase production\n" +
                    "• Unlock new modules and technologies\n" +
                    "• Build your space empire!\n\n" +
                    $"Version: {GameConstants.GAME_VERSION}";
            }
        }
        
        #endregion
        
        #region Settings
        
        private void OnMusicVolumeChanged(float value)
        {
            // TODO: Implement audio manager
            PlayerPrefs.SetFloat("MusicVolume", value);
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            // TODO: Implement audio manager
            PlayerPrefs.SetFloat("SFXVolume", value);
        }
        
        private void OnAutoSaveToggled(bool enabled)
        {
            PlayerPrefs.SetInt("AutoSave", enabled ? 1 : 0);
            
            if (gameManager != null)
            {
                // TODO: Update game manager auto-save setting
            }
        }
        
        private void LoadSettings()
        {
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = PlayerPrefs.GetFloat("SFXVolume", 0.8f);
            
            if (autoSaveToggle != null)
                autoSaveToggle.isOn = PlayerPrefs.GetInt("AutoSave", 1) == 1;
        }
        
        #endregion
        
        #region Module Visuals
        
        private void UpdateModuleVisuals(UpgradeData upgrade)
        {
            switch (upgrade.type)
            {
                case UpgradeType.SolarPanelEfficiency:
                    UpdateSolarPanelVisuals();
                    break;
                case UpgradeType.MiningDrillSpeed:
                    UpdateMiningDrillVisuals();
                    break;
                case UpgradeType.ResearchLabCapacity:
                    UpdateResearchLabVisuals();
                    break;
                case UpgradeType.SpaceStationUpgrade:
                    UpdateSpaceStationVisuals();
                    break;
            }
        }
        
        private void UpdateSolarPanelVisuals()
        {
            if (solarPanelModule != null)
            {
                // Add visual effects based on upgrade level
                // TODO: Implement module visual updates
            }
        }
        
        private void UpdateMiningDrillVisuals()
        {
            if (miningDrillModule != null)
            {
                // Add visual effects based on upgrade level
                // TODO: Implement module visual updates
            }
        }
        
        private void UpdateResearchLabVisuals()
        {
            if (researchLabModule != null)
            {
                // Add visual effects based on upgrade level
                // TODO: Implement module visual updates
            }
        }
        
        private void UpdateSpaceStationVisuals()
        {
            if (spaceStationModule != null)
            {
                // Add visual effects based on upgrade level
                // TODO: Implement module visual updates
            }
        }
        
        #endregion
        
        #region Animations
        
        private void PlayPanelOpenAnimation(GameObject panel)
        {
            panel.transform.localScale = Vector3.zero;
            LeanTween.scale(panel, Vector3.one, 0.3f)
                .setEase(LeanTweenType.easeOutBack);
        }
        
        private void PlayPanelCloseAnimation(GameObject panel, System.Action onComplete)
        {
            LeanTween.scale(panel, Vector3.zero, 0.2f)
                .setEase(LeanTweenType.easeInBack)
                .setOnComplete(onComplete);
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Refresh all UI elements
        /// </summary>
        public void RefreshUI()
        {
            // Refresh resource displays
            if (energyDisplay != null) energyDisplay.RefreshDisplay();
            if (mineralsDisplay != null) mineralsDisplay.RefreshDisplay();
            if (researchDisplay != null) researchDisplay.RefreshDisplay();
            if (currencyDisplay != null) currencyDisplay.RefreshDisplay();
            
            // Refresh upgrade buttons
            foreach (var button in upgradeButtons)
            {
                button.RefreshDisplay();
            }
        }
        
        /// <summary>
        /// Show a temporary message to the player
        /// </summary>
        public void ShowMessage(string message, float duration = 3f)
        {
            // TODO: Implement message system
            Debug.Log($"Message: {message}");
        }
        
        #endregion
    }
}
