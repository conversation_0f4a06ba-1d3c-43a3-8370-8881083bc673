#!/usr/bin/env python3
"""
Script pour corriger tous les Debug.Log dans ChronoForge
"""

import os
import re

def fix_debug_logs_in_file(file_path):
    """Corrige les Debug.Log dans un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacements
        replacements = [
            (r'ChronoForge\.Debug\.Log\(', 'UnityEngine.Debug.Log('),
            (r'ChronoForge\.Debug\.LogWarning\(', 'UnityEngine.Debug.LogWarning('),
            (r'ChronoForge\.Debug\.LogError\(', 'UnityEngine.Debug.LogError('),
            (r'(?<!UnityEngine\.)Debug\.Log\(', 'UnityEngine.Debug.Log('),
            (r'(?<!UnityEngine\.)Debug\.LogWarning\(', 'UnityEngine.Debug.LogWarning('),
            (r'(?<!UnityEngine\.)Debug\.LogError\(', 'UnityEngine.Debug.LogError('),
        ]
        
        modified = False
        for pattern, replacement in replacements:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                modified = True
        
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
    
    return False

def main():
    """Fonction principale"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(script_dir, "Assets", "Scripts")
    
    if not os.path.exists(assets_dir):
        print(f"❌ Directory not found: {assets_dir}")
        return
    
    fixed_count = 0
    
    # Parcourir tous les fichiers .cs
    for root, dirs, files in os.walk(assets_dir):
        for file in files:
            if file.endswith('.cs'):
                file_path = os.path.join(root, file)
                if fix_debug_logs_in_file(file_path):
                    fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} files!")

if __name__ == "__main__":
    main()
