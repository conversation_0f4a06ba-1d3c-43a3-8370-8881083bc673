using UnityEngine;
using UnityEditor;
using System.IO;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Générateur d'assets visuels pour Space Clicker
    /// </summary>
    public class AssetGenerator : EditorWindow
    {
        private string spritesPath = "Assets/Sprites/";
        private string materialsPath = "Assets/Materials/";
        private string texturesPath = "Assets/Textures/";
        
        [MenuItem("Space Clicker/Asset Generator")]
        public static void ShowWindow()
        {
            GetWindow<AssetGenerator>("Asset Generator");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker Asset Generator", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Paths
            GUILayout.Label("Asset Paths:");
            spritesPath = EditorGUILayout.TextField("Sprites Path:", spritesPath);
            materialsPath = EditorGUILayout.TextField("Materials Path:", materialsPath);
            texturesPath = EditorGUILayout.TextField("Textures Path:", texturesPath);
            
            GUILayout.Space(20);
            
            // Generation buttons
            if (GUILayout.Button("Generate All Assets", GUILayout.Height(30)))
            {
                GenerateAllAssets();
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Generate Sprites"))
            {
                GenerateSprites();
            }
            
            if (GUILayout.Button("Generate Materials"))
            {
                GenerateMaterials();
            }
            
            if (GUILayout.Button("Generate Textures"))
            {
                GenerateTextures();
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Create Folder Structure"))
            {
                CreateFolderStructure();
            }
        }
        
        private void GenerateAllAssets()
        {
            Debug.Log("🎨 Generating all Space Clicker assets...");
            
            CreateFolderStructure();
            GenerateTextures();
            GenerateSprites();
            GenerateMaterials();
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("✅ All assets generated successfully!");
        }
        
        private void CreateFolderStructure()
        {
            Debug.Log("Creating folder structure...");
            
            // Create main folders
            CreateFolderIfNotExists("Assets", "Sprites");
            CreateFolderIfNotExists("Assets", "Materials");
            CreateFolderIfNotExists("Assets", "Textures");
            CreateFolderIfNotExists("Assets", "Audio");
            CreateFolderIfNotExists("Assets", "Prefabs");
            CreateFolderIfNotExists("Assets", "Scenes");
            
            // Create subfolders
            CreateFolderIfNotExists("Assets/Sprites", "Modules");
            CreateFolderIfNotExists("Assets/Sprites", "UI");
            CreateFolderIfNotExists("Assets/Sprites", "Effects");
            CreateFolderIfNotExists("Assets/Sprites", "Icons");
            
            CreateFolderIfNotExists("Assets/Audio", "Music");
            CreateFolderIfNotExists("Assets/Audio", "SFX");
            CreateFolderIfNotExists("Assets/Audio", "Ambient");
            
            CreateFolderIfNotExists("Assets/Materials", "UI");
            CreateFolderIfNotExists("Assets/Materials", "Effects");
            
            Debug.Log("✅ Folder structure created");
        }
        
        private void GenerateTextures()
        {
            Debug.Log("Generating textures...");
            
            // Generate basic textures programmatically
            GenerateGradientTexture("SpaceBackground", 512, 512, Color.black, new Color(0.1f, 0.05f, 0.2f));
            GenerateCircleTexture("ModuleBase", 256, 256, Color.white);
            GenerateStarTexture("StarParticle", 32, 32);
            GenerateNoiseTexture("SpaceNoise", 256, 256);
            
            Debug.Log("✅ Textures generated");
        }
        
        private void GenerateSprites()
        {
            Debug.Log("Generating sprites...");
            
            // Generate module sprites
            GenerateModuleSprite("SolarPanel", new Color(1f, 0.84f, 0f)); // Gold
            GenerateModuleSprite("MiningDrill", new Color(0f, 1f, 1f)); // Cyan
            GenerateModuleSprite("ResearchLab", new Color(0f, 1f, 0f)); // Green
            GenerateModuleSprite("SpaceStation", new Color(1f, 0f, 1f)); // Magenta
            
            // Generate UI sprites
            GenerateUISprite("ButtonBackground", 150, 50, new Color(0.2f, 0.3f, 0.5f));
            GenerateUISprite("PanelBackground", 300, 200, new Color(0.1f, 0.1f, 0.1f, 0.8f));
            
            // Generate icon sprites
            GenerateIconSprite("EnergyIcon", new Color(1f, 0.84f, 0f));
            GenerateIconSprite("MineralsIcon", new Color(0f, 1f, 1f));
            GenerateIconSprite("ResearchIcon", new Color(0f, 1f, 0f));
            GenerateIconSprite("CurrencyIcon", new Color(1f, 0f, 1f));
            
            Debug.Log("✅ Sprites generated");
        }
        
        private void GenerateMaterials()
        {
            Debug.Log("Generating materials...");
            
            // Generate UI materials
            GenerateUIMaterial("UIDefault", Color.white);
            GenerateUIMaterial("UIGlow", Color.cyan, true);
            
            // Generate particle materials
            GenerateParticleMaterial("ClickParticle", Color.white);
            GenerateParticleMaterial("UpgradeParticle", Color.green);
            
            Debug.Log("✅ Materials generated");
        }
        
        #region Texture Generation
        
        private void GenerateGradientTexture(string name, int width, int height, Color startColor, Color endColor)
        {
            Texture2D texture = new Texture2D(width, height);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float t = (float)y / height;
                    Color color = Color.Lerp(startColor, endColor, t);
                    texture.SetPixel(x, y, color);
                }
            }
            
            texture.Apply();
            SaveTexture(texture, name);
        }
        
        private void GenerateCircleTexture(string name, int width, int height, Color color)
        {
            Texture2D texture = new Texture2D(width, height);
            Vector2 center = new Vector2(width / 2f, height / 2f);
            float radius = Mathf.Min(width, height) / 2f;
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), center);
                    float alpha = 1f - Mathf.Clamp01(distance / radius);
                    
                    Color pixelColor = color;
                    pixelColor.a = alpha;
                    texture.SetPixel(x, y, pixelColor);
                }
            }
            
            texture.Apply();
            SaveTexture(texture, name);
        }
        
        private void GenerateStarTexture(string name, int size)
        {
            Texture2D texture = new Texture2D(size, size);
            Vector2 center = new Vector2(size / 2f, size / 2f);
            
            for (int x = 0; x < size; x++)
            {
                for (int y = 0; y < size; y++)
                {
                    Vector2 pos = new Vector2(x, y);
                    float distance = Vector2.Distance(pos, center);
                    
                    // Create star shape
                    float angle = Mathf.Atan2(y - center.y, x - center.x);
                    float starShape = Mathf.Abs(Mathf.Sin(angle * 4)) * 0.5f + 0.5f;
                    
                    float alpha = (1f - distance / (size / 2f)) * starShape;
                    alpha = Mathf.Clamp01(alpha);
                    
                    Color color = Color.white;
                    color.a = alpha;
                    texture.SetPixel(x, y, color);
                }
            }
            
            texture.Apply();
            SaveTexture(texture, name);
        }
        
        private void GenerateNoiseTexture(string name, int width, int height)
        {
            Texture2D texture = new Texture2D(width, height);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float noise = Mathf.PerlinNoise(x * 0.1f, y * 0.1f);
                    Color color = new Color(noise, noise, noise, 1f);
                    texture.SetPixel(x, y, color);
                }
            }
            
            texture.Apply();
            SaveTexture(texture, name);
        }
        
        #endregion
        
        #region Sprite Generation
        
        private void GenerateModuleSprite(string name, Color moduleColor)
        {
            int size = 200;
            Texture2D texture = new Texture2D(size, size);
            Vector2 center = new Vector2(size / 2f, size / 2f);
            
            for (int x = 0; x < size; x++)
            {
                for (int y = 0; y < size; y++)
                {
                    Vector2 pos = new Vector2(x, y);
                    float distance = Vector2.Distance(pos, center);
                    
                    if (distance < size / 2f - 10)
                    {
                        // Main module body
                        float alpha = 1f - (distance / (size / 2f)) * 0.3f;
                        Color color = moduleColor;
                        color.a = alpha;
                        texture.SetPixel(x, y, color);
                    }
                    else if (distance < size / 2f)
                    {
                        // Border
                        Color borderColor = moduleColor * 0.7f;
                        borderColor.a = 1f;
                        texture.SetPixel(x, y, borderColor);
                    }
                    else
                    {
                        texture.SetPixel(x, y, Color.clear);
                    }
                }
            }
            
            texture.Apply();
            SaveSprite(texture, name);
        }
        
        private void GenerateUISprite(string name, int width, int height, Color color)
        {
            Texture2D texture = new Texture2D(width, height);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    texture.SetPixel(x, y, color);
                }
            }
            
            texture.Apply();
            SaveSprite(texture, name);
        }
        
        private void GenerateIconSprite(string name, Color color)
        {
            int size = 32;
            Texture2D texture = new Texture2D(size, size);
            Vector2 center = new Vector2(size / 2f, size / 2f);
            
            for (int x = 0; x < size; x++)
            {
                for (int y = 0; y < size; y++)
                {
                    Vector2 pos = new Vector2(x, y);
                    float distance = Vector2.Distance(pos, center);
                    
                    if (distance < size / 2f - 2)
                    {
                        texture.SetPixel(x, y, color);
                    }
                    else
                    {
                        texture.SetPixel(x, y, Color.clear);
                    }
                }
            }
            
            texture.Apply();
            SaveSprite(texture, name);
        }
        
        #endregion
        
        #region Material Generation
        
        private void GenerateUIMaterial(string name, Color color, bool glow = false)
        {
            Material material = new Material(Shader.Find("UI/Default"));
            material.color = color;
            
            if (glow)
            {
                // Try to use UI glow shader if available
                Shader glowShader = Shader.Find("UI/Unlit/Transparent");
                if (glowShader != null)
                {
                    material.shader = glowShader;
                }
            }
            
            SaveMaterial(material, name);
        }
        
        private void GenerateParticleMaterial(string name, Color color)
        {
            Material material = new Material(Shader.Find("Sprites/Default"));
            material.color = color;
            
            SaveMaterial(material, name);
        }
        
        #endregion
        
        #region Save Methods
        
        private void SaveTexture(Texture2D texture, string name)
        {
            string path = texturesPath + name + ".png";
            byte[] bytes = texture.EncodeToPNG();
            File.WriteAllBytes(path, bytes);
            AssetDatabase.ImportAsset(path);
            
            // Configure texture import settings
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Default;
                importer.alphaIsTransparency = true;
                importer.SaveAndReimport();
            }
        }
        
        private void SaveSprite(Texture2D texture, string name)
        {
            string path = spritesPath + name + ".png";
            byte[] bytes = texture.EncodeToPNG();
            File.WriteAllBytes(path, bytes);
            AssetDatabase.ImportAsset(path);
            
            // Configure sprite import settings
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Sprite;
                importer.spriteImportMode = SpriteImportMode.Single;
                importer.alphaIsTransparency = true;
                importer.SaveAndReimport();
            }
        }
        
        private void SaveMaterial(Material material, string name)
        {
            string path = materialsPath + name + ".mat";
            AssetDatabase.CreateAsset(material, path);
        }
        
        private void CreateFolderIfNotExists(string parentFolder, string folderName)
        {
            string fullPath = parentFolder + "/" + folderName;
            if (!AssetDatabase.IsValidFolder(fullPath))
            {
                AssetDatabase.CreateFolder(parentFolder, folderName);
            }
        }
        
        #endregion
    }
}
