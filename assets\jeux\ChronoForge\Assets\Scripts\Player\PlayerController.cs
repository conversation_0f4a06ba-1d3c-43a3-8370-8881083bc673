using UnityEngine;
using ChronoForge.Core;
using ChronoForge.Combat;

namespace ChronoForge.Player
{
    /// <summary>
    /// Contrôleur principal du joueur pour ChronoForge
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class PlayerController : MonoBehaviour
    {
        [Header("Movement")]
        public float moveSpeed = 8f;
        public float acceleration = 50f;
        public float deceleration = 50f;
        
        [Header("Dash")]
        public float dashDistance = 5f;
        public float dashDuration = 0.2f;
        public float dashCooldown = 1f;
        public bool dashThroughEnemies = true;
        
        [Header("Input")]
        public KeyCode dashKey = KeyCode.Space;
        public KeyCode interactKey = KeyCode.E;
        
        [Header("Animation")]
        public Animator animator;
        public SpriteRenderer spriteRenderer;
        
        [Header("Audio")]
        public AudioSource audioSource;
        public AudioClip dashSound;
        public AudioClip footstepSound;
        
        // Events
        public static System.Action OnPlayerDeath;
        public static System.Action OnPlayerDash;
        public static System.Action<Vector2> OnPlayerMove;
        
        // Components
        private Rigidbody2D rb;
        private PlayerCombat playerCombat;
        private PlayerStats playerStats;
        private ChronoForge.Player.HealthSystem healthSystem;
        
        // Movement state
        private Vector2 moveInput;
        private Vector2 lastMoveDirection = Vector2.down;
        private bool isMoving = false;
        
        // Dash state
        private bool isDashing = false;
        private bool canDash = true;
        private float dashTimer = 0f;
        private float dashCooldownTimer = 0f;
        private Vector2 dashDirection;
        
        // State
        private bool isAlive = true;
        private bool canMove = true;
        private bool isInvulnerable = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            InitializePlayer();
        }
        
        private void Update()
        {
            if (!isAlive || !canMove) return;
            
            HandleInput();
            UpdateDash();
            UpdateAnimation();
        }
        
        private void FixedUpdate()
        {
            if (!isAlive) return;
            
            HandleMovement();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeComponents()
        {
            rb = GetComponent<Rigidbody2D>();
            playerCombat = GetComponent<PlayerCombat>();
            playerStats = GetComponent<PlayerStats>();
            healthSystem = GetComponent<ChronoForge.Player.HealthSystem>();
            
            if (animator == null)
                animator = GetComponentInChildren<Animator>();
            
            if (spriteRenderer == null)
                spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            
            if (audioSource == null)
                audioSource = GetComponent<AudioSource>();
        }
        
        private void InitializePlayer()
        {
            // Configure rigidbody
            rb.gravityScale = 0f;
            rb.drag = 0f;
            rb.angularDrag = 0f;
            rb.freezeRotation = true;
            
            // Subscribe to health events
            if (healthSystem != null)
            {
                ChronoForge.Player.HealthSystem.OnDeath += HandleDeath;
                // OnDamageTaken doesn't exist, we'll handle damage through other means
            }
            
            UnityEngine.Debug.Log("🎮 PlayerController initialized");
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleInput()
        {
            // Movement input
            moveInput.x = Input.GetAxisRaw("Horizontal");
            moveInput.y = Input.GetAxisRaw("Vertical");
            
            // Normalize diagonal movement
            if (moveInput.magnitude > 1f)
            {
                moveInput = moveInput.normalized;
            }
            
            // Update last move direction for facing
            if (moveInput != Vector2.zero)
            {
                lastMoveDirection = moveInput;
            }
            
            // Dash input
            if (Input.GetKeyDown(dashKey) && canDash && !isDashing)
            {
                StartDash();
            }
            
            // Interact input
            if (Input.GetKeyDown(interactKey))
            {
                TryInteract();
            }
        }
        
        #endregion
        
        #region Movement
        
        private void HandleMovement()
        {
            if (isDashing)
            {
                HandleDashMovement();
                return;
            }
            
            Vector2 targetVelocity = moveInput * moveSpeed;
            
            // Apply acceleration/deceleration
            if (moveInput != Vector2.zero)
            {
                // Accelerating
                rb.velocity = Vector2.MoveTowards(rb.velocity, targetVelocity, acceleration * Time.fixedDeltaTime);
                isMoving = true;
            }
            else
            {
                // Decelerating
                rb.velocity = Vector2.MoveTowards(rb.velocity, Vector2.zero, deceleration * Time.fixedDeltaTime);
                isMoving = rb.velocity.magnitude > 0.1f;
            }
            
            // Notify movement
            if (isMoving)
            {
                OnPlayerMove?.Invoke(rb.velocity);
            }
        }
        
        #endregion
        
        #region Dash System
        
        private void StartDash()
        {
            if (!canDash || isDashing) return;
            
            // Determine dash direction
            dashDirection = moveInput != Vector2.zero ? moveInput : lastMoveDirection;
            dashDirection = dashDirection.normalized;
            
            // Start dash
            isDashing = true;
            canDash = false;
            dashTimer = dashDuration;
            dashCooldownTimer = dashCooldown;
            
            // Set invulnerability during dash
            if (dashThroughEnemies)
            {
                SetInvulnerable(true);
            }
            
            // Play effects
            PlayDashEffects();
            
            OnPlayerDash?.Invoke();
            
            UnityEngine.Debug.Log($"💨 Dash started in direction: {dashDirection}");
        }
        
        private void HandleDashMovement()
        {
            if (!isDashing) return;
            
            // Calculate dash speed
            float dashSpeed = dashDistance / dashDuration;
            rb.velocity = dashDirection * dashSpeed;
            
            dashTimer -= Time.fixedDeltaTime;
            
            if (dashTimer <= 0f)
            {
                EndDash();
            }
        }
        
        private void EndDash()
        {
            isDashing = false;
            
            // Remove invulnerability
            if (dashThroughEnemies)
            {
                SetInvulnerable(false);
            }
            
            // Reduce velocity after dash
            rb.velocity *= 0.5f;
            
            UnityEngine.Debug.Log("💨 Dash ended");
        }
        
        private void UpdateDash()
        {
            // Update dash cooldown
            if (!canDash)
            {
                dashCooldownTimer -= Time.deltaTime;
                if (dashCooldownTimer <= 0f)
                {
                    canDash = true;
                }
            }
        }
        
        private void PlayDashEffects()
        {
            // Play dash sound
            if (audioSource != null && dashSound != null)
            {
                audioSource.PlayOneShot(dashSound);
            }
            
            // Trigger dash animation
            if (animator != null)
            {
                animator.SetTrigger("Dash");
            }
            
            // Visual effect (could add particle system here)
            StartCoroutine(DashVisualEffect());
        }
        
        private System.Collections.IEnumerator DashVisualEffect()
        {
            // Simple flash effect
            if (spriteRenderer != null)
            {
                Color originalColor = spriteRenderer.color;
                spriteRenderer.color = Color.white;
                
                yield return new WaitForSeconds(0.1f);
                
                spriteRenderer.color = originalColor;
            }
        }
        
        #endregion
        
        #region Animation
        
        private void UpdateAnimation()
        {
            if (animator == null) return;
            
            // Movement animation
            animator.SetBool("IsMoving", isMoving);
            animator.SetFloat("MoveSpeed", rb.velocity.magnitude);
            
            // Direction animation
            animator.SetFloat("MoveX", lastMoveDirection.x);
            animator.SetFloat("MoveY", lastMoveDirection.y);
            
            // Dash animation
            animator.SetBool("IsDashing", isDashing);
            
            // Flip sprite based on direction
            if (spriteRenderer != null && lastMoveDirection.x != 0)
            {
                spriteRenderer.flipX = lastMoveDirection.x < 0;
            }
        }
        
        #endregion
        
        #region Health and Damage
        
        private void HandleDeath()
        {
            if (!isAlive) return;
            
            isAlive = false;
            canMove = false;
            
            // Stop movement
            rb.velocity = Vector2.zero;
            
            // Play death animation
            if (animator != null)
            {
                animator.SetTrigger("Death");
            }
            
            // Notify death
            OnPlayerDeath?.Invoke();
            
            UnityEngine.Debug.Log("💀 Player died");
        }
        
        private void HandleDamageTaken(float damage)
        {
            if (!isAlive) return;
            
            // Play hurt animation
            if (animator != null)
            {
                animator.SetTrigger("Hurt");
            }
            
            // Knockback effect
            StartCoroutine(DamageKnockback());
        }
        
        private System.Collections.IEnumerator DamageKnockback()
        {
            // Brief knockback
            Vector2 knockbackDirection = -lastMoveDirection;
            rb.AddForce(knockbackDirection * 300f);
            
            yield return new WaitForSeconds(0.1f);
            
            // Reduce velocity
            rb.velocity *= 0.5f;
        }
        
        #endregion
        
        #region Interaction
        
        private void TryInteract()
        {
            // Check for interactable objects in range
            Collider2D[] colliders = Physics2D.OverlapCircleAll(transform.position, 1.5f);
            
            foreach (var collider in colliders)
            {
                IInteractable interactable = collider.GetComponent<IInteractable>();
                if (interactable != null)
                {
                    interactable.Interact(this);
                    break; // Only interact with one object
                }
            }
        }
        
        #endregion
        
        #region State Management
        
        public void SetInvulnerable(bool invulnerable)
        {
            isInvulnerable = invulnerable;
            
            if (healthSystem != null)
            {
                healthSystem.SetInvulnerable(invulnerable);
            }
        }
        
        public void SetCanMove(bool canMove)
        {
            this.canMove = canMove;

            if (!canMove)
            {
                rb.velocity = Vector2.zero;
                moveInput = Vector2.zero;
            }
        }

        public void SetCanAttack(bool canAttack)
        {
            if (playerCombat != null)
            {
                playerCombat.SetCanAttack(canAttack);
            }
        }
        
        public void ResetForNewRun()
        {
            // Reset state
            isAlive = true;
            canMove = true;
            isInvulnerable = false;
            
            // Reset movement
            rb.velocity = Vector2.zero;
            moveInput = Vector2.zero;
            lastMoveDirection = Vector2.down;
            isMoving = false;
            
            // Reset dash
            isDashing = false;
            canDash = true;
            dashTimer = 0f;
            dashCooldownTimer = 0f;
            
            // Reset health
            if (healthSystem != null)
            {
                healthSystem.ResetHealth();
            }
            
            // Reset combat
            if (playerCombat != null)
            {
                playerCombat.ResetForNewRun();
            }
            
            // Reset animation
            if (animator != null)
            {
                animator.SetBool("IsMoving", false);
                animator.SetBool("IsDashing", false);
            }
            
            UnityEngine.Debug.Log("🔄 Player reset for new run");
        }
        
        #endregion
        
        #region Public Getters
        
        public bool IsAlive()
        {
            return isAlive;
        }
        
        public bool IsMoving()
        {
            return isMoving;
        }
        
        public bool IsDashing()
        {
            return isDashing;
        }
        
        public bool CanDash()
        {
            return canDash;
        }
        
        public Vector2 GetMoveDirection()
        {
            return lastMoveDirection;
        }
        
        public Vector2 GetVelocity()
        {
            return rb.velocity;
        }
        
        public float GetDashCooldownProgress()
        {
            return canDash ? 1f : 1f - (dashCooldownTimer / dashCooldown);
        }
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            // Draw interaction range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, 1.5f);
            
            // Draw dash direction
            if (isDashing)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawRay(transform.position, dashDirection * dashDistance);
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Interface pour les objets interactifs
    /// </summary>
    public interface IInteractable
    {
        void Interact(PlayerController player);
        bool CanInteract(PlayerController player);
        string GetInteractionPrompt();
    }
}
