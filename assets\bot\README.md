# 🤖 Bot de Publication Automatisé

Un bot Python pour automatiser vos publications sur les réseaux sociaux (Twitter, Reddit, LinkedIn, Dev.to).

## 🎯 Objectifs

- **Automatiser** la publication de contenus sur plusieurs plateformes
- **Augmenter** votre visibilité en ligne (personal branding)
- **Diffuser** vos projets et outils
- **Maintenir** une présence régulière sans effort manuel

## ✨ Fonctionnalités

- 📝 **Gestion de contenu** : Posts stockés en YAML/JSON/Markdown
- ⏰ **Planification** : Publications automatiques avec APScheduler
- 🎯 **Multi-plateformes** : Twitter, Reddit, LinkedIn, Dev.to
- 🔄 **Recyclage** : Republication automatique d'anciens contenus
- 📊 **Logs** : Suivi détaillé des publications
- 🛡️ **Sécurité** : Authentification via variables d'environnement
- 🖥️ **CLI** : Interface en ligne de commande complète

## 🚀 Installation

### 1. Prérequis

- Python 3.8+
- Comptes sur les plateformes souhaitées
- Clés API pour chaque plateforme

### 2. Installation des dépendances

```bash
cd assets/bot
pip install -r requirements.txt
```

### 3. Configuration

1. **Copiez le fichier d'environnement :**
```bash
cp .env.example .env
```

2. **Remplissez vos clés API dans `.env` :**
```env
# Twitter/X API
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
TWITTER_ACCESS_TOKEN=your_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_access_token_secret
TWITTER_BEARER_TOKEN=your_bearer_token

# Reddit API
REDDIT_CLIENT_ID=your_client_id
REDDIT_CLIENT_SECRET=your_client_secret
REDDIT_USERNAME=your_username
REDDIT_PASSWORD=your_password

# LinkedIn API
LINKEDIN_ACCESS_TOKEN=your_access_token

# Dev.to API
DEVTO_API_KEY=your_api_key
```

3. **Personnalisez la configuration dans `config.yaml`**

## 📝 Utilisation

### Interface CLI

```bash
# Vérifier le statut
python cli.py status

# Lister les posts
python cli.py list-posts

# Publier un post spécifique
python cli.py publish mon_post_id

# Publier sur une plateforme spécifique
python cli.py publish mon_post_id --platform twitter

# Test de publication
python cli.py test

# Démarrer le planificateur
python cli.py start

# Ajouter un post manuel
python cli.py add-post "Mon titre" "Mon contenu" --platforms twitter,reddit --tags dev,opensource
```

### Création de posts

Créez vos posts dans le dossier `posts/` au format YAML :

```yaml
posts:
  - id: "mon_projet"
    title: "Mon nouveau projet open source"
    body: |
      🚀 Nouveau projet : MoneyBby
      
      Un gestionnaire financier personnel simple et efficace.
      👉 https://github.com/NeethDseven/moneybby
      
      #fintech #webdev #opensource
    platforms: ["twitter", "linkedin", "devto"]
    schedule: "2025-07-01 14:00"
    tags: ["fintech", "webdev", "opensource"]
```

## 🔧 Configuration des API

### Twitter/X API

1. Créez une app sur [developer.twitter.com](https://developer.twitter.com)
2. Générez les clés API et tokens
3. Activez les permissions de lecture/écriture

### Reddit API

1. Créez une app sur [reddit.com/prefs/apps](https://reddit.com/prefs/apps)
2. Type : "script"
3. Notez le client ID et secret

### LinkedIn API

1. Créez une app sur [developer.linkedin.com](https://developer.linkedin.com)
2. Demandez l'accès à l'API Marketing
3. Générez un token d'accès

### Dev.to API

1. Allez dans [dev.to/settings/account](https://dev.to/settings/account)
2. Générez une clé API dans la section "DEV Community API Keys"

## 📊 Planification

Le bot supporte plusieurs types de planification :

- **Date fixe** : `"2025-07-01 14:00"`
- **Récurrent** : `"recurring"` avec `frequency: "weekly"`
- **Manuel** : `"manual"` pour publication à la demande

### Heures de travail

Configurez les heures de publication dans `config.yaml` :

```yaml
scheduler:
  working_hours:
    start: "09:00"
    end: "18:00"
  working_days: [1, 2, 3, 4, 5]  # Lundi à Vendredi
```

## 🔄 Recyclage de contenu

Le bot peut automatiquement republier d'anciens contenus :

```yaml
recycling:
  enabled: true
  min_days_before_repost: 30
  max_reposts: 3
  variation_enabled: true
```

## 📈 Logs et monitoring

- **Logs de publication** : `bot.log`
- **Historique** : `published.json`
- **Niveaux de log** : DEBUG, INFO, WARNING, ERROR

## 🛡️ Sécurité

- ✅ Clés API dans variables d'environnement
- ✅ Fichier `.env` non versionné
- ✅ Rate limiting respecté
- ✅ Gestion d'erreurs robuste

## 🚀 Déploiement

### Local
```bash
python cli.py start --blocking
```

### VPS/Serveur
```bash
# En arrière-plan
python cli.py start &

# Avec systemd (recommandé)
sudo systemctl enable bot-publication
sudo systemctl start bot-publication
```

### GitHub Actions
Voir `examples/github-actions.yml` pour un exemple de déploiement automatisé.

## 🤝 Contribution

1. Fork le projet
2. Créez une branche feature
3. Committez vos changements
4. Poussez vers la branche
5. Ouvrez une Pull Request

## 📄 Licence

MIT License - voir le fichier LICENSE pour plus de détails.

## 🆘 Support

- 📖 Documentation complète dans `/docs`
- 🐛 Issues sur GitHub
- 💬 Discussions dans les issues

---

**Créé avec ❤️ pour automatiser votre présence en ligne**
