using UnityEngine;
using System.Collections.Generic;
using ChronoForge.Player;

namespace ChronoForge.Combat
{
    /// <summary>
    /// Système d'armes pour ChronoForge
    /// </summary>
    public class WeaponSystem : MonoBehaviour
    {
        [Header("Current Weapon")]
        public WeaponData currentWeapon;
        public Transform weaponHolder;
        public GameObject weaponVisual;
        
        [Header("Weapon Settings")]
        public List<WeaponData> availableWeapons = new List<WeaponData>();
        public float weaponSwitchCooldown = 0.5f;
        
        [Header("Projectile Settings")]
        public Transform firePoint;
        public GameObject defaultProjectile;
        
        // Events
        public static System.Action<WeaponData> OnWeaponChanged;
        public static System.Action<GameObject> OnProjectileFired;
        public static System.Action<GameObject> OnEnemyHit;
        
        // Components
        private PlayerStats playerStats;
        private AudioSource audioSource;
        
        // State
        private bool canSwitchWeapon = true;
        private float lastSwitchTime = 0f;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeWeaponSystem();
        }
        
        private void Start()
        {
            SetupDefaultWeapon();
        }
        
        private void Update()
        {
            HandleWeaponSwitching();
            UpdateWeaponCooldowns();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeWeaponSystem()
        {
            playerStats = GetComponent<PlayerStats>();
            audioSource = GetComponent<AudioSource>();
            
            if (weaponHolder == null)
            {
                GameObject holder = new GameObject("WeaponHolder");
                holder.transform.SetParent(transform);
                holder.transform.localPosition = Vector3.zero;
                weaponHolder = holder.transform;
            }
            
            if (firePoint == null)
            {
                GameObject point = new GameObject("FirePoint");
                point.transform.SetParent(weaponHolder);
                point.transform.localPosition = Vector3.forward;
                firePoint = point.transform;
            }
            
            InitializeDefaultWeapons();
            
            UnityEngine.Debug.Log("⚔️ WeaponSystem initialized");
        }
        
        private void InitializeDefaultWeapons()
        {
            if (availableWeapons.Count == 0)
            {
                // Add default weapons
                availableWeapons.Add(CreateDefaultWeapon(WeaponType.PlasmaSword));
                availableWeapons.Add(CreateDefaultWeapon(WeaponType.CryoCrossbow));
                availableWeapons.Add(CreateDefaultWeapon(WeaponType.NeuralGrimoire));
            }
        }
        
        private WeaponData CreateDefaultWeapon(WeaponType type)
        {
            switch (type)
            {
                case WeaponType.PlasmaSword:
                    return new WeaponData
                    {
                        weaponName = "Plasma Sword",
                        weaponType = WeaponType.PlasmaSword,
                        damage = 15f,
                        attackSpeed = 1.2f,
                        range = 2f,
                        critChance = 0.1f,
                        specialCooldown = 3f,
                        description = "Épée plasma énergétique avec attaques rapides",
                        rarity = WeaponRarity.Common
                    };
                    
                case WeaponType.CryoCrossbow:
                    return new WeaponData
                    {
                        weaponName = "Cryo Crossbow",
                        weaponType = WeaponType.CryoCrossbow,
                        damage = 20f,
                        attackSpeed = 0.8f,
                        range = 6f,
                        critChance = 0.15f,
                        specialCooldown = 5f,
                        description = "Arbalète cryogénique qui ralentit les ennemis",
                        rarity = WeaponRarity.Uncommon
                    };
                    
                case WeaponType.NeuralGrimoire:
                    return new WeaponData
                    {
                        weaponName = "Neural Grimoire",
                        weaponType = WeaponType.NeuralGrimoire,
                        damage = 25f,
                        attackSpeed = 0.6f,
                        range = 4f,
                        critChance = 0.2f,
                        specialCooldown = 8f,
                        description = "Grimoire neural avec sorts technomantiques",
                        rarity = WeaponRarity.Rare
                    };
                    
                default:
                    return CreateDefaultWeapon(WeaponType.PlasmaSword);
            }
        }
        
        #endregion
        
        #region Weapon Management
        
        public void EquipWeapon(WeaponData weapon)
        {
            if (weapon == null) return;
            
            currentWeapon = weapon;
            UpdateWeaponVisual();
            ApplyWeaponStats();
            
            OnWeaponChanged?.Invoke(currentWeapon);
            
            UnityEngine.Debug.Log($"⚔️ Equipped weapon: {weapon.weaponName}");
        }
        
        public void EquipWeapon(int weaponIndex)
        {
            if (weaponIndex >= 0 && weaponIndex < availableWeapons.Count)
            {
                EquipWeapon(availableWeapons[weaponIndex]);
            }
        }
        
        private void SetupDefaultWeapon()
        {
            if (currentWeapon == null && availableWeapons.Count > 0)
            {
                EquipWeapon(availableWeapons[0]);
            }
        }
        
        private void UpdateWeaponVisual()
        {
            // Remove old weapon visual
            if (weaponVisual != null)
            {
                DestroyImmediate(weaponVisual);
            }
            
            // Create new weapon visual
            if (currentWeapon != null && currentWeapon.weaponPrefab != null)
            {
                weaponVisual = Instantiate(currentWeapon.weaponPrefab, weaponHolder);
                weaponVisual.transform.localPosition = Vector3.zero;
                weaponVisual.transform.localRotation = Quaternion.identity;
            }
        }
        
        private void ApplyWeaponStats()
        {
            if (currentWeapon == null || playerStats == null) return;
            
            // Remove old weapon modifiers
            playerStats.RemoveModifiersFromSource("Weapon");
            
            // Apply new weapon modifiers
            playerStats.AddStatModifier(new StatModifier
            {
                statType = StatType.AttackDamage,
                value = currentWeapon.damage,
                type = ModifierType.Flat,
                duration = -1f,
                source = "Weapon"
            });
            
            playerStats.AddStatModifier(new StatModifier
            {
                statType = StatType.AttackSpeed,
                value = currentWeapon.attackSpeed,
                type = ModifierType.Multiplicative,
                duration = -1f,
                source = "Weapon"
            });
            
            playerStats.AddStatModifier(new StatModifier
            {
                statType = StatType.CritChance,
                value = currentWeapon.critChance,
                type = ModifierType.Flat,
                duration = -1f,
                source = "Weapon"
            });
        }
        
        #endregion
        
        #region Weapon Switching
        
        private void HandleWeaponSwitching()
        {
            if (!canSwitchWeapon) return;
            
            // Scroll wheel weapon switching
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                int direction = scroll > 0f ? 1 : -1;
                SwitchWeapon(direction);
            }
            
            // Number key weapon switching
            for (int i = 1; i <= 9; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha0 + i))
                {
                    EquipWeapon(i - 1);
                    break;
                }
            }
        }
        
        private void SwitchWeapon(int direction)
        {
            if (availableWeapons.Count <= 1) return;
            
            int currentIndex = availableWeapons.IndexOf(currentWeapon);
            int newIndex = (currentIndex + direction) % availableWeapons.Count;
            
            if (newIndex < 0)
                newIndex = availableWeapons.Count - 1;
            
            EquipWeapon(newIndex);
            StartSwitchCooldown();
        }
        
        private void StartSwitchCooldown()
        {
            canSwitchWeapon = false;
            lastSwitchTime = Time.time;
        }
        
        private void UpdateWeaponCooldowns()
        {
            if (!canSwitchWeapon)
            {
                if (Time.time - lastSwitchTime >= weaponSwitchCooldown)
                {
                    canSwitchWeapon = true;
                }
            }
        }
        
        #endregion
        
        #region Combat Integration
        
        public float ModifyDamage(float baseDamage)
        {
            if (currentWeapon == null) return baseDamage;
            
            float modifiedDamage = baseDamage;
            
            // Apply weapon-specific damage modifiers
            switch (currentWeapon.weaponType)
            {
                case WeaponType.PlasmaSword:
                    // Plasma weapons deal bonus damage to shields
                    modifiedDamage *= 1.2f;
                    break;
                    
                case WeaponType.CryoCrossbow:
                    // Cryo weapons have chance to freeze
                    if (Random.value < 0.3f)
                    {
                        // Apply slow effect (would need enemy reference)
                        modifiedDamage *= 1.1f;
                    }
                    break;
                    
                case WeaponType.NeuralGrimoire:
                    // Neural weapons have higher crit chance
                    if (Random.value < currentWeapon.critChance * 2f)
                    {
                        modifiedDamage *= 2.5f; // Higher crit multiplier
                    }
                    break;
            }
            
            return modifiedDamage;
        }
        
        public void OnHitEnemy(GameObject enemy)
        {
            if (currentWeapon == null) return;
            
            // Apply weapon-specific on-hit effects
            ApplyWeaponEffects(enemy);
            
            OnEnemyHit?.Invoke(enemy);
        }
        
        private void ApplyWeaponEffects(GameObject enemy)
        {
            switch (currentWeapon.weaponType)
            {
                case WeaponType.PlasmaSword:
                    // Plasma burn effect
                    ApplyBurnEffect(enemy);
                    break;
                    
                case WeaponType.CryoCrossbow:
                    // Slow effect
                    ApplySlowEffect(enemy);
                    break;
                    
                case WeaponType.NeuralGrimoire:
                    // Confusion effect
                    ApplyConfusionEffect(enemy);
                    break;
            }
        }
        
        #endregion
        
        #region Special Abilities
        
        public void UseSpecialAbility()
        {
            if (currentWeapon == null) return;
            
            switch (currentWeapon.weaponType)
            {
                case WeaponType.PlasmaSword:
                    PlasmaSwordSpecial();
                    break;
                    
                case WeaponType.CryoCrossbow:
                    CryoCrossbowSpecial();
                    break;
                    
                case WeaponType.NeuralGrimoire:
                    NeuralGrimoireSpecial();
                    break;
            }
        }
        
        private void PlasmaSwordSpecial()
        {
            // Plasma Wave: Area damage around player
            UnityEngine.Debug.Log("🔥 Plasma Wave unleashed!");
        }
        
        private void CryoCrossbowSpecial()
        {
            // Ice Storm: Multiple projectiles
            UnityEngine.Debug.Log("❄️ Ice Storm activated!");
        }
        
        private void NeuralGrimoireSpecial()
        {
            // Mind Control: Convert enemy temporarily
            UnityEngine.Debug.Log("🧠 Mind Control cast!");
        }
        
        #endregion
        
        #region Status Effects
        
        private void ApplyBurnEffect(GameObject enemy)
        {
            // Apply burn damage over time
            UnityEngine.Debug.Log($"🔥 Applied burn to {enemy.name}");
        }
        
        private void ApplySlowEffect(GameObject enemy)
        {
            // Reduce enemy movement speed
            UnityEngine.Debug.Log($"❄️ Applied slow to {enemy.name}");
        }
        
        private void ApplyConfusionEffect(GameObject enemy)
        {
            // Confuse enemy AI
            UnityEngine.Debug.Log($"🧠 Applied confusion to {enemy.name}");
        }
        
        #endregion
        
        #region Public Methods
        
        public void ResetForNewRun()
        {
            // Keep equipped weapon but reset cooldowns
            canSwitchWeapon = true;
            lastSwitchTime = 0f;
        }
        
        public void AddWeapon(WeaponData weapon)
        {
            if (!availableWeapons.Contains(weapon))
            {
                availableWeapons.Add(weapon);
                UnityEngine.Debug.Log($"⚔️ Added weapon: {weapon.weaponName}");
            }
        }
        
        public bool HasWeapon(WeaponType weaponType)
        {
            return availableWeapons.Exists(w => w.weaponType == weaponType);
        }
        
        public WeaponData GetCurrentWeapon()
        {
            return currentWeapon;
        }
        
        public List<WeaponData> GetAvailableWeapons()
        {
            return new List<WeaponData>(availableWeapons);
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types d'armes disponibles
    /// </summary>
    public enum WeaponType
    {
        PlasmaSword,
        CryoCrossbow,
        NeuralGrimoire,
        QuantumRifle,
        VoidHammer,
        TechBlade
    }
    
    /// <summary>
    /// Rareté des armes
    /// </summary>
    public enum WeaponRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
    
    /// <summary>
    /// Données d'une arme
    /// </summary>
    [System.Serializable]
    public class WeaponData
    {
        public string weaponName;
        public WeaponType weaponType;
        public string description;
        public WeaponRarity rarity;
        
        [Header("Stats")]
        public float damage;
        public float attackSpeed;
        public float range;
        public float critChance;
        public float specialCooldown;
        
        [Header("Visual")]
        public GameObject weaponPrefab;
        public Sprite weaponIcon;
        public AudioClip attackSound;
        public AudioClip specialSound;
        
        [Header("Effects")]
        public GameObject hitEffect;
        public GameObject specialEffect;
    }
}
