using NUnit.Framework;
using UnityEngine;
using System.Collections;
using System.Numerics;
using SpaceClicker.Core;
using SpaceClicker.Utils;

// Note: UnityTest requires Test Framework package
// Install via Window > Package Manager > Test Framework
#if UNITY_INCLUDE_TESTS
using UnityEngine.TestTools;
#endif

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Tests d'intégration pour vérifier que tous les systèmes fonctionnent ensemble
    /// </summary>
    public class GameIntegrationTests
    {
        private GameObject gameManagerObject;
        private GameObject resourceManagerObject;
        private GameObject upgradeManagerObject;
        
        private GameManager gameManager;
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        
        [SetUp]
        public void Setup()
        {
            // Créer les managers dans l'ordre correct
            SetupResourceManager();
            SetupUpgradeManager();
            SetupGameManager();
            
            // Connecter les références
            ConnectManagerReferences();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (gameManagerObject != null)
                Object.DestroyImmediate(gameManagerObject);
            
            if (resourceManagerObject != null)
                Object.DestroyImmediate(resourceManagerObject);
            
            if (upgradeManagerObject != null)
                Object.DestroyImmediate(upgradeManagerObject);
        }
        
        private void SetupResourceManager()
        {
            resourceManagerObject = new GameObject("TestResourceManager");
            resourceManager = resourceManagerObject.AddComponent<ResourceManager>();
        }
        
        private void SetupUpgradeManager()
        {
            upgradeManagerObject = new GameObject("TestUpgradeManager");
            upgradeManager = upgradeManagerObject.AddComponent<UpgradeManager>();
        }
        
        private void SetupGameManager()
        {
            gameManagerObject = new GameObject("TestGameManager");
            gameManager = gameManagerObject.AddComponent<GameManager>();
        }
        
        private void ConnectManagerReferences()
        {
            // Utiliser la réflexion pour connecter les références privées
            var resourceField = typeof(UpgradeManager).GetField("resourceManager", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            resourceField?.SetValue(upgradeManager, resourceManager);
            
            var upgradeField = typeof(GameManager).GetField("upgradeManager", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            upgradeField?.SetValue(gameManager, upgradeManager);
            
            var gameResourceField = typeof(GameManager).GetField("resourceManager", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            gameResourceField?.SetValue(gameManager, resourceManager);
        }
        
        [Test]
        public void FullGameplayLoop_ClickGenerateUpgradePurchase_ShouldWorkCorrectly()
        {
            // Arrange - Start with clean state
            resourceManager.ResetResources();
            upgradeManager.ResetUpgrades();
            
            // Act 1: Generate some energy by clicking
            BigInteger initialEnergy = resourceManager.GetResource(ResourceType.Energy);
            resourceManager.OnResourceClicked(ResourceType.Energy);
            BigInteger afterClickEnergy = resourceManager.GetResource(ResourceType.Energy);
            
            // Assert 1: Energy should increase after click
            Assert.IsTrue(afterClickEnergy > initialEnergy, "Energy should increase after clicking");
            
            // Act 2: Generate enough resources for an upgrade
            var availableUpgrades = upgradeManager.GetAvailableUpgrades();
            var energyUpgrade = availableUpgrades.Find(u => u.type == UpgradeType.SolarPanelEfficiency);
            
            if (energyUpgrade != null)
            {
                BigInteger upgradeCost = energyUpgrade.GetNextLevelCost();
                resourceManager.AddResource(energyUpgrade.costType, upgradeCost);
                
                // Act 3: Purchase the upgrade
                bool purchaseResult = upgradeManager.PurchaseUpgrade(energyUpgrade.id);
                
                // Assert 2: Purchase should succeed
                Assert.IsTrue(purchaseResult, "Upgrade purchase should succeed");
                Assert.AreEqual(1, energyUpgrade.currentLevel, "Upgrade level should be 1");
                
                // Act 4: Check production multiplier
                float multiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
                
                // Assert 3: Multiplier should be greater than 1
                Assert.IsTrue(multiplier > 1f, "Production multiplier should increase after upgrade");
            }
            else
            {
                Assert.Inconclusive("No energy upgrade found for integration test");
            }
        }
        
        [Test]
        public void MultipleResourceTypes_ShouldWorkIndependently()
        {
            // Arrange
            resourceManager.ResetResources();
            
            // Act: Generate different types of resources
            resourceManager.OnResourceClicked(ResourceType.Energy);
            resourceManager.OnResourceClicked(ResourceType.Minerals);
            resourceManager.OnResourceClicked(ResourceType.ResearchData);
            
            // Assert: All resource types should have increased
            Assert.IsTrue(resourceManager.GetResource(ResourceType.Energy) > BigInteger.Zero, 
                "Energy should be generated");
            Assert.IsTrue(resourceManager.GetResource(ResourceType.Minerals) > BigInteger.Zero, 
                "Minerals should be generated");
            Assert.IsTrue(resourceManager.GetResource(ResourceType.ResearchData) > BigInteger.Zero, 
                "Research data should be generated");
        }
        
        [Test]
        public void UpgradeProgression_ShouldIncreaseEffectiveness()
        {
            // Arrange
            resourceManager.ResetResources();
            upgradeManager.ResetUpgrades();
            
            var upgrades = upgradeManager.GetAvailableUpgrades();
            var testUpgrade = upgrades[0];
            
            // Give plenty of resources
            BigInteger largeCost = new BigInteger(999999);
            resourceManager.AddResource(testUpgrade.costType, largeCost);
            
            // Act: Purchase multiple levels
            float initialMultiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
            
            upgradeManager.PurchaseUpgrade(testUpgrade.id);
            float level1Multiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
            
            upgradeManager.PurchaseUpgrade(testUpgrade.id);
            float level2Multiplier = upgradeManager.GetProductionMultiplier(ResourceType.Energy);
            
            // Assert: Each level should provide increasing benefits
            if (testUpgrade.type == UpgradeType.SolarPanelEfficiency)
            {
                Assert.IsTrue(level1Multiplier > initialMultiplier, 
                    "Level 1 should be better than initial");
                Assert.IsTrue(level2Multiplier > level1Multiplier, 
                    "Level 2 should be better than level 1");
            }
        }
        
#if UNITY_INCLUDE_TESTS
        [UnityTest]
#endif
        public IEnumerator ResourceProduction_WithUpgrades_ShouldIncreaseOverTime()
        {
            // Arrange
            resourceManager.ResetResources();
            upgradeManager.ResetUpgrades();
            
            // Set initial production rate
            resourceManager.SetProductionRate(ResourceType.Energy, 10f);
            BigInteger initialAmount = resourceManager.GetResource(ResourceType.Energy);
            
            // Wait for production
            yield return new WaitForSeconds(1f);
            
            BigInteger afterProductionAmount = resourceManager.GetResource(ResourceType.Energy);
            BigInteger baseProduction = afterProductionAmount - initialAmount;
            
            // Purchase an upgrade that affects energy production
            var upgrades = upgradeManager.GetAvailableUpgrades();
            var energyUpgrade = upgrades.Find(u => u.type == UpgradeType.SolarPanelEfficiency);
            
            if (energyUpgrade != null)
            {
                BigInteger cost = energyUpgrade.GetNextLevelCost();
                resourceManager.AddResource(energyUpgrade.costType, cost);
                upgradeManager.PurchaseUpgrade(energyUpgrade.id);
                
                // Wait for more production with upgrade
                BigInteger beforeUpgradeProduction = resourceManager.GetResource(ResourceType.Energy);
                yield return new WaitForSeconds(1f);
                BigInteger afterUpgradeProduction = resourceManager.GetResource(ResourceType.Energy);
                BigInteger upgradeProduction = afterUpgradeProduction - beforeUpgradeProduction;
                
                // Assert: Production with upgrade should be higher
                Assert.IsTrue(upgradeProduction > baseProduction, 
                    "Production should increase after purchasing upgrade");
            }
        }
        
        [Test]
        public void SaveLoadSystem_ShouldPreserveGameState()
        {
            // Arrange
            resourceManager.ResetResources();
            upgradeManager.ResetUpgrades();
            
            // Set up some game state
            BigInteger testEnergyAmount = new BigInteger(12345);
            BigInteger testMineralsAmount = new BigInteger(67890);
            
            resourceManager.AddResource(ResourceType.Energy, testEnergyAmount);
            resourceManager.AddResource(ResourceType.Minerals, testMineralsAmount);
            
            // Purchase an upgrade
            var upgrades = upgradeManager.GetAvailableUpgrades();
            if (upgrades.Count > 0)
            {
                var testUpgrade = upgrades[0];
                BigInteger cost = testUpgrade.GetNextLevelCost();
                resourceManager.AddResource(testUpgrade.costType, cost);
                upgradeManager.PurchaseUpgrade(testUpgrade.id);
            }
            
            // Act: Save and load
            gameManager.SaveGame();
            
            // Reset state
            resourceManager.ResetResources();
            upgradeManager.ResetUpgrades();
            
            // Load the saved state
            bool loadResult = gameManager.LoadGame();
            
            // Assert: State should be restored
            Assert.IsTrue(loadResult, "Load should succeed");
            Assert.AreEqual(testEnergyAmount, resourceManager.GetResource(ResourceType.Energy), 
                "Energy should be restored");
            Assert.AreEqual(testMineralsAmount, resourceManager.GetResource(ResourceType.Minerals), 
                "Minerals should be restored");
            
            if (upgrades.Count > 0)
            {
                Assert.AreEqual(1, upgrades[0].currentLevel, "Upgrade level should be restored");
            }
        }
        
        [Test]
        public void GameConstants_ShouldBeValid()
        {
            // Test that game constants are properly defined
            Assert.IsTrue(GameConstants.BASE_CLICK_POWER > 0, "Base click power should be positive");
            Assert.IsTrue(GameConstants.PRODUCTION_TICK_RATE > 0, "Production tick rate should be positive");
            Assert.IsNotNull(GameConstants.GAME_VERSION, "Game version should be defined");
            Assert.IsNotNull(GameConstants.ENERGY_COLOR, "Energy color should be defined");
            Assert.IsNotNull(GameConstants.MINERALS_COLOR, "Minerals color should be defined");
        }
        
        [Test]
        public void BigIntegerExtensions_IntegrationWithGameSystems_ShouldWork()
        {
            // Test that BigInteger extensions work with actual game values
            BigInteger largeAmount = new BigInteger(1234567890);
            resourceManager.AddResource(ResourceType.Energy, largeAmount);
            
            BigInteger retrieved = resourceManager.GetResource(ResourceType.Energy);
            string compactString = retrieved.ToCompactString();
            string displayString = retrieved.ToDisplayString();
            
            Assert.AreEqual(largeAmount, retrieved, "Large amounts should be stored correctly");
            Assert.IsNotNull(compactString, "Compact string should be generated");
            Assert.IsNotNull(displayString, "Display string should be generated");
            Assert.IsTrue(compactString.Length < displayString.Length, 
                "Compact string should be shorter than display string for large numbers");
        }
    }
}
