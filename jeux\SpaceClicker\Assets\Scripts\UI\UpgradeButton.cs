using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Numerics;
using SpaceClicker.Core;
using SpaceClicker.Utils;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Composant pour un bouton d'upgrade
    /// </summary>
    public class UpgradeButton : MonoBehaviour
    {
        [Header("UI References")]
        public Button upgradeButton;
        public TextMeshProUGUI nameText;
        public TextMeshProUGUI descriptionText;
        public TextMeshProUGUI costText;
        public TextMeshProUGUI levelText;
        public Image iconImage;
        public Image backgroundImage;
        
        [Header("Visual States")]
        public Color affordableColor = Color.white;
        public Color unaffordableColor = Color.gray;
        public Color maxLevelColor = Color.green;
        
        // Private fields
        private UpgradeData upgradeData;
        private UpgradeManager upgradeManager;
        private ResourceManager resourceManager;
        
        // Animation
        private Vector3 originalScale;
        private bool isInitialized = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            originalScale = transform.localScale;
        }
        
        private void Start()
        {
            Initialize();
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (upgradeButton != null)
            {
                upgradeButton.onClick.RemoveAllListeners();
            }
            
            ResourceManager.OnResourceChanged -= OnResourceChanged;
            UpgradeManager.OnUpgradePurchased -= OnUpgradePurchased;
        }
        
        #endregion
        
        #region Initialization
        
        private void Initialize()
        {
            if (isInitialized) return;
            
            // Get manager references
            upgradeManager = FindObjectOfType<UpgradeManager>();
            resourceManager = FindObjectOfType<ResourceManager>();
            
            if (upgradeManager == null)
            {
                Debug.LogError($"UpgradeManager not found for {gameObject.name}");
                return;
            }
            
            if (resourceManager == null)
            {
                Debug.LogError($"ResourceManager not found for {gameObject.name}");
                return;
            }
            
            // Setup button click
            if (upgradeButton != null)
            {
                upgradeButton.onClick.AddListener(OnUpgradeClicked);
            }
            
            // Subscribe to events
            ResourceManager.OnResourceChanged += OnResourceChanged;
            UpgradeManager.OnUpgradePurchased += OnUpgradePurchased;
            
            isInitialized = true;
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Setup this button with upgrade data
        /// </summary>
        public void SetupUpgrade(UpgradeData upgrade)
        {
            upgradeData = upgrade;
            UpdateDisplay();
        }
        
        /// <summary>
        /// Refresh the button display
        /// </summary>
        public void RefreshDisplay()
        {
            UpdateDisplay();
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnUpgradeClicked()
        {
            if (upgradeData == null || upgradeManager == null) return;
            
            bool success = upgradeManager.PurchaseUpgrade(upgradeData.id);
            
            if (success)
            {
                PlayPurchaseAnimation();
            }
            else
            {
                PlayFailAnimation();
            }
        }
        
        private void OnResourceChanged(ResourceType type, BigInteger amount)
        {
            if (upgradeData != null && type == upgradeData.costType)
            {
                UpdateAffordability();
            }
        }
        
        private void OnUpgradePurchased(UpgradeData upgrade)
        {
            if (upgradeData != null && upgrade.id == upgradeData.id)
            {
                UpdateDisplay();
            }
        }
        
        #endregion
        
        #region Display Updates
        
        private void UpdateDisplay()
        {
            if (upgradeData == null) return;
            
            // Update texts
            if (nameText != null)
            {
                nameText.text = upgradeData.name;
            }
            
            if (descriptionText != null)
            {
                descriptionText.text = upgradeData.description;
            }
            
            if (levelText != null)
            {
                if (upgradeData.maxLevel > 1)
                {
                    levelText.text = $"Level {upgradeData.currentLevel}/{upgradeData.maxLevel}";
                    levelText.gameObject.SetActive(true);
                }
                else
                {
                    levelText.gameObject.SetActive(false);
                }
            }
            
            // Update cost
            if (costText != null)
            {
                if (upgradeData.currentLevel >= upgradeData.maxLevel)
                {
                    costText.text = "MAX LEVEL";
                }
                else
                {
                    BigInteger cost = upgradeData.GetNextLevelCost();
                    costText.text = $"Cost: {cost.ToDisplayString()} {GetResourceIcon(upgradeData.costType)}";
                }
            }
            
            // Update icon based on upgrade type
            if (iconImage != null)
            {
                iconImage.sprite = GetUpgradeIcon(upgradeData.type);
            }
            
            UpdateAffordability();
        }
        
        private void UpdateAffordability()
        {
            if (upgradeData == null || upgradeManager == null || resourceManager == null) return;
            
            bool canAfford = upgradeManager.CanPurchaseUpgrade(upgradeData.id);
            bool isMaxLevel = upgradeData.currentLevel >= upgradeData.maxLevel;
            
            // Update button interactability
            if (upgradeButton != null)
            {
                upgradeButton.interactable = canAfford && !isMaxLevel;
            }
            
            // Update visual state
            Color targetColor;
            if (isMaxLevel)
            {
                targetColor = maxLevelColor;
            }
            else if (canAfford)
            {
                targetColor = affordableColor;
            }
            else
            {
                targetColor = unaffordableColor;
            }
            
            // Apply color to background
            if (backgroundImage != null)
            {
                backgroundImage.color = targetColor;
            }
            
            // Apply color to texts
            ApplyColorToTexts(targetColor);
        }
        
        private void ApplyColorToTexts(Color color)
        {
            if (nameText != null) nameText.color = color;
            if (descriptionText != null) descriptionText.color = color * 0.8f; // Slightly dimmer
            if (costText != null) costText.color = color;
            if (levelText != null) levelText.color = color;
        }
        
        #endregion
        
        #region Utility Methods
        
        private string GetResourceIcon(ResourceType type)
        {
            switch (type)
            {
                case ResourceType.Energy:
                    return "⚡";
                case ResourceType.Minerals:
                    return "💎";
                case ResourceType.ResearchData:
                    return "📡";
                case ResourceType.SpaceCurrency:
                    return "💰";
                default:
                    return "";
            }
        }
        
        private Sprite GetUpgradeIcon(UpgradeType type)
        {
            // TODO: Load actual sprites from Resources
            // For now, return null and use placeholder
            return null;
        }
        
        #endregion
        
        #region Animations
        
        private void PlayPurchaseAnimation()
        {
            // Success animation - green flash and scale
            if (backgroundImage != null)
            {
                Color originalColor = backgroundImage.color;
                
                LeanTween.value(gameObject, 0f, 1f, 0.3f)
                    .setOnUpdate((float value) => {
                        Color flashColor = Color.Lerp(Color.green, originalColor, value);
                        backgroundImage.color = flashColor;
                    });
            }
            
            // Scale animation
            LeanTween.scale(gameObject, originalScale * 1.1f, 0.1f)
                .setEase(LeanTweenType.easeOutQuad)
                .setOnComplete(() => {
                    LeanTween.scale(gameObject, originalScale, 0.2f)
                        .setEase(LeanTweenType.easeOutBounce);
                });
        }
        
        private void PlayFailAnimation()
        {
            // Fail animation - red flash and shake
            if (backgroundImage != null)
            {
                Color originalColor = backgroundImage.color;
                
                LeanTween.value(gameObject, 0f, 1f, 0.2f)
                    .setOnUpdate((float value) => {
                        Color flashColor = Color.Lerp(Color.red, originalColor, value);
                        backgroundImage.color = flashColor;
                    });
            }
            
            // Shake animation
            Vector3 originalPosition = transform.localPosition;
            LeanTween.moveLocalX(gameObject, originalPosition.x + 10f, 0.05f)
                .setEase(LeanTweenType.easeOutQuad)
                .setLoopPingPong(4)
                .setOnComplete(() => {
                    transform.localPosition = originalPosition;
                });
        }
        
        private void PlayHoverAnimation()
        {
            LeanTween.scale(gameObject, originalScale * 1.05f, 0.1f)
                .setEase(LeanTweenType.easeOutQuad);
        }
        
        private void PlayUnhoverAnimation()
        {
            LeanTween.scale(gameObject, originalScale, 0.1f)
                .setEase(LeanTweenType.easeOutQuad);
        }
        
        #endregion
        
        #region Unity UI Events
        
        public void OnPointerEnter()
        {
            PlayHoverAnimation();
        }
        
        public void OnPointerExit()
        {
            PlayUnhoverAnimation();
        }
        
        #endregion
    }
}
