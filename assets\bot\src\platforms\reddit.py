"""
Connecteur Reddit utilisant PRAW.
"""

import praw
import os
from typing import Dict, Any, Optional
from .base import PlatformConnector, PublicationResult
import logging

logger = logging.getLogger(__name__)

class RedditConnector(PlatformConnector):
    """Connecteur pour Reddit."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.reddit = None
        self.default_subreddit = config.get('default_subreddit', 'test')
        self.subreddits = config.get('subreddits', ['test'])
        
    def authenticate(self) -> bool:
        """Authentifie avec l'API Reddit."""
        try:
            self.reddit = praw.Reddit(
                client_id=os.getenv('REDDIT_CLIENT_ID'),
                client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
                user_agent=os.getenv('REDDIT_USER_AGENT', 'MoneyBby Bot v1.0'),
                username=os.getenv('REDDIT_USERNAME'),
                password=os.getenv('REDDIT_PASSWORD')
            )
            
            # Test de connexion
            user = self.reddit.user.me()
            if user:
                logger.info(f"Authentifié sur Reddit en tant que u/{user.name}")
                self.authenticated = True
                return True
            else:
                logger.error("Échec de l'authentification Reddit")
                return False
                
        except Exception as e:
            logger.error(f"Erreur d'authentification Reddit: {e}")
            return False
    
    def validate_post(self, title: str, body: str, **kwargs) -> bool:
        """Valide qu'un post respecte les contraintes Reddit."""
        # Vérifier la longueur du titre (max 300 caractères)
        if len(title) > 300:
            logger.warning(f"Titre Reddit trop long: {len(title)} caractères (max: 300)")
            return False
        
        # Vérifier que le subreddit existe
        subreddit_name = kwargs.get('subreddit', self.default_subreddit)
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            # Test d'accès au subreddit
            subreddit.display_name
            return True
        except Exception as e:
            logger.error(f"Subreddit {subreddit_name} inaccessible: {e}")
            return False
    
    def publish_post(self, title: str, body: str, **kwargs) -> PublicationResult:
        """Publie un post sur Reddit."""
        if not self.authenticated:
            return PublicationResult(
                success=False,
                platform="reddit",
                error="Non authentifié"
            )
        
        try:
            subreddit_name = kwargs.get('subreddit', self.default_subreddit)
            subreddit = self.reddit.subreddit(subreddit_name)
            
            # Déterminer le type de post
            if self._is_link_post(body):
                # Post de type lien
                url = self._extract_url(body)
                submission = subreddit.submit(
                    title=title,
                    url=url
                )
            else:
                # Post de type texte
                submission = subreddit.submit(
                    title=title,
                    selftext=body
                )
            
            if submission:
                self.log_success(kwargs.get('post_id', 'unknown'), {
                    'id': submission.id,
                    'url': submission.url,
                    'permalink': submission.permalink
                })
                
                return PublicationResult(
                    success=True,
                    platform="reddit",
                    post_id=submission.id,
                    response_data={
                        'url': f"https://reddit.com{submission.permalink}",
                        'subreddit': subreddit_name
                    }
                )
            else:
                return PublicationResult(
                    success=False,
                    platform="reddit",
                    error="Échec de la soumission"
                )
                
        except Exception as e:
            self.log_error(kwargs.get('post_id', 'unknown'), e)
            return PublicationResult(
                success=False,
                platform="reddit",
                error=str(e)
            )
    
    def _is_link_post(self, body: str) -> bool:
        """Détermine si le post contient principalement un lien."""
        # Simple heuristique: si le body contient une URL et fait moins de 200 caractères
        return ('http' in body or 'www.' in body) and len(body.strip()) < 200
    
    def _extract_url(self, text: str) -> str:
        """Extrait la première URL trouvée dans le texte."""
        import re
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text)
        return urls[0] if urls else ""
    
    def get_suitable_subreddit(self, tags: list, default: str = None) -> str:
        """Suggère un subreddit approprié basé sur les tags."""
        if default and default in self.subreddits:
            return default
            
        # Mapping tags -> subreddits
        tag_mapping = {
            'webdev': 'webdev',
            'javascript': 'javascript', 
            'python': 'Python',
            'opensource': 'opensource',
            'programming': 'programming',
            'dev': 'programming',
            'fintech': 'fintech',
            'career': 'cscareerquestions'
        }
        
        for tag in tags:
            if tag.lower() in tag_mapping:
                suggested = tag_mapping[tag.lower()]
                if suggested in self.subreddits:
                    return suggested
        
        return self.default_subreddit
    
    def check_subreddit_rules(self, subreddit_name: str) -> Dict[str, Any]:
        """Vérifie les règles d'un subreddit."""
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            rules = []
            
            for rule in subreddit.rules:
                rules.append({
                    'short_name': rule.short_name,
                    'description': rule.description,
                    'kind': rule.kind
                })
            
            return {
                'subreddit': subreddit_name,
                'subscribers': subreddit.subscribers,
                'rules': rules,
                'submission_type': subreddit.submission_type
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la vérification des règles de r/{subreddit_name}: {e}")
            return {}
