# Guide de Contribution - API2CSV

Merci de votre intérêt pour contribuer à API2CSV ! 🎉

## 🤝 Comment contribuer

### Signaler un bug
1. Vérifiez d'abord si le bug n'a pas déjà été signalé dans les [Issues](https://github.com/NeethDseven/moneybby/issues)
2. Utilisez le template "Bug report" pour créer une nouvelle issue
3. Fournissez le maximum de détails pour nous aider à reproduire le problème

### Suggérer une fonctionnalité
1. Vérifiez si la fonctionnalité n'a pas déjà été suggérée
2. Utilisez le template "Feature request"
3. Expliquez clairement le cas d'usage et la valeur ajoutée

### Contribuer au code

#### Prérequis
- Connaissance de base en HTML, CSS, JavaScript
- Familiarité avec Git et GitHub
- Compréhension du fonctionnement de PapaParse

#### Processus de contribution
1. **Fork** le repository
2. **Créez une branche** pour votre fonctionnalité :
   ```bash
   git checkout -b feature/ma-nouvelle-fonctionnalite
   ```
3. **Développez** votre fonctionnalité
4. **Testez** vos modifications
5. **Committez** avec un message descriptif :
   ```bash
   git commit -m "feat: ajouter support pour les tableaux imbriqués"
   ```
6. **Poussez** votre branche :
   ```bash
   git push origin feature/ma-nouvelle-fonctionnalite
   ```
7. **Créez une Pull Request**

#### Standards de code
- **HTML** : Utilisez une indentation de 2 espaces
- **CSS** : Suivez la convention BEM pour les classes
- **JavaScript** : 
  - Utilisez `const` et `let` au lieu de `var`
  - Fonctions fléchées quand approprié
  - Commentaires JSDoc pour les fonctions importantes
- **Nommage** : Variables et fonctions en camelCase

#### Structure du projet
```
/
├── index.html          # Page principale
├── style.css          # Styles CSS
├── script.js          # Logique JavaScript
├── README.md          # Documentation
├── PRIVACY.md         # Politique de confidentialité
├── TERMS.md           # Conditions d'utilisation
└── .github/           # Templates GitHub
```

### Types de contributions recherchées

#### 🐛 Corrections de bugs
- Problèmes de conversion JSON
- Bugs d'interface utilisateur
- Problèmes de compatibilité navigateur

#### ✨ Nouvelles fonctionnalités
- Support de nouveaux formats de données
- Améliorations de l'interface utilisateur
- Optimisations de performance
- Fonctionnalités d'accessibilité

#### 📖 Documentation
- Amélioration du README
- Exemples d'utilisation
- Guides tutoriels
- Traductions

#### 🧪 Tests
- Tests unitaires JavaScript
- Tests de compatibilité navigateur
- Tests de performance

## 📋 Checklist Pull Request

Avant de soumettre votre PR, vérifiez que :

- [ ] Le code fonctionne correctement
- [ ] Les modifications sont testées sur différents navigateurs
- [ ] La documentation est mise à jour si nécessaire
- [ ] Le code suit les standards établis
- [ ] Les commits ont des messages descriptifs
- [ ] Aucune donnée sensible n'est incluse

## 🎯 Priorités actuelles

### Haute priorité
- Amélioration de la gestion des JSON très volumineux
- Support des caractères spéciaux et encodages
- Optimisation des performances

### Moyenne priorité
- Interface utilisateur améliorée
- Fonctionnalités d'export avancées
- Mode sombre

### Basse priorité
- Intégrations avec d'autres outils
- Fonctionnalités premium

## 💡 Idées de contribution

### Pour les débutants
- Améliorer les messages d'erreur
- Ajouter des tooltips d'aide
- Corriger les fautes de frappe
- Améliorer l'accessibilité

### Pour les développeurs expérimentés
- Optimiser l'algorithme de conversion
- Implémenter le streaming pour gros fichiers
- Ajouter des tests automatisés
- Créer des extensions navigateur

## 🔧 Configuration de développement

### Installation locale
```bash
# Cloner le repository
git clone https://github.com/NeethDseven/moneybby.git
cd moneybby

# Ouvrir dans votre éditeur
code .

# Servir localement (optionnel)
python -m http.server 8000
# ou
npx serve .
```

### Tests
- Testez avec différents types de JSON
- Vérifiez la compatibilité sur Chrome, Firefox, Safari, Edge
- Testez avec des fichiers de différentes tailles
- Validez l'accessibilité avec un lecteur d'écran

## 📞 Besoin d'aide ?

- 💬 [Discussions GitHub](https://github.com/NeethDseven/moneybby/discussions)
- 🐛 [Issues](https://github.com/NeethDseven/moneybby/issues)
- 📧 Contact direct via GitHub

## 📄 Licence

En contribuant à API2CSV, vous acceptez que vos contributions soient sous licence MIT.

---

**Merci de contribuer à rendre API2CSV encore meilleur ! 🚀**
