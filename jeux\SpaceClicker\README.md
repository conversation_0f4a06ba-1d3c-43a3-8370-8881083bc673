# Space Clicker - Idle Space Exploration Game

Un jeu mobile clicker/idle d'exploration spatiale développé avec Unity.

## 🚀 Description

Space Clicker est un jeu casual où vous incarnez le commandant d'une base spatiale. Collectez des ressources, améliorez vos installations, et explorez la galaxie dans ce jeu addictif qui progresse même quand vous n'êtes pas là !

## ✨ Fonctionnalités

### Core Gameplay
- **Collecte manuelle** : Tapez sur les modules pour générer des ressources
- **Production automatique** : Vos installations produisent en continu
- **Progression hors ligne** : Continuez à progresser même fermé (max 1h)
- **Système d'upgrades** : Améliorez vos modules et débloquez de nouvelles technologies

### Ressources
- ⚡ **Énergie** : Ressource de base pour faire fonctionner la base
- 💎 **Minéraux** : Matériaux rares pour les constructions
- 📡 **Données de Recherche** : Pour débloquer les technologies
- 💰 **Crédits Spatiaux** : Monnaie premium (IAP)

### Progression
- **7 Planètes** à explorer : Station Orbitale → Lune → Mars → Europa → Proxima Centauri → Kepler-442b → Trappist-1e
- **Système de Prestige** : Recommencez avec des bonus permanents
- **Technologies** : Recherches pour débloquer de nouveaux contenus
- **Achievements** : Objectifs à long terme

## 🛠️ Stack Technique

- **Engine** : Unity 2023.3 LTS
- **Langage** : C#
- **Backend** : Firebase (Firestore, Auth, FCM, Analytics)
- **Monétisation** : Unity Ads + Unity IAP
- **Plateformes** : Android (API 21+), iOS (12.0+)

## 📁 Structure du Projet

```
Assets/
├── Scripts/
│   ├── Core/
│   │   ├── GameManager.cs          # Gestionnaire principal
│   │   ├── ResourceManager.cs      # Gestion des ressources
│   │   └── UpgradeManager.cs       # Système d'upgrades
│   ├── UI/
│   │   └── UIManager.cs            # Interface utilisateur
│   ├── Save/
│   │   └── SaveSystem.cs           # Sauvegarde/chargement
│   └── Utils/
│       ├── BigIntegerExtensions.cs # Extensions pour gros nombres
│       └── GameConstants.cs        # Constantes du jeu
├── Scenes/
│   └── MainGame.unity              # Scène principale
├── Prefabs/
│   ├── UI/                         # Prefabs d'interface
│   └── Effects/                    # Effets visuels
└── Resources/
    ├── Audio/                      # Sons et musiques
    ├── Sprites/                    # Images et icônes
    └── Data/                       # Données de configuration
```

## 🎮 Installation et Setup

### Prérequis
- Unity 2023.3 LTS ou plus récent
- Visual Studio ou VS Code
- Android SDK (pour build Android)
- Xcode (pour build iOS, Mac uniquement)

### Configuration Unity
1. Ouvrir Unity Hub
2. Cliquer sur "Open" et sélectionner le dossier `SpaceClicker`
3. Unity va importer automatiquement le projet

### Packages Requis
Le projet utilise les packages Unity suivants :
- TextMeshPro
- Unity Analytics
- Unity Ads
- Unity IAP
- Firebase SDK (à installer séparément)

### Configuration Firebase
1. Créer un projet Firebase sur [console.firebase.google.com](https://console.firebase.google.com)
2. Ajouter votre app Android/iOS
3. Télécharger `google-services.json` (Android) et `GoogleService-Info.plist` (iOS)
4. Placer les fichiers dans `Assets/StreamingAssets/`

## 🚀 Build et Déploiement

### Build Android
1. File → Build Settings
2. Sélectionner Android
3. Player Settings → Configuration :
   - Company Name : Votre nom
   - Product Name : Space Clicker
   - Bundle Identifier : com.yourcompany.spaceclicker
   - Minimum API Level : 21
4. Build

### Build iOS
1. File → Build Settings
2. Sélectionner iOS
3. Player Settings → Configuration :
   - Bundle Identifier : com.yourcompany.spaceclicker
   - Target minimum iOS Version : 12.0
4. Build et ouvrir dans Xcode

## 🎯 Roadmap de Développement

### Phase 1 : Prototype ✅
- [x] Collecte manuelle de ressources
- [x] Interface basique
- [x] Système d'upgrade simple
- [x] Sauvegarde locale

### Phase 2 : Idle Mechanics (En cours)
- [ ] Production automatique
- [ ] Calcul progression hors ligne
- [ ] Upgrades de production
- [ ] Système de timer

### Phase 3 : Monétisation
- [ ] Unity Ads integration
- [ ] Unity IAP setup
- [ ] Système de boosters
- [ ] Analytics de base

### Phase 4 : Cloud Save
- [ ] Firebase Authentication
- [ ] Firestore integration
- [ ] Synchronisation multi-device
- [ ] Résolution conflits

### Phase 5 : Notifications
- [ ] Firebase Cloud Messaging
- [ ] Notifications locales
- [ ] Scheduling intelligent
- [ ] Préférences utilisateur

### Phase 6 : Polish & UI/UX
- [ ] Animations et effets
- [ ] Sound design spatial
- [ ] Optimisations performance
- [ ] Tutoriel interactif

### Phase 7 : Publication
- [ ] Tests finaux
- [ ] Store listings
- [ ] Soumission stores
- [ ] Monitoring post-lancement

## 🎨 Assets et Ressources

### Audio
- Sons de clic spatiaux
- Musique d'ambiance spatiale
- Effets de production
- Notifications sonores

### Visuels
- Interface futuriste/spatiale
- Icônes de ressources
- Effets de particules
- Animations de modules

### Polices
- Police futuriste pour l'UI
- Police lisible pour les textes

## 📊 Métriques et Analytics

### KPIs Gameplay
- Durée moyenne des sessions
- Fréquence de connexion
- Taux de clics par minute
- Progression par niveau

### KPIs Monétisation
- ARPU (Average Revenue Per User)
- Taux de conversion IAP
- Revenus publicitaires
- LTV (Lifetime Value)

### KPIs Rétention
- Retention D1/D7/D30
- Taux d'abandon par niveau
- Engagement avec les fonctionnalités
- Feedback utilisateurs

## 🐛 Debug et Tests

### Commandes Debug (Editor uniquement)
- `Ctrl+Shift+R` : Ajouter 1M de chaque ressource
- `Ctrl+Shift+U` : Débloquer tous les upgrades
- `Ctrl+Shift+P` : Simuler progression hors ligne

### Tests
- Tests unitaires pour la logique métier
- Tests d'intégration Firebase
- Tests de performance sur devices
- Tests de monétisation

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Merci de :
1. Fork le projet
2. Créer une branche feature
3. Commit vos changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📞 Support

- **Issues** : [GitHub Issues](https://github.com/NeethDseven/SpaceClicker/issues)
- **Discord** : [Serveur Discord](https://discord.gg/spaceclicker)
- **Email** : <EMAIL>

---

**Développé avec ❤️ par NeethDseven**  
**Explorez l'infini, un clic à la fois ! 🚀**
