using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Numerics;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Gestionnaire principal de l'interface utilisateur
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("Resource UI")]
        public TextMeshProUGUI energyText;
        public TextMeshProUGUI mineralsText;
        public TextMeshProUGUI researchDataText;
        public TextMeshProUGUI spaceCurrencyText;
        
        [Header("Production UI")]
        public TextMeshProUGUI energyProductionText;
        public TextMeshProUGUI mineralsProductionText;
        public TextMeshProUGUI researchProductionText;
        
        [Header("Clickable Modules")]
        public Button energyModuleButton;
        public Button mineralsModuleButton;
        public Button researchModuleButton;
        
        [Header("Upgrade UI")]
        public Transform upgradeContainer;
        public GameObject upgradeButtonPrefab;
        
        [Header("Popups")]
        public GameObject offlineRewardsPopup;
        public TextMeshProUGUI offlineRewardsText;
        public Button claimOfflineRewardsButton;
        
        [Header("Navigation")]
        public Button upgradesTabButton;
        public Button researchTabButton;
        public Button settingsTabButton;
        public GameObject upgradesPanel;
        public GameObject researchPanel;
        public GameObject settingsPanel;
        
        [Header("Effects")]
        public ParticleSystem clickEffectPrefab;
        public AudioSource clickAudioSource;
        
        // Références
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        
        // UI State
        private Dictionary<string, GameObject> upgradeButtons;
        private OfflineRewards pendingOfflineRewards;
        
        #region Initialization
        
        public void Initialize()
        {
            Debug.Log("🖥️ Initializing UIManager...");
            
            // Obtenir les références
            resourceManager = FindObjectOfType<ResourceManager>();
            upgradeManager = FindObjectOfType<UpgradeManager>();
            
            // Initialiser les dictionnaires
            upgradeButtons = new Dictionary<string, GameObject>();
            
            // Configurer les événements
            SetupEvents();
            
            // Configurer les boutons
            SetupButtons();
            
            // Initialiser l'UI
            RefreshAllUI();
            
            Debug.Log("✅ UIManager initialized");
        }
        
        private void SetupEvents()
        {
            // Événements des ressources
            ResourceManager.OnResourceChanged += OnResourceChanged;
            ResourceManager.OnResourceGained += OnResourceGained;
            
            // Événements des upgrades
            UpgradeManager.OnUpgradePurchased += OnUpgradePurchased;
            UpgradeManager.OnUpgradeUnlocked += OnUpgradeUnlocked;
        }
        
        private void SetupButtons()
        {
            // Boutons de modules cliquables
            if (energyModuleButton != null)
                energyModuleButton.onClick.AddListener(() => OnModuleClicked(ResourceType.Energy));
            
            if (mineralsModuleButton != null)
                mineralsModuleButton.onClick.AddListener(() => OnModuleClicked(ResourceType.Minerals));
            
            if (researchModuleButton != null)
                researchModuleButton.onClick.AddListener(() => OnModuleClicked(ResourceType.ResearchData));
            
            // Boutons de navigation
            if (upgradesTabButton != null)
                upgradesTabButton.onClick.AddListener(() => ShowPanel("upgrades"));
            
            if (researchTabButton != null)
                researchTabButton.onClick.AddListener(() => ShowPanel("research"));
            
            if (settingsTabButton != null)
                settingsTabButton.onClick.AddListener(() => ShowPanel("settings"));
            
            // Bouton de récompenses hors ligne
            if (claimOfflineRewardsButton != null)
                claimOfflineRewardsButton.onClick.AddListener(ClaimOfflineRewards);
        }
        
        #endregion
        
        #region Resource UI
        
        /// <summary>
        /// Met à jour l'affichage d'une ressource
        /// </summary>
        private void OnResourceChanged(ResourceType type, BigInteger amount)
        {
            UpdateResourceDisplay(type, amount);
            UpdateProductionDisplay();
            UpdateUpgradeButtons();
        }
        
        /// <summary>
        /// Gère l'affichage quand une ressource est gagnée
        /// </summary>
        private void OnResourceGained(ResourceType type, BigInteger amount)
        {
            // Effet visuel de gain de ressource
            ShowResourceGainEffect(type, amount);
        }
        
        /// <summary>
        /// Met à jour l'affichage d'une ressource spécifique
        /// </summary>
        private void UpdateResourceDisplay(ResourceType type, BigInteger amount)
        {
            string formattedAmount = FormatNumber(amount);
            
            switch (type)
            {
                case ResourceType.Energy:
                    if (energyText != null) energyText.text = formattedAmount;
                    break;
                    
                case ResourceType.Minerals:
                    if (mineralsText != null) mineralsText.text = formattedAmount;
                    break;
                    
                case ResourceType.ResearchData:
                    if (researchDataText != null) researchDataText.text = formattedAmount;
                    break;
                    
                case ResourceType.SpaceCurrency:
                    if (spaceCurrencyText != null) spaceCurrencyText.text = formattedAmount;
                    break;
            }
        }
        
        /// <summary>
        /// Met à jour l'affichage de la production
        /// </summary>
        private void UpdateProductionDisplay()
        {
            if (resourceManager == null) return;
            
            if (energyProductionText != null)
            {
                float rate = resourceManager.GetProductionRate(ResourceType.Energy);
                energyProductionText.text = $"+{FormatNumber(rate)}/sec";
            }
            
            if (mineralsProductionText != null)
            {
                float rate = resourceManager.GetProductionRate(ResourceType.Minerals);
                mineralsProductionText.text = $"+{FormatNumber(rate)}/sec";
            }
            
            if (researchProductionText != null)
            {
                float rate = resourceManager.GetProductionRate(ResourceType.ResearchData);
                researchProductionText.text = $"+{FormatNumber(rate)}/sec";
            }
        }
        
        #endregion
        
        #region Click Handling
        
        /// <summary>
        /// Gère le clic sur un module
        /// </summary>
        private void OnModuleClicked(ResourceType type)
        {
            if (resourceManager == null) return;
            
            // Effectuer le clic
            resourceManager.OnResourceClicked(type);
            
            // Effets visuels et sonores
            PlayClickEffect(type);
            PlayClickSound();
        }
        
        /// <summary>
        /// Joue l'effet visuel de clic
        /// </summary>
        private void PlayClickEffect(ResourceType type)
        {
            if (clickEffectPrefab == null) return;
            
            // Déterminer la position de l'effet
            Vector3 effectPosition = GetModulePosition(type);
            
            // Créer l'effet de particules
            ParticleSystem effect = Instantiate(clickEffectPrefab, effectPosition, Quaternion.identity);
            
            // Personnaliser la couleur selon le type de ressource
            var main = effect.main;
            main.startColor = GetResourceColor(type);
            
            // Détruire après l'effet
            Destroy(effect.gameObject, 2f);
        }
        
        /// <summary>
        /// Joue le son de clic
        /// </summary>
        private void PlayClickSound()
        {
            if (clickAudioSource != null)
            {
                clickAudioSource.Play();
            }
        }
        
        /// <summary>
        /// Obtient la position d'un module pour les effets
        /// </summary>
        private Vector3 GetModulePosition(ResourceType type)
        {
            switch (type)
            {
                case ResourceType.Energy:
                    return energyModuleButton != null ? energyModuleButton.transform.position : Vector3.zero;
                case ResourceType.Minerals:
                    return mineralsModuleButton != null ? mineralsModuleButton.transform.position : Vector3.zero;
                case ResourceType.ResearchData:
                    return researchModuleButton != null ? researchModuleButton.transform.position : Vector3.zero;
                default:
                    return Vector3.zero;
            }
        }
        
        /// <summary>
        /// Obtient la couleur associée à un type de ressource
        /// </summary>
        private Color GetResourceColor(ResourceType type)
        {
            switch (type)
            {
                case ResourceType.Energy:
                    return Color.yellow;
                case ResourceType.Minerals:
                    return Color.cyan;
                case ResourceType.ResearchData:
                    return Color.green;
                case ResourceType.SpaceCurrency:
                    return Color.magenta;
                default:
                    return Color.white;
            }
        }
        
        #endregion
        
        #region Upgrade UI
        
        /// <summary>
        /// Met à jour les boutons d'upgrade
        /// </summary>
        private void UpdateUpgradeButtons()
        {
            if (upgradeManager == null || upgradeContainer == null) return;
            
            var availableUpgrades = upgradeManager.GetAvailableUpgrades();
            
            // Créer ou mettre à jour les boutons d'upgrade
            foreach (var upgrade in availableUpgrades)
            {
                if (!upgradeButtons.ContainsKey(upgrade.id))
                {
                    CreateUpgradeButton(upgrade);
                }
                else
                {
                    UpdateUpgradeButton(upgrade);
                }
            }
        }
        
        /// <summary>
        /// Crée un bouton d'upgrade
        /// </summary>
        private void CreateUpgradeButton(UpgradeData upgrade)
        {
            if (upgradeButtonPrefab == null) return;
            
            GameObject buttonObj = Instantiate(upgradeButtonPrefab, upgradeContainer);
            upgradeButtons[upgrade.id] = buttonObj;
            
            // Configurer le bouton
            Button button = buttonObj.GetComponent<Button>();
            if (button != null)
            {
                button.onClick.AddListener(() => PurchaseUpgrade(upgrade.id));
            }
            
            UpdateUpgradeButton(upgrade);
        }
        
        /// <summary>
        /// Met à jour un bouton d'upgrade
        /// </summary>
        private void UpdateUpgradeButton(UpgradeData upgrade)
        {
            if (!upgradeButtons.ContainsKey(upgrade.id)) return;
            
            GameObject buttonObj = upgradeButtons[upgrade.id];
            
            // Mettre à jour le texte
            TextMeshProUGUI[] texts = buttonObj.GetComponentsInChildren<TextMeshProUGUI>();
            if (texts.Length >= 3)
            {
                texts[0].text = upgrade.name;
                texts[1].text = upgrade.description;
                texts[2].text = $"Cost: {FormatNumber(upgrade.GetNextLevelCost())} {upgrade.costType}";
            }
            
            // Mettre à jour l'état du bouton
            Button button = buttonObj.GetComponent<Button>();
            if (button != null)
            {
                button.interactable = upgradeManager.CanPurchaseUpgrade(upgrade.id);
            }
        }
        
        /// <summary>
        /// Achète un upgrade
        /// </summary>
        private void PurchaseUpgrade(string upgradeId)
        {
            if (upgradeManager != null)
            {
                upgradeManager.PurchaseUpgrade(upgradeId);
            }
        }
        
        /// <summary>
        /// Gère l'achat d'un upgrade
        /// </summary>
        private void OnUpgradePurchased(UpgradeData upgrade)
        {
            // Effet visuel d'achat
            // TODO: Ajouter des effets visuels
        }
        
        /// <summary>
        /// Gère le déverrouillage d'un upgrade
        /// </summary>
        private void OnUpgradeUnlocked(UpgradeData upgrade)
        {
            UpdateUpgradeButtons();
            // TODO: Notification de déverrouillage
        }
        
        #endregion
        
        #region Offline Rewards
        
        /// <summary>
        /// Affiche la popup de récompenses hors ligne
        /// </summary>
        public void ShowOfflineRewardsPopup(OfflineRewards rewards, float offlineSeconds)
        {
            if (offlineRewardsPopup == null) return;
            
            pendingOfflineRewards = rewards;
            
            // Formater le texte des récompenses
            string rewardsText = $"Vous étiez absent pendant {FormatTime(offlineSeconds)}\n\n";
            rewardsText += "Récompenses collectées:\n";
            
            if (rewards.energy > 0)
                rewardsText += $"⚡ Énergie: +{FormatNumber(rewards.energy)}\n";
            
            if (rewards.minerals > 0)
                rewardsText += $"💎 Minéraux: +{FormatNumber(rewards.minerals)}\n";
            
            if (rewards.researchData > 0)
                rewardsText += $"📡 Données: +{FormatNumber(rewards.researchData)}\n";
            
            if (offlineRewardsText != null)
                offlineRewardsText.text = rewardsText;
            
            offlineRewardsPopup.SetActive(true);
        }
        
        /// <summary>
        /// Récupère les récompenses hors ligne
        /// </summary>
        private void ClaimOfflineRewards()
        {
            if (offlineRewardsPopup != null)
                offlineRewardsPopup.SetActive(false);
            
            pendingOfflineRewards = null;
        }
        
        #endregion
        
        #region Navigation
        
        /// <summary>
        /// Affiche un panneau spécifique
        /// </summary>
        private void ShowPanel(string panelName)
        {
            // Cacher tous les panneaux
            if (upgradesPanel != null) upgradesPanel.SetActive(false);
            if (researchPanel != null) researchPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
            
            // Afficher le panneau demandé
            switch (panelName)
            {
                case "upgrades":
                    if (upgradesPanel != null) upgradesPanel.SetActive(true);
                    break;
                case "research":
                    if (researchPanel != null) researchPanel.SetActive(true);
                    break;
                case "settings":
                    if (settingsPanel != null) settingsPanel.SetActive(true);
                    break;
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Formate un nombre pour l'affichage
        /// </summary>
        private string FormatNumber(BigInteger number)
        {
            if (number < 1000)
                return number.ToString();
            
            double value = (double)number;
            string[] suffixes = { "", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc" };
            
            int suffixIndex = 0;
            while (value >= 1000 && suffixIndex < suffixes.Length - 1)
            {
                value /= 1000;
                suffixIndex++;
            }
            
            return $"{value:F2}{suffixes[suffixIndex]}";
        }
        
        /// <summary>
        /// Formate un nombre décimal pour l'affichage
        /// </summary>
        private string FormatNumber(float number)
        {
            return FormatNumber(new BigInteger(number));
        }
        
        /// <summary>
        /// Formate un temps en secondes
        /// </summary>
        private string FormatTime(float seconds)
        {
            if (seconds < 60)
                return $"{seconds:F0} secondes";
            
            if (seconds < 3600)
                return $"{seconds / 60:F0} minutes";
            
            return $"{seconds / 3600:F1} heures";
        }
        
        /// <summary>
        /// Affiche un effet de gain de ressource
        /// </summary>
        private void ShowResourceGainEffect(ResourceType type, BigInteger amount)
        {
            // TODO: Implémenter des effets visuels de gain
        }
        
        /// <summary>
        /// Met à jour toute l'interface
        /// </summary>
        public void RefreshAllUI()
        {
            if (resourceManager != null)
            {
                foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
                {
                    UpdateResourceDisplay(type, resourceManager.GetResource(type));
                }
                UpdateProductionDisplay();
            }
            
            UpdateUpgradeButtons();
        }
        
        #endregion
        
        #region Cleanup
        
        private void OnDestroy()
        {
            // Désabonner des événements
            ResourceManager.OnResourceChanged -= OnResourceChanged;
            ResourceManager.OnResourceGained -= OnResourceGained;
            UpgradeManager.OnUpgradePurchased -= OnUpgradePurchased;
            UpgradeManager.OnUpgradeUnlocked -= OnUpgradeUnlocked;
        }
        
        #endregion
    }
}
