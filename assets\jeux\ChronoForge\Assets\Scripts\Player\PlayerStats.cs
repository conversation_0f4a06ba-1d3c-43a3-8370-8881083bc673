using UnityEngine;
using System.Collections.Generic;
using ChronoForge.Core;

namespace ChronoForge.Player
{
    /// <summary>
    /// Système de statistiques et classes du joueur pour ChronoForge
    /// </summary>
    public class PlayerStats : MonoBehaviour
    {
        [Header("Base Stats")]
        public float baseHealth = 100f;
        public float baseAttackDamage = 10f;
        public float baseAttackSpeed = 1f;
        public float baseMoveSpeed = 8f;
        public float baseCritChance = 0.05f;
        public float baseCritMultiplier = 2f;
        
        [Header("Current Class")]
        public PlayerClass playerClass = PlayerClass.CyberWarrior;
        
        [Header("Level and Experience")]
        public int runLevel = 1;
        public int runExperience = 0;
        public int experienceToNextLevel = 100;
        
        [Header("Temporary Modifiers")]
        public List<StatModifier> activeModifiers = new List<StatModifier>();
        
        // Events
        public static System.Action<PlayerClass> OnClassChanged;
        public static System.Action<int> OnRunLevelUp;
        public static System.Action<StatType, float> OnStatChanged;
        
        // Components
        private ChronoForge.Player.HealthSystem healthSystem;
        private PlayerController playerController;
        private PlayerCombat playerCombat;
        
        // Calculated stats
        private Dictionary<StatType, float> finalStats = new Dictionary<StatType, float>();
        private ClassData currentClassData;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeStats();
        }
        
        private void Start()
        {
            ApplyClassStats();
            CalculateAllStats();
        }
        
        private void Update()
        {
            UpdateModifiers();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeStats()
        {
            // Get components
            healthSystem = GetComponent<ChronoForge.Player.HealthSystem>();
            playerController = GetComponent<PlayerController>();
            playerCombat = GetComponent<PlayerCombat>();
            
            // Initialize stats dictionary
            InitializeStatsDictionary();
            
            // Load class data
            LoadClassData();
            
            Debug.Log($"📊 PlayerStats initialized - Class: {playerClass}");
        }
        
        private void InitializeStatsDictionary()
        {
            finalStats[StatType.Health] = baseHealth;
            finalStats[StatType.AttackDamage] = baseAttackDamage;
            finalStats[StatType.AttackSpeed] = baseAttackSpeed;
            finalStats[StatType.MoveSpeed] = baseMoveSpeed;
            finalStats[StatType.CritChance] = baseCritChance;
            finalStats[StatType.CritMultiplier] = baseCritMultiplier;
            finalStats[StatType.Defense] = 0f;
            finalStats[StatType.LifeSteal] = 0f;
            finalStats[StatType.DashCooldown] = 1f;
            finalStats[StatType.SpecialCooldown] = 5f;
        }
        
        #endregion
        
        #region Class System
        
        public void ChangeClass(PlayerClass newClass)
        {
            if (playerClass == newClass) return;
            
            playerClass = newClass;
            LoadClassData();
            ApplyClassStats();
            CalculateAllStats();
            
            OnClassChanged?.Invoke(playerClass);
            
            Debug.Log($"🎭 Class changed to: {playerClass}");
        }
        
        private void LoadClassData()
        {
            currentClassData = GetClassData(playerClass);
        }
        
        private ClassData GetClassData(PlayerClass classType)
        {
            switch (classType)
            {
                case PlayerClass.CyberWarrior:
                    return new ClassData
                    {
                        className = "Cyber Warrior",
                        description = "Guerrier cybernétique équilibré avec de bonnes capacités de survie",
                        healthMultiplier = 1.2f,
                        attackMultiplier = 1.0f,
                        speedMultiplier = 1.0f,
                        specialAbility = "Bouclier Énergétique",
                        ultimateAbility = "Surcharge Cybernétique",
                        passiveBonus = "Régénération de bouclier"
                    };
                    
                case PlayerClass.TechnoMage:
                    return new ClassData
                    {
                        className = "Techno Mage",
                        description = "Mage technologique avec des attaques à distance et contrôle",
                        healthMultiplier = 0.8f,
                        attackMultiplier = 1.3f,
                        speedMultiplier = 0.9f,
                        specialAbility = "Orbe Quantique",
                        ultimateAbility = "Tempête Arcane",
                        passiveBonus = "Régénération de mana"
                    };
                    
                case PlayerClass.DimensionalRogue:
                    return new ClassData
                    {
                        className = "Dimensional Rogue",
                        description = "Rôdeur dimensionnel rapide avec capacités de téléportation",
                        healthMultiplier = 0.9f,
                        attackMultiplier = 1.1f,
                        speedMultiplier = 1.3f,
                        specialAbility = "Téléportation",
                        ultimateAbility = "Faille Dimensionnelle",
                        passiveBonus = "Esquive améliorée"
                    };
                    
                default:
                    return GetClassData(PlayerClass.CyberWarrior);
            }
        }
        
        private void ApplyClassStats()
        {
            if (currentClassData == null) return;
            
            // Apply class multipliers to base stats
            finalStats[StatType.Health] = baseHealth * currentClassData.healthMultiplier;
            finalStats[StatType.AttackDamage] = baseAttackDamage * currentClassData.attackMultiplier;
            finalStats[StatType.MoveSpeed] = baseMoveSpeed * currentClassData.speedMultiplier;
            
            // Apply class-specific bonuses
            ApplyClassSpecificBonuses();
        }
        
        private void ApplyClassSpecificBonuses()
        {
            switch (playerClass)
            {
                case PlayerClass.CyberWarrior:
                    finalStats[StatType.Defense] += 5f;
                    finalStats[StatType.DashCooldown] *= 0.9f; // Faster dash
                    break;
                    
                case PlayerClass.TechnoMage:
                    finalStats[StatType.CritChance] += 0.1f; // +10% crit chance
                    finalStats[StatType.SpecialCooldown] *= 0.8f; // Faster special
                    break;
                    
                case PlayerClass.DimensionalRogue:
                    finalStats[StatType.CritMultiplier] += 0.5f; // Higher crit damage
                    finalStats[StatType.DashCooldown] *= 0.7f; // Much faster dash
                    break;
            }
        }
        
        #endregion
        
        #region Level System
        
        public void AddRunExperience(int amount)
        {
            runExperience += amount;
            
            while (runExperience >= experienceToNextLevel)
            {
                LevelUpInRun();
            }
        }
        
        private void LevelUpInRun()
        {
            runExperience -= experienceToNextLevel;
            runLevel++;
            
            // Increase experience requirement
            experienceToNextLevel = Mathf.RoundToInt(100 * Mathf.Pow(1.1f, runLevel - 1));
            
            // Apply level up bonuses
            ApplyLevelUpBonuses();
            
            OnRunLevelUp?.Invoke(runLevel);
            
            Debug.Log($"🎉 Run level up! Now level {runLevel}");
        }
        
        private void ApplyLevelUpBonuses()
        {
            // Increase base stats slightly each level
            AddStatModifier(new StatModifier
            {
                statType = StatType.Health,
                value = 5f,
                type = ModifierType.Flat,
                duration = -1f, // Permanent for this run
                source = "Level Up"
            });
            
            AddStatModifier(new StatModifier
            {
                statType = StatType.AttackDamage,
                value = 1f,
                type = ModifierType.Flat,
                duration = -1f,
                source = "Level Up"
            });
        }
        
        #endregion
        
        #region Stat Modifiers
        
        public void AddStatModifier(StatModifier modifier)
        {
            activeModifiers.Add(modifier);
            CalculateAllStats();
            
            Debug.Log($"📈 Added modifier: {modifier.statType} {modifier.value} from {modifier.source}");
        }
        
        public void RemoveStatModifier(StatModifier modifier)
        {
            activeModifiers.Remove(modifier);
            CalculateAllStats();
        }
        
        public void RemoveModifiersFromSource(string source)
        {
            activeModifiers.RemoveAll(m => m.source == source);
            CalculateAllStats();
        }
        
        private void UpdateModifiers()
        {
            // Update modifier durations
            for (int i = activeModifiers.Count - 1; i >= 0; i--)
            {
                if (activeModifiers[i].duration > 0)
                {
                    activeModifiers[i].duration -= Time.deltaTime;
                    
                    if (activeModifiers[i].duration <= 0)
                    {
                        activeModifiers.RemoveAt(i);
                        CalculateAllStats();
                    }
                }
            }
        }
        
        #endregion
        
        #region Stat Calculation
        
        private void CalculateAllStats()
        {
            // Reset to base + class values
            ApplyClassStats();
            
            // Apply all modifiers
            foreach (var modifier in activeModifiers)
            {
                ApplyModifier(modifier);
            }
            
            // Apply calculated stats to components
            ApplyStatsToComponents();
            
            // Notify stat changes
            NotifyStatChanges();
        }
        
        private void ApplyModifier(StatModifier modifier)
        {
            if (!finalStats.ContainsKey(modifier.statType)) return;
            
            switch (modifier.type)
            {
                case ModifierType.Flat:
                    finalStats[modifier.statType] += modifier.value;
                    break;
                    
                case ModifierType.Percentage:
                    finalStats[modifier.statType] *= (1f + modifier.value);
                    break;
                    
                case ModifierType.Multiplicative:
                    finalStats[modifier.statType] *= modifier.value;
                    break;
            }
        }
        
        private void ApplyStatsToComponents()
        {
            // Apply to health system
            if (healthSystem != null)
            {
                healthSystem.SetMaxHealth(finalStats[StatType.Health]);
            }
            
            // Apply to player controller
            if (playerController != null)
            {
                playerController.moveSpeed = finalStats[StatType.MoveSpeed];
                playerController.dashCooldown = finalStats[StatType.DashCooldown];
            }
            
            // Apply to combat system
            if (playerCombat != null)
            {
                playerCombat.attackCooldown = 1f / finalStats[StatType.AttackSpeed];
                playerCombat.specialCooldown = finalStats[StatType.SpecialCooldown];
            }
        }
        
        private void NotifyStatChanges()
        {
            foreach (var stat in finalStats)
            {
                OnStatChanged?.Invoke(stat.Key, stat.Value);
            }
        }
        
        #endregion
        
        #region Abilities
        
        public void UseUltimateAbility()
        {
            switch (playerClass)
            {
                case PlayerClass.CyberWarrior:
                    CyberWarriorUltimate();
                    break;
                    
                case PlayerClass.TechnoMage:
                    TechnoMageUltimate();
                    break;
                    
                case PlayerClass.DimensionalRogue:
                    DimensionalRogueUltimate();
                    break;
            }
        }
        
        private void CyberWarriorUltimate()
        {
            // Surcharge Cybernétique: Boost temporaire de toutes les stats
            AddStatModifier(new StatModifier
            {
                statType = StatType.AttackDamage,
                value = 0.5f,
                type = ModifierType.Percentage,
                duration = 10f,
                source = "Cyber Surge"
            });
            
            AddStatModifier(new StatModifier
            {
                statType = StatType.AttackSpeed,
                value = 0.3f,
                type = ModifierType.Percentage,
                duration = 10f,
                source = "Cyber Surge"
            });
            
            Debug.Log("⚡ Cyber Surge activated!");
        }
        
        private void TechnoMageUltimate()
        {
            // Tempête Arcane: Dégâts massifs dans une grande zone
            Debug.Log("🌪️ Arcane Storm unleashed!");
        }
        
        private void DimensionalRogueUltimate()
        {
            // Faille Dimensionnelle: Téléportation avec dégâts
            Debug.Log("🌀 Dimensional Rift opened!");
        }
        
        #endregion
        
        #region Public Getters
        
        public float GetStat(StatType statType)
        {
            return finalStats.ContainsKey(statType) ? finalStats[statType] : 0f;
        }
        
        public float GetAttackDamage()
        {
            return GetStat(StatType.AttackDamage);
        }
        
        public float GetHealth()
        {
            return GetStat(StatType.Health);
        }
        
        public float GetMoveSpeed()
        {
            return GetStat(StatType.MoveSpeed);
        }
        
        public float GetCritChance()
        {
            return GetStat(StatType.CritChance);
        }
        
        public float GetCritMultiplier()
        {
            return GetStat(StatType.CritMultiplier);
        }
        
        public ClassData GetCurrentClassData()
        {
            return currentClassData;
        }
        
        public void ResetForNewRun()
        {
            runLevel = 1;
            runExperience = 0;
            experienceToNextLevel = 100;
            
            activeModifiers.Clear();
            CalculateAllStats();
            
            Debug.Log("🔄 Player stats reset for new run");
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types de statistiques
    /// </summary>
    public enum StatType
    {
        Health,
        AttackDamage,
        AttackSpeed,
        MoveSpeed,
        CritChance,
        CritMultiplier,
        Defense,
        LifeSteal,
        DashCooldown,
        SpecialCooldown
    }
    
    /// <summary>
    /// Classes de joueur disponibles
    /// </summary>
    public enum PlayerClass
    {
        CyberWarrior,
        TechnoMage,
        DimensionalRogue
    }
    
    /// <summary>
    /// Types de modificateurs
    /// </summary>
    public enum ModifierType
    {
        Flat,           // +10
        Percentage,     // +50% (multiply by 1.5)
        Multiplicative  // x2 (multiply by 2)
    }
    
    /// <summary>
    /// Modificateur de statistique
    /// </summary>
    [System.Serializable]
    public class StatModifier
    {
        public StatType statType;
        public float value;
        public ModifierType type;
        public float duration; // -1 for permanent
        public string source;
    }
    
    /// <summary>
    /// Données d'une classe
    /// </summary>
    [System.Serializable]
    public class ClassData
    {
        public string className;
        public string description;
        public float healthMultiplier;
        public float attackMultiplier;
        public float speedMultiplier;
        public string specialAbility;
        public string ultimateAbility;
        public string passiveBonus;
    }
}
