<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API2CSV Demo - Interactive Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .demo-step {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease-out;
        }
        .demo-step.active {
            opacity: 1;
            transform: translateY(0);
        }
        .typing {
            border-right: 2px solid #3b82f6;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { border-color: #3b82f6; }
            51%, 100% { border-color: transparent; }
        }
        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto p-6">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">🔄 API2CSV Demo</h1>
            <p class="text-lg text-gray-600">Watch how JSON transforms into CSV in seconds</p>
            <div class="mt-4">
                <button id="startDemo" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    ▶️ Start Demo
                </button>
                <button id="resetDemo" class="bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors ml-4">
                    🔄 Reset
                </button>
            </div>
        </div>

        <!-- Demo Container -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- JSON Input Side -->
            <div class="space-y-4">
                <div class="demo-step" id="step1">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Step 1: Paste Complex JSON</h3>
                    <div class="bg-white rounded-lg border-2 border-gray-300 p-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">JSON Input</label>
                        <textarea id="jsonInput" 
                                  class="w-full h-64 p-3 border border-gray-300 rounded-lg font-mono text-sm resize-none"
                                  placeholder="Complex JSON will appear here..."></textarea>
                    </div>
                </div>

                <div class="demo-step" id="step2">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Step 2: Convert to CSV</h3>
                    <button id="convertBtn" 
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors pulse-glow">
                        🔄 Convert to CSV
                    </button>
                </div>
            </div>

            <!-- CSV Output Side -->
            <div class="space-y-4">
                <div class="demo-step" id="step3">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Step 3: Preview CSV Table</h3>
                    <div class="bg-white rounded-lg border-2 border-gray-300 p-4">
                        <div id="loadingState" class="hidden text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p class="text-gray-600">Converting JSON to CSV...</p>
                        </div>
                        
                        <div id="csvPreview" class="hidden">
                            <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                                <p class="text-green-800 text-sm font-medium">✅ Conversion successful: <span id="statsText">3 rows, 15 columns</span></p>
                            </div>
                            <div class="overflow-auto max-h-64">
                                <table id="csvTable" class="w-full text-xs border-collapse">
                                    <thead class="bg-gray-50">
                                        <tr id="csvHeader"></tr>
                                    </thead>
                                    <tbody id="csvBody"></tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div id="emptyState" class="text-center py-8 text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p>CSV preview will appear here</p>
                        </div>
                    </div>
                </div>

                <div class="demo-step" id="step4">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Step 4: Download CSV</h3>
                    <button id="downloadBtn" 
                            class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" 
                            disabled>
                        📥 Download CSV File
                    </button>
                </div>
            </div>
        </div>

        <!-- Viral Message -->
        <div class="demo-step mt-12 text-center" id="step5">
            <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-xl font-semibold text-green-900 mb-2">
                    💡 Share with a developer who struggles with JSON
                </h3>
                <p class="text-green-800 mb-4">Help your colleagues discover this free tool</p>
                <div class="flex justify-center space-x-4">
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
                        📤 Share Tool
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        🔗 Copy URL
                    </button>
                    <a href="https://github.com/NeethDseven/api2csv" class="bg-gray-800 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-900 transition-colors">
                        ⭐ Star on GitHub
                    </a>
                </div>
            </div>
        </div>

        <!-- Instructions for GIF Creation -->
        <div class="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-900 mb-4">📹 GIF Creation Instructions</h3>
            <ol class="list-decimal list-inside text-yellow-800 space-y-2 text-sm">
                <li>Use this demo page as reference for your screen recording</li>
                <li>Record at 1200x800 resolution, 12-15 FPS</li>
                <li>Follow the step-by-step sequence shown above</li>
                <li>Keep total duration under 30 seconds</li>
                <li>End with the viral sharing message</li>
                <li>Export as GIF under 5MB for social media</li>
            </ol>
        </div>
    </div>

    <script>
        const demoData = {
            "users": [
                {
                    "id": 1,
                    "profile": {
                        "name": "Alice Johnson",
                        "contact": {
                            "email": "<EMAIL>",
                            "phone": "******-0123",
                            "address": {
                                "street": "123 Main St",
                                "city": "San Francisco",
                                "country": "USA"
                            }
                        },
                        "preferences": {
                            "theme": "dark",
                            "notifications": true,
                            "language": "en"
                        }
                    },
                    "stats": {
                        "posts": 42,
                        "followers": 1337,
                        "following": 256
                    }
                },
                {
                    "id": 2,
                    "profile": {
                        "name": "Bob Wilson",
                        "contact": {
                            "email": "<EMAIL>",
                            "phone": "******-0456",
                            "address": {
                                "street": "456 Tech Ave",
                                "city": "Austin",
                                "country": "USA"
                            }
                        },
                        "preferences": {
                            "theme": "light",
                            "notifications": false,
                            "language": "en"
                        }
                    },
                    "stats": {
                        "posts": 28,
                        "followers": 892,
                        "following": 145
                    }
                },
                {
                    "id": 3,
                    "profile": {
                        "name": "Charlie Davis",
                        "contact": {
                            "email": "<EMAIL>",
                            "phone": "******-0789",
                            "address": {
                                "street": "789 Innovation Blvd",
                                "city": "Seattle",
                                "country": "USA"
                            }
                        },
                        "preferences": {
                            "theme": "auto",
                            "notifications": true,
                            "language": "fr"
                        }
                    },
                    "stats": {
                        "posts": 67,
                        "followers": 2156,
                        "following": 432
                    }
                }
            ],
            "metadata": {
                "total": 3,
                "page": 1,
                "per_page": 10,
                "timestamp": "2025-01-20T15:30:00Z",
                "api_version": "v2.1"
            }
        };

        let currentStep = 0;
        const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];

        function showStep(stepIndex) {
            if (stepIndex < steps.length) {
                document.getElementById(steps[stepIndex]).classList.add('active');
            }
        }

        function resetDemo() {
            steps.forEach(step => {
                document.getElementById(step).classList.remove('active');
            });
            document.getElementById('jsonInput').value = '';
            document.getElementById('csvPreview').classList.add('hidden');
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('emptyState').classList.remove('hidden');
            document.getElementById('downloadBtn').disabled = true;
            currentStep = 0;
        }

        async function typeJSON() {
            const textarea = document.getElementById('jsonInput');
            const jsonString = JSON.stringify(demoData, null, 2);
            textarea.classList.add('typing');
            
            for (let i = 0; i <= jsonString.length; i++) {
                textarea.value = jsonString.substring(0, i);
                await new Promise(resolve => setTimeout(resolve, 20));
            }
            
            textarea.classList.remove('typing');
        }

        function flattenObject(obj, prefix = '') {
            const flattened = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const newKey = prefix ? `${prefix}.${key}` : key;
                    const value = obj[key];
                    
                    if (value === null || value === undefined) {
                        flattened[newKey] = '';
                    } else if (Array.isArray(value)) {
                        if (value.every(item => typeof item !== 'object')) {
                            flattened[newKey] = value.join('; ');
                        } else {
                            value.forEach((item, index) => {
                                if (typeof item === 'object') {
                                    const subFlattened = flattenObject(item, `${newKey}[${index}]`);
                                    Object.assign(flattened, subFlattened);
                                } else {
                                    flattened[`${newKey}[${index}]`] = item;
                                }
                            });
                        }
                    } else if (typeof value === 'object') {
                        const subFlattened = flattenObject(value, newKey);
                        Object.assign(flattened, subFlattened);
                    } else {
                        flattened[newKey] = value;
                    }
                }
            }
            return flattened;
        }

        function convertToCSV() {
            const data = demoData.users.map(user => flattenObject(user));
            const columns = Object.keys(data[0]);
            
            // Show loading
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('loadingState').classList.remove('hidden');
            
            setTimeout(() => {
                // Hide loading, show results
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('csvPreview').classList.remove('hidden');
                
                // Create table header
                const header = document.getElementById('csvHeader');
                header.innerHTML = columns.map(col => 
                    `<th class="border border-gray-300 px-2 py-1 bg-gray-50 text-left text-xs font-medium">${col}</th>`
                ).join('');
                
                // Create table body
                const body = document.getElementById('csvBody');
                body.innerHTML = data.map(row => 
                    `<tr>${columns.map(col => 
                        `<td class="border border-gray-300 px-2 py-1 text-xs">${row[col] || ''}</td>`
                    ).join('')}</tr>`
                ).join('');
                
                // Update stats
                document.getElementById('statsText').textContent = `${data.length} rows, ${columns.length} columns`;
                
                // Enable download
                document.getElementById('downloadBtn').disabled = false;
                
                // Show next step
                setTimeout(() => showStep(3), 500);
                setTimeout(() => showStep(4), 2000);
            }, 2000);
        }

        // Event listeners
        document.getElementById('startDemo').addEventListener('click', async () => {
            resetDemo();
            showStep(0);
            
            setTimeout(async () => {
                await typeJSON();
                setTimeout(() => {
                    showStep(1);
                    setTimeout(() => showStep(2), 1000);
                }, 1000);
            }, 1000);
        });

        document.getElementById('resetDemo').addEventListener('click', resetDemo);
        document.getElementById('convertBtn').addEventListener('click', convertToCSV);
        
        document.getElementById('downloadBtn').addEventListener('click', () => {
            alert('🎉 CSV file downloaded! (Demo simulation)');
        });

        // Initialize
        resetDemo();
    </script>
</body>
</html>
