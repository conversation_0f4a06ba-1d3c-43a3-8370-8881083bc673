<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convert JSON to CSV Online - Free Tool | API2CSV</title>
    <meta name="description" content="Paste your JSON and download it as CSV instantly. 100% free and no signup required. Works entirely in your browser. Perfect for developers and data analysts.">

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="JSON to CSV online, convert API response to CSV, free JSON parser, online JSON to Excel, JSON converter, CSV generator, developer tools">
    <meta name="author" content="API2CSV">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://neethdseven.github.io/api2csv/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://neethdseven.github.io/api2csv/">
    <meta property="og:title" content="API2CSV - Convertisseur J<PERSON><PERSON> vers CSV Gratuit">
    <meta property="og:description" content="Convertissez instantanément vos réponses JSON d'API en fichiers CSV téléchargeables. Outil gratuit et sécurisé.">
    <meta property="og:image" content="https://neethdseven.github.io/api2csv/assets/og-image.png">
    <meta property="og:site_name" content="API2CSV">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://neethdseven.github.io/api2csv/">
    <meta property="twitter:title" content="API2CSV - Convertisseur JSON vers CSV Gratuit">
    <meta property="twitter:description" content="Convertissez instantanément vos réponses JSON d'API en fichiers CSV téléchargeables. Outil gratuit et sécurisé.">
    <meta property="twitter:image" content="https://neethdseven.github.io/api2csv/assets/twitter-image.png">

    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "API2CSV",
        "description": "Outil gratuit pour convertir JSON en CSV",
        "url": "https://neethdseven.github.io/api2csv/",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "EUR"
        },
        "creator": {
            "@type": "Organization",
            "name": "API2CSV"
        }
    }
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- PapaParse pour la conversion CSV -->
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    
    <!-- Configuration Tailwind pour le mode sombre -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="text-3xl">🔄</div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">API2CSV</h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Convert JSON to CSV instantly</p>
                    </div>
                </div>
                
                <!-- Bouton mode sombre -->
                <button id="darkModeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Bandeau viral -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-center space-x-4 text-sm">
                <span class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span id="usageCounter">2,847 developers used this tool this week</span>
                </span>
                <span class="hidden sm:block">•</span>
                <a href="https://github.com/NeethDseven/api2csv" class="hover:underline flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span>⭐ Star on GitHub</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Paste JSON → Download CSV
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                Convert API responses to CSV in seconds. No signup, no coding required.
            </p>
        </div>

        <!-- Instructions -->
        <div class="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div class="flex items-center justify-center space-x-8 text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">1</div>
                    <span class="text-blue-900 dark:text-blue-100">Paste JSON</span>
                </div>
                <div class="text-blue-400">→</div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">2</div>
                    <span class="text-blue-900 dark:text-blue-100">Preview Table</span>
                </div>
                <div class="text-blue-400">→</div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">3</div>
                    <span class="text-blue-900 dark:text-blue-100">Download CSV</span>
                </div>
            </div>
        </div>

        <!-- Zone de saisie JSON -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input JSON -->
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <label for="jsonInput" class="block text-lg font-medium text-gray-900 dark:text-white">
                        Réponse JSON
                    </label>
                    <button id="clearJson" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        Effacer
                    </button>
                </div>
                
                <textarea 
                    id="jsonInput" 
                    placeholder="Collez votre JSON ici... (Ctrl+V pour coller automatiquement)"
                    class="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                ></textarea>
                
                <!-- Options -->
                <div class="space-y-3">
                    <label class="flex items-center space-x-2">
                        <input type="checkbox" id="flattenObjects" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <span class="text-sm text-gray-700 dark:text-gray-300">Aplatir les objets imbriqués (a.b.c)</span>
                    </label>
                    
                    <label class="flex items-center space-x-2">
                        <input type="checkbox" id="includeArrayIndex" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <span class="text-sm text-gray-700 dark:text-gray-300">Inclure les index des tableaux</span>
                    </label>
                </div>
                
                <!-- Exemples JSON -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Exemples JSON :
                    </label>
                    <div class="flex flex-wrap gap-2">
                        <button
                            onclick="loadExample('simple')"
                            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        >
                            Objet simple
                        </button>
                        <button
                            onclick="loadExample('array')"
                            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        >
                            Tableau d'objets
                        </button>
                        <button
                            onclick="loadExample('nested')"
                            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        >
                            Objets imbriqués
                        </button>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="flex space-x-3">
                    <button
                        id="convertBtn"
                        class="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        aria-label="Convertir le JSON en format CSV"
                    >
                        🔄 Convertir en CSV
                    </button>

                    <button
                        id="downloadBtn"
                        class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled
                        aria-label="Télécharger le fichier CSV généré"
                    >
                        📥 Télécharger CSV
                    </button>
                </div>
            </div>

            <!-- Aperçu du résultat -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Aperçu du CSV</h3>
                
                <!-- Zone d'erreur -->
                <div id="errorMessage" class="hidden bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex items-start space-x-2">
                        <svg class="w-5 h-5 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <h4 class="text-red-800 dark:text-red-200 font-medium">Erreur de conversion</h4>
                            <p id="errorText" class="text-red-700 dark:text-red-300 text-sm mt-1"></p>
                        </div>
                    </div>
                </div>
                
                <!-- Statistiques -->
                <div id="statsContainer" class="hidden bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span id="statsText" class="text-green-800 dark:text-green-200 text-sm font-medium"></span>
                    </div>
                </div>
                
                <!-- Tableau d'aperçu -->
                <div class="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 overflow-hidden">
                    <div id="previewContainer" class="max-h-96 overflow-auto">
                        <div id="emptyState" class="p-8 text-center text-gray-500 dark:text-gray-400">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-lg font-medium mb-2">Aucun aperçu disponible</p>
                            <p class="text-sm">Collez votre JSON et cliquez sur "Convertir" pour voir l'aperçu</p>
                        </div>
                        
                        <table id="previewTable" class="hidden w-full text-sm">
                            <thead id="tableHeader" class="bg-gray-50 dark:bg-gray-700"></thead>
                            <tbody id="tableBody" class="divide-y divide-gray-200 dark:divide-gray-600"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Call to Action Viral -->
    <div class="mt-16 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mx-4 sm:mx-6 lg:mx-8">
        <div class="text-center">
            <h3 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
                💡 Share with a developer who struggles with JSON
            </h3>
            <p class="text-green-800 dark:text-green-200 text-sm mb-4">
                Help your colleagues discover this free tool
            </p>
            <div class="flex justify-center space-x-4">
                <button id="shareBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    📤 Share Tool
                </button>
                <button id="copyUrlBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    🔗 Copy URL
                </button>
                <a href="https://github.com/NeethDseven/api2csv" class="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    ⭐ Star on GitHub
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Free and open-source tool to convert JSON to CSV
                </p>
                <div class="flex justify-center space-x-6 text-sm mb-4">
                    <a href="https://github.com/NeethDseven/api2csv" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">GitHub</a>
                    <a href="https://github.com/NeethDseven/api2csv/issues" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">Report Bug</a>
                    <a href="https://github.com/sponsors/NeethDseven" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">☕ Sponsor</a>
                </div>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    Made with ❤️ for the developer community • Add to bookmarks for your next API project
                </p>
            </div>
        </div>
    </footer>

    <!-- Script principal -->
    <script src="script.js"></script>
</body>
</html>
