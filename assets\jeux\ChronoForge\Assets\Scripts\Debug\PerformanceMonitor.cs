using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace ChronoForge.Debug
{
    /// <summary>
    /// Moniteur de performance pour ChronoForge
    /// </summary>
    public class PerformanceMonitor : MonoBehaviour
    {
        [Header("Display Settings")]
        public bool showFPS = true;
        public bool showMemory = true;
        public bool showDrawCalls = true;
        public bool showDetailedStats = false;
        public KeyCode toggleKey = KeyCode.F3;
        
        [Header("FPS Settings")]
        public int fpsHistorySize = 60;
        public Color goodFPSColor = Color.green;
        public Color averageFPSColor = Color.yellow;
        public Color badFPSColor = Color.red;
        
        [Header("Memory Settings")]
        public bool trackGCAllocations = true;
        public float memoryUpdateInterval = 1f;
        
        [Header("Performance Thresholds")]
        public float goodFPSThreshold = 50f;
        public float averageFPSThreshold = 30f;
        public long memoryWarningThreshold = 500 * 1024 * 1024; // 500MB
        
        // Performance data
        private Queue<float> fpsHistory = new Queue<float>();
        private float deltaTime = 0f;
        private float lastMemoryUpdate = 0f;
        private long lastGCMemory = 0;
        private long currentMemory = 0;
        private int drawCalls = 0;
        private int triangles = 0;
        private int vertices = 0;
        
        // UI
        private GUIStyle labelStyle;
        private GUIStyle headerStyle;
        private bool isVisible = false;
        
        // Performance warnings
        private List<PerformanceWarning> activeWarnings = new List<PerformanceWarning>();
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Initialize styles
            InitializeStyles();
            
            // Set initial visibility
            isVisible = showFPS || showMemory || showDrawCalls;
        }
        
        private void Update()
        {
            // Toggle visibility
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleVisibility();
            }
            
            if (!isVisible) return;
            
            // Update performance metrics
            UpdateFPS();
            UpdateMemory();
            UpdateRenderingStats();
            UpdatePerformanceWarnings();
        }
        
        private void OnGUI()
        {
            if (!isVisible) return;
            
            DrawPerformanceOverlay();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeStyles()
        {
            labelStyle = new GUIStyle();
            labelStyle.fontSize = 14;
            labelStyle.normal.textColor = Color.white;
            labelStyle.fontStyle = FontStyle.Normal;
            
            headerStyle = new GUIStyle();
            headerStyle.fontSize = 16;
            headerStyle.normal.textColor = Color.cyan;
            headerStyle.fontStyle = FontStyle.Bold;
        }
        
        #endregion
        
        #region Performance Tracking
        
        private void UpdateFPS()
        {
            deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
            float currentFPS = 1.0f / deltaTime;
            
            // Add to history
            fpsHistory.Enqueue(currentFPS);
            if (fpsHistory.Count > fpsHistorySize)
            {
                fpsHistory.Dequeue();
            }
            
            // Check for FPS warnings
            if (currentFPS < averageFPSThreshold)
            {
                AddPerformanceWarning(PerformanceWarningType.LowFPS, $"Low FPS: {currentFPS:F1}");
            }
        }
        
        private void UpdateMemory()
        {
            if (Time.time - lastMemoryUpdate >= memoryUpdateInterval)
            {
                lastMemoryUpdate = Time.time;
                
                // Get memory usage
                currentMemory = System.GC.GetTotalMemory(false);
                
                // Track GC allocations
                if (trackGCAllocations)
                {
                    long gcDelta = currentMemory - lastGCMemory;
                    if (gcDelta > 1024 * 1024) // 1MB allocation spike
                    {
                        AddPerformanceWarning(PerformanceWarningType.MemorySpike, $"Memory spike: {FormatBytes(gcDelta)}");
                    }
                    lastGCMemory = currentMemory;
                }
                
                // Check memory threshold
                if (currentMemory > memoryWarningThreshold)
                {
                    AddPerformanceWarning(PerformanceWarningType.HighMemory, $"High memory usage: {FormatBytes(currentMemory)}");
                }
            }
        }
        
        private void UpdateRenderingStats()
        {
            // Get rendering statistics
            drawCalls = UnityEngine.Rendering.DebugManager.instance != null ? 0 : 0; // Placeholder
            triangles = 0; // Would need to be calculated from active renderers
            vertices = 0; // Would need to be calculated from active renderers
            
            // In a real implementation, you'd use Unity's built-in profiler API
            // or custom rendering statistics collection
        }
        
        private void UpdatePerformanceWarnings()
        {
            // Remove old warnings
            activeWarnings.RemoveAll(w => Time.time - w.timestamp > 5f);
        }
        
        #endregion
        
        #region UI Drawing
        
        private void DrawPerformanceOverlay()
        {
            float yOffset = 10f;
            float lineHeight = 20f;
            
            // Background
            GUI.Box(new Rect(10, 10, 300, GetOverlayHeight()), "");
            
            // Header
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), "PERFORMANCE MONITOR", headerStyle);
            yOffset += lineHeight + 5f;
            
            // FPS
            if (showFPS)
            {
                DrawFPSInfo(ref yOffset, lineHeight);
            }
            
            // Memory
            if (showMemory)
            {
                DrawMemoryInfo(ref yOffset, lineHeight);
            }
            
            // Draw calls
            if (showDrawCalls)
            {
                DrawRenderingInfo(ref yOffset, lineHeight);
            }
            
            // Detailed stats
            if (showDetailedStats)
            {
                DrawDetailedStats(ref yOffset, lineHeight);
            }
            
            // Performance warnings
            DrawPerformanceWarnings(ref yOffset, lineHeight);
        }
        
        private void DrawFPSInfo(ref float yOffset, float lineHeight)
        {
            float currentFPS = 1.0f / deltaTime;
            float avgFPS = fpsHistory.Count > 0 ? fpsHistory.Average() : currentFPS;
            float minFPS = fpsHistory.Count > 0 ? fpsHistory.Min() : currentFPS;
            float maxFPS = fpsHistory.Count > 0 ? fpsHistory.Max() : currentFPS;
            
            // Set color based on FPS
            Color fpsColor = GetFPSColor(currentFPS);
            labelStyle.normal.textColor = fpsColor;
            
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"FPS: {currentFPS:F1} (Avg: {avgFPS:F1})", labelStyle);
            yOffset += lineHeight;
            
            if (showDetailedStats)
            {
                labelStyle.normal.textColor = Color.white;
                GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Min: {minFPS:F1} | Max: {maxFPS:F1}", labelStyle);
                yOffset += lineHeight;
            }
        }
        
        private void DrawMemoryInfo(ref float yOffset, float lineHeight)
        {
            labelStyle.normal.textColor = Color.white;
            
            string memoryText = $"Memory: {FormatBytes(currentMemory)}";
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), memoryText, labelStyle);
            yOffset += lineHeight;
            
            if (showDetailedStats)
            {
                long unityMemory = UnityEngine.Profiling.Profiler.GetTotalAllocatedMemory(0);
                GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Unity: {FormatBytes(unityMemory)}", labelStyle);
                yOffset += lineHeight;
            }
        }
        
        private void DrawRenderingInfo(ref float yOffset, float lineHeight)
        {
            labelStyle.normal.textColor = Color.white;
            
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Draw Calls: {drawCalls}", labelStyle);
            yOffset += lineHeight;
            
            if (showDetailedStats)
            {
                GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Triangles: {triangles:N0}", labelStyle);
                yOffset += lineHeight;
                GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Vertices: {vertices:N0}", labelStyle);
                yOffset += lineHeight;
            }
        }
        
        private void DrawDetailedStats(ref float yOffset, float lineHeight)
        {
            labelStyle.normal.textColor = Color.gray;
            
            // System info
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Platform: {Application.platform}", labelStyle);
            yOffset += lineHeight;
            
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"Quality: {QualitySettings.names[QualitySettings.GetQualityLevel()]}", labelStyle);
            yOffset += lineHeight;
            
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"VSync: {(QualitySettings.vSyncCount > 0 ? "On" : "Off")}", labelStyle);
            yOffset += lineHeight;
        }
        
        private void DrawPerformanceWarnings(ref float yOffset, float lineHeight)
        {
            if (activeWarnings.Count == 0) return;
            
            yOffset += 5f;
            labelStyle.normal.textColor = Color.red;
            GUI.Label(new Rect(20, yOffset, 280, lineHeight), "WARNINGS:", labelStyle);
            yOffset += lineHeight;
            
            foreach (var warning in activeWarnings.Take(3)) // Show max 3 warnings
            {
                GUI.Label(new Rect(20, yOffset, 280, lineHeight), $"• {warning.message}", labelStyle);
                yOffset += lineHeight;
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        private float GetOverlayHeight()
        {
            float height = 50f; // Header + padding
            
            if (showFPS) height += showDetailedStats ? 40f : 20f;
            if (showMemory) height += showDetailedStats ? 40f : 20f;
            if (showDrawCalls) height += showDetailedStats ? 60f : 20f;
            if (showDetailedStats) height += 60f; // System info
            
            height += activeWarnings.Count * 20f + (activeWarnings.Count > 0 ? 25f : 0f);
            
            return height;
        }
        
        private Color GetFPSColor(float fps)
        {
            if (fps >= goodFPSThreshold)
                return goodFPSColor;
            else if (fps >= averageFPSThreshold)
                return averageFPSColor;
            else
                return badFPSColor;
        }
        
        private string FormatBytes(long bytes)
        {
            if (bytes < 1024)
                return $"{bytes} B";
            else if (bytes < 1024 * 1024)
                return $"{bytes / 1024f:F1} KB";
            else if (bytes < 1024 * 1024 * 1024)
                return $"{bytes / (1024f * 1024f):F1} MB";
            else
                return $"{bytes / (1024f * 1024f * 1024f):F1} GB";
        }
        
        private void AddPerformanceWarning(PerformanceWarningType type, string message)
        {
            // Don't add duplicate warnings
            if (activeWarnings.Any(w => w.type == type && w.message == message))
                return;
            
            activeWarnings.Add(new PerformanceWarning
            {
                type = type,
                message = message,
                timestamp = Time.time
            });
            
            // Limit warning count
            if (activeWarnings.Count > 10)
            {
                activeWarnings.RemoveAt(0);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void ToggleVisibility()
        {
            isVisible = !isVisible;
        }
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
        }
        
        public float GetCurrentFPS()
        {
            return 1.0f / deltaTime;
        }
        
        public float GetAverageFPS()
        {
            return fpsHistory.Count > 0 ? fpsHistory.Average() : GetCurrentFPS();
        }
        
        public long GetCurrentMemoryUsage()
        {
            return currentMemory;
        }
        
        public bool HasPerformanceIssues()
        {
            return GetCurrentFPS() < averageFPSThreshold || currentMemory > memoryWarningThreshold;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types d'avertissements de performance
    /// </summary>
    public enum PerformanceWarningType
    {
        LowFPS,
        HighMemory,
        MemorySpike,
        HighDrawCalls,
        LongFrameTime
    }
    
    /// <summary>
    /// Avertissement de performance
    /// </summary>
    [System.Serializable]
    public class PerformanceWarning
    {
        public PerformanceWarningType type;
        public string message;
        public float timestamp;
    }
}
