using System;
using System.Collections.Generic;
using UnityEngine;
using System.Numerics;
using System.Linq;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Types d'upgrades disponibles
    /// </summary>
    public enum UpgradeType
    {
        Production,     // Augmente la production
        Efficiency,     // Améliore l'efficacité
        Click,          // Améliore les clics
        Special,        // Effets spéciaux
        Unlock          // Débloque du contenu
    }
    
    /// <summary>
    /// Données d'un upgrade
    /// </summary>
    [System.Serializable]
    public class UpgradeData
    {
        public string id;
        public string name;
        public string description;
        public UpgradeType type;
        public ResourceType costType;
        public BigInteger baseCost;
        public float costMultiplier = 1.15f;
        public int currentLevel = 0;
        public int maxLevel = 100;
        public float effectValue;
        public ResourceType targetResource;
        public bool isUnlocked = false;
        public string[] prerequisites;
        
        /// <summary>
        /// Calcule le coût pour le niveau suivant
        /// </summary>
        public BigInteger GetNextLevelCost()
        {
            if (currentLevel >= maxLevel) return BigInteger.Zero;
            
            double cost = (double)baseCost * Math.Pow(costMultiplier, currentLevel);
            return new BigInteger(cost);
        }
        
        /// <summary>
        /// Vérifie si l'upgrade peut être acheté
        /// </summary>
        public bool CanUpgrade()
        {
            return isUnlocked && currentLevel < maxLevel;
        }
        
        /// <summary>
        /// Obtient l'effet total actuel
        /// </summary>
        public float GetTotalEffect()
        {
            return effectValue * currentLevel;
        }
    }
    
    /// <summary>
    /// Gestionnaire des upgrades du jeu
    /// </summary>
    public class UpgradeManager : MonoBehaviour
    {
        [Header("Upgrade Settings")]
        public UpgradeData[] availableUpgrades;
        
        [Header("Debug")]
        public bool showDebugInfo = false;
        
        // Dictionnaire des upgrades par ID
        private Dictionary<string, UpgradeData> upgrades;
        
        // Références
        private ResourceManager resourceManager;
        
        // Events
        public static event Action<UpgradeData> OnUpgradePurchased;
        public static event Action<UpgradeData> OnUpgradeUnlocked;
        
        #region Initialization
        
        public void Initialize()
        {
            Debug.Log("⬆️ Initializing UpgradeManager...");
            
            resourceManager = FindObjectOfType<ResourceManager>();
            
            // Initialiser le dictionnaire des upgrades
            upgrades = new Dictionary<string, UpgradeData>();
            
            // Charger les upgrades de base
            LoadBaseUpgrades();
            
            // Déverrouiller les upgrades de départ
            UnlockInitialUpgrades();
            
            Debug.Log($"✅ UpgradeManager initialized with {upgrades.Count} upgrades");
        }
        
        private void LoadBaseUpgrades()
        {
            // Upgrades de production d'énergie
            AddUpgrade(new UpgradeData
            {
                id = "energy_production_1",
                name = "Panneau Solaire",
                description = "Augmente la production d'énergie de +0.5/sec",
                type = UpgradeType.Production,
                costType = ResourceType.Energy,
                baseCost = new BigInteger(10),
                effectValue = 0.5f,
                targetResource = ResourceType.Energy,
                isUnlocked = true
            });
            
            AddUpgrade(new UpgradeData
            {
                id = "energy_production_2",
                name = "Réacteur Fusion",
                description = "Augmente la production d'énergie de +2.0/sec",
                type = UpgradeType.Production,
                costType = ResourceType.Energy,
                baseCost = new BigInteger(100),
                effectValue = 2.0f,
                targetResource = ResourceType.Energy,
                prerequisites = new[] { "energy_production_1" }
            });
            
            // Upgrades de production de minéraux
            AddUpgrade(new UpgradeData
            {
                id = "minerals_production_1",
                name = "Extracteur Minier",
                description = "Commence la production de minéraux +0.1/sec",
                type = UpgradeType.Production,
                costType = ResourceType.Energy,
                baseCost = new BigInteger(50),
                effectValue = 0.1f,
                targetResource = ResourceType.Minerals
            });
            
            // Upgrades de clic
            AddUpgrade(new UpgradeData
            {
                id = "click_multiplier_1",
                name = "Gants Énergétiques",
                description = "Augmente la valeur des clics x2",
                type = UpgradeType.Click,
                costType = ResourceType.Energy,
                baseCost = new BigInteger(25),
                effectValue = 1.0f,
                maxLevel = 50
            });
            
            // Upgrades d'efficacité
            AddUpgrade(new UpgradeData
            {
                id = "efficiency_1",
                name = "Optimisation IA",
                description = "Augmente toute la production de +10%",
                type = UpgradeType.Efficiency,
                costType = ResourceType.ResearchData,
                baseCost = new BigInteger(10),
                effectValue = 0.1f,
                maxLevel = 20
            });
            
            // Copier depuis le tableau si défini dans l'inspecteur
            if (availableUpgrades != null)
            {
                foreach (var upgrade in availableUpgrades)
                {
                    if (!string.IsNullOrEmpty(upgrade.id))
                    {
                        upgrades[upgrade.id] = upgrade;
                    }
                }
            }
        }
        
        private void AddUpgrade(UpgradeData upgrade)
        {
            upgrades[upgrade.id] = upgrade;
        }
        
        private void UnlockInitialUpgrades()
        {
            // Déverrouiller les upgrades de base
            foreach (var upgrade in upgrades.Values)
            {
                if (upgrade.prerequisites == null || upgrade.prerequisites.Length == 0)
                {
                    upgrade.isUnlocked = true;
                }
            }
        }
        
        #endregion
        
        #region Upgrade System
        
        /// <summary>
        /// Achète un upgrade
        /// </summary>
        public bool PurchaseUpgrade(string upgradeId)
        {
            if (!upgrades.ContainsKey(upgradeId))
            {
                Debug.LogError($"Upgrade {upgradeId} not found!");
                return false;
            }
            
            UpgradeData upgrade = upgrades[upgradeId];
            
            if (!upgrade.CanUpgrade())
            {
                if (showDebugInfo)
                    Debug.Log($"Cannot upgrade {upgradeId}: not unlocked or max level reached");
                return false;
            }
            
            BigInteger cost = upgrade.GetNextLevelCost();
            
            if (!resourceManager.CanAfford(upgrade.costType, cost))
            {
                if (showDebugInfo)
                    Debug.Log($"Cannot afford upgrade {upgradeId}: need {cost} {upgrade.costType}");
                return false;
            }
            
            // Dépenser les ressources
            resourceManager.SpendResource(upgrade.costType, cost);
            
            // Appliquer l'upgrade
            upgrade.currentLevel++;
            ApplyUpgradeEffect(upgrade);
            
            // Vérifier les déverrouillages
            CheckUnlocks();
            
            OnUpgradePurchased?.Invoke(upgrade);
            
            if (showDebugInfo)
            {
                Debug.Log($"✅ Purchased {upgrade.name} level {upgrade.currentLevel}");
            }
            
            return true;
        }
        
        /// <summary>
        /// Applique l'effet d'un upgrade
        /// </summary>
        private void ApplyUpgradeEffect(UpgradeData upgrade)
        {
            switch (upgrade.type)
            {
                case UpgradeType.Production:
                    resourceManager.AddProductionBonus(upgrade.targetResource, upgrade.effectValue);
                    break;
                    
                case UpgradeType.Click:
                    float currentMultiplier = resourceManager.clickMultiplier;
                    resourceManager.SetClickMultiplier(currentMultiplier + upgrade.effectValue);
                    break;
                    
                case UpgradeType.Efficiency:
                    // Appliquer un multiplicateur global
                    ApplyGlobalMultiplier(upgrade);
                    break;
                    
                case UpgradeType.Special:
                    ApplySpecialEffect(upgrade);
                    break;
                    
                case UpgradeType.Unlock:
                    UnlockContent(upgrade);
                    break;
            }
        }
        
        private void ApplyGlobalMultiplier(UpgradeData upgrade)
        {
            // Calculer le nouveau multiplicateur global
            float totalBonus = 1f + upgrade.GetTotalEffect();
            
            // Appliquer à toutes les ressources ou à une ressource spécifique
            if (upgrade.targetResource == ResourceType.Energy) // Si Energy, appliquer à tout
            {
                foreach (ResourceType type in Enum.GetValues(typeof(ResourceType)))
                {
                    if (type != ResourceType.SpaceCurrency)
                    {
                        resourceManager.SetGlobalMultiplier(type, totalBonus);
                    }
                }
            }
            else
            {
                resourceManager.SetGlobalMultiplier(upgrade.targetResource, totalBonus);
            }
        }
        
        private void ApplySpecialEffect(UpgradeData upgrade)
        {
            // Effets spéciaux personnalisés
            switch (upgrade.id)
            {
                case "auto_clicker":
                    // TODO: Implémenter auto-clicker
                    break;
                    
                case "offline_bonus":
                    // TODO: Améliorer les gains hors ligne
                    break;
            }
        }
        
        private void UnlockContent(UpgradeData upgrade)
        {
            // Déverrouiller du nouveau contenu
            switch (upgrade.id)
            {
                case "unlock_minerals":
                    // Déverrouiller la production de minéraux
                    break;
                    
                case "unlock_research":
                    // Déverrouiller la recherche
                    break;
            }
        }
        
        #endregion
        
        #region Unlock System
        
        /// <summary>
        /// Vérifie et déverrouille les nouveaux upgrades
        /// </summary>
        private void CheckUnlocks()
        {
            foreach (var upgrade in upgrades.Values)
            {
                if (!upgrade.isUnlocked && ArePrerequisitesMet(upgrade))
                {
                    upgrade.isUnlocked = true;
                    OnUpgradeUnlocked?.Invoke(upgrade);
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"🔓 Unlocked upgrade: {upgrade.name}");
                    }
                }
            }
        }
        
        /// <summary>
        /// Vérifie si les prérequis d'un upgrade sont remplis
        /// </summary>
        private bool ArePrerequisitesMet(UpgradeData upgrade)
        {
            if (upgrade.prerequisites == null || upgrade.prerequisites.Length == 0)
                return true;
            
            foreach (string prereqId in upgrade.prerequisites)
            {
                if (!upgrades.ContainsKey(prereqId))
                    return false;
                
                UpgradeData prereq = upgrades[prereqId];
                if (prereq.currentLevel == 0)
                    return false;
            }
            
            return true;
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Obtient un upgrade par son ID
        /// </summary>
        public UpgradeData GetUpgrade(string upgradeId)
        {
            return upgrades.ContainsKey(upgradeId) ? upgrades[upgradeId] : null;
        }
        
        /// <summary>
        /// Obtient tous les upgrades disponibles
        /// </summary>
        public List<UpgradeData> GetAvailableUpgrades()
        {
            return upgrades.Values.Where(u => u.isUnlocked).ToList();
        }
        
        /// <summary>
        /// Obtient les upgrades par type
        /// </summary>
        public List<UpgradeData> GetUpgradesByType(UpgradeType type)
        {
            return upgrades.Values.Where(u => u.isUnlocked && u.type == type).ToList();
        }
        
        /// <summary>
        /// Vérifie si un upgrade peut être acheté
        /// </summary>
        public bool CanPurchaseUpgrade(string upgradeId)
        {
            if (!upgrades.ContainsKey(upgradeId))
                return false;
            
            UpgradeData upgrade = upgrades[upgradeId];
            if (!upgrade.CanUpgrade())
                return false;
            
            BigInteger cost = upgrade.GetNextLevelCost();
            return resourceManager.CanAfford(upgrade.costType, cost);
        }
        
        #endregion
        
        #region Save/Load
        
        /// <summary>
        /// Sauvegarde les données des upgrades
        /// </summary>
        public UpgradeSaveData GetSaveData()
        {
            UpgradeSaveData saveData = new UpgradeSaveData();
            saveData.upgradeLevels = new Dictionary<string, int>();
            saveData.unlockedUpgrades = new List<string>();
            
            foreach (var upgrade in upgrades.Values)
            {
                if (upgrade.currentLevel > 0)
                {
                    saveData.upgradeLevels[upgrade.id] = upgrade.currentLevel;
                }
                
                if (upgrade.isUnlocked)
                {
                    saveData.unlockedUpgrades.Add(upgrade.id);
                }
            }
            
            return saveData;
        }
        
        /// <summary>
        /// Charge les données des upgrades
        /// </summary>
        public void LoadSaveData(UpgradeSaveData saveData)
        {
            if (saveData == null) return;
            
            // Charger les niveaux
            if (saveData.upgradeLevels != null)
            {
                foreach (var kvp in saveData.upgradeLevels)
                {
                    if (upgrades.ContainsKey(kvp.Key))
                    {
                        upgrades[kvp.Key].currentLevel = kvp.Value;
                        
                        // Réappliquer les effets
                        for (int i = 0; i < kvp.Value; i++)
                        {
                            ApplyUpgradeEffect(upgrades[kvp.Key]);
                        }
                    }
                }
            }
            
            // Charger les déverrouillages
            if (saveData.unlockedUpgrades != null)
            {
                foreach (string upgradeId in saveData.unlockedUpgrades)
                {
                    if (upgrades.ContainsKey(upgradeId))
                    {
                        upgrades[upgradeId].isUnlocked = true;
                    }
                }
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Structure de sauvegarde pour les upgrades
    /// </summary>
    [System.Serializable]
    public class UpgradeSaveData
    {
        public Dictionary<string, int> upgradeLevels;
        public List<string> unlockedUpgrades;
    }
}
