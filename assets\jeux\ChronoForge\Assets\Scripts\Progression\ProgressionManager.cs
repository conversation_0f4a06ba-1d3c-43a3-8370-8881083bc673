using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using ChronoForge.Core;
using ChronoForge.Player;

namespace ChronoForge.Progression
{
    /// <summary>
    /// Gestionnaire de progression persistante pour ChronoForge
    /// </summary>
    public class ProgressionManager : MonoBehaviour
    {
        [Header("Progression Settings")]
        public int maxPlayerLevel = 100;
        public float baseExperienceRequired = 100f;
        public float experienceMultiplier = 1.2f;
        public int maxMetaCurrency = 999999;
        
        [Header("Meta Upgrades")]
        public List<MetaUpgrade> availableUpgrades = new List<MetaUpgrade>();
        public int maxUpgradeLevel = 10;
        
        [Header("Unlockables")]
        public List<UnlockableContent> unlockableContent = new List<UnlockableContent>();
        
        // Events
        public static System.Action<int> OnLevelUp;
        public static System.Action<int> OnMetaCurrencyChanged;
        public static System.Action<string> OnAchievementUnlocked;
        public static System.Action<string> OnContentUnlocked;
        public static System.Action<MetaUpgrade> OnMetaUpgradePurchased;
        
        // Player progression data
        private PlayerProgressionData progressionData;
        
        // Singleton
        public static ProgressionManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeProgressionManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            LoadProgressionData();
            CheckUnlocks();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeProgressionManager()
        {
            // Initialize progression data
            progressionData = new PlayerProgressionData();
            
            // Create default meta upgrades if none exist
            if (availableUpgrades.Count == 0)
            {
                CreateDefaultMetaUpgrades();
            }
            
            // Create default unlockable content
            if (unlockableContent.Count == 0)
            {
                CreateDefaultUnlockables();
            }
            
            UnityEngine.Debug.Log("📈 ProgressionManager initialized");
        }
        
        private void CreateDefaultMetaUpgrades()
        {
            // Health upgrades
            availableUpgrades.Add(new MetaUpgrade
            {
                id = "health_boost",
                name = "Vitality Enhancement",
                description = "Increases maximum health by 10 per level",
                category = MetaUpgradeCategory.Health,
                baseCost = 50,
                costMultiplier = 1.5f,
                maxLevel = 10,
                valuePerLevel = 10f
            });
            
            // Damage upgrades
            availableUpgrades.Add(new MetaUpgrade
            {
                id = "damage_boost",
                name = "Combat Mastery",
                description = "Increases base damage by 5% per level",
                category = MetaUpgradeCategory.Damage,
                baseCost = 75,
                costMultiplier = 1.6f,
                maxLevel = 10,
                valuePerLevel = 0.05f
            });
            
            // Speed upgrades
            availableUpgrades.Add(new MetaUpgrade
            {
                id = "speed_boost",
                name = "Neural Acceleration",
                description = "Increases movement speed by 8% per level",
                category = MetaUpgradeCategory.Speed,
                baseCost = 60,
                costMultiplier = 1.4f,
                maxLevel = 8,
                valuePerLevel = 0.08f
            });
            
            // Critical chance upgrades
            availableUpgrades.Add(new MetaUpgrade
            {
                id = "crit_chance",
                name = "Precision Targeting",
                description = "Increases critical hit chance by 2% per level",
                category = MetaUpgradeCategory.Critical,
                baseCost = 100,
                costMultiplier = 1.8f,
                maxLevel = 5,
                valuePerLevel = 0.02f
            });
            
            // Starting currency
            availableUpgrades.Add(new MetaUpgrade
            {
                id = "starting_currency",
                name = "Resource Cache",
                description = "Start runs with additional currency",
                category = MetaUpgradeCategory.Economy,
                baseCost = 80,
                costMultiplier = 2f,
                maxLevel = 5,
                valuePerLevel = 50f
            });
        }
        
        private void CreateDefaultUnlockables()
        {
            // New classes
            unlockableContent.Add(new UnlockableContent
            {
                id = "techno_mage_class",
                name = "Techno Mage",
                description = "Unlock the Techno Mage class",
                type = UnlockableType.PlayerClass,
                unlockCondition = UnlockCondition.ReachLevel,
                requiredValue = 5,
                isUnlocked = false
            });
            
            unlockableContent.Add(new UnlockableContent
            {
                id = "dimensional_rogue_class",
                name = "Dimensional Rogue",
                description = "Unlock the Dimensional Rogue class",
                type = UnlockableType.PlayerClass,
                unlockCondition = UnlockCondition.CompleteRuns,
                requiredValue = 3,
                isUnlocked = false
            });
            
            // New weapons
            unlockableContent.Add(new UnlockableContent
            {
                id = "quantum_rifle",
                name = "Quantum Rifle",
                description = "Unlock the Quantum Rifle weapon",
                type = UnlockableType.Weapon,
                unlockCondition = UnlockCondition.KillEnemies,
                requiredValue = 500,
                isUnlocked = false
            });
            
            // New biomes
            unlockableContent.Add(new UnlockableContent
            {
                id = "cyber_catacombs",
                name = "Cyber Catacombs",
                description = "Unlock the Cyber Catacombs biome",
                type = UnlockableType.Biome,
                unlockCondition = UnlockCondition.ReachLevel,
                requiredValue = 10,
                isUnlocked = false
            });
            
            // Special features
            unlockableContent.Add(new UnlockableContent
            {
                id = "hardcore_mode",
                name = "Hardcore Mode",
                description = "Unlock Hardcore difficulty mode",
                type = UnlockableType.GameMode,
                unlockCondition = UnlockCondition.CompleteRuns,
                requiredValue = 10,
                isUnlocked = false
            });
        }
        
        #endregion
        
        #region Experience and Leveling
        
        public void AddExperience(int amount)
        {
            progressionData.totalExperience += amount;
            
            // Check for level ups
            int newLevel = CalculateLevelFromExperience(progressionData.totalExperience);
            
            if (newLevel > progressionData.playerLevel)
            {
                LevelUp(newLevel);
            }
            
            SaveProgressionData();
        }
        
        private void LevelUp(int newLevel)
        {
            int oldLevel = progressionData.playerLevel;
            progressionData.playerLevel = newLevel;
            
            // Award meta currency for leveling up
            int currencyReward = (newLevel - oldLevel) * 25;
            AddMetaCurrency(currencyReward);
            
            OnLevelUp?.Invoke(newLevel);
            
            UnityEngine.Debug.Log($"🎉 Level up! Now level {newLevel} (gained {currencyReward} meta currency)");
        }
        
        private int CalculateLevelFromExperience(int totalExp)
        {
            int level = 1;
            int expRequired = 0;
            
            while (level < maxPlayerLevel)
            {
                int expForNextLevel = Mathf.RoundToInt(baseExperienceRequired * Mathf.Pow(experienceMultiplier, level - 1));
                
                if (totalExp < expRequired + expForNextLevel)
                    break;
                
                expRequired += expForNextLevel;
                level++;
            }
            
            return level;
        }
        
        public int GetExperienceForNextLevel()
        {
            if (progressionData.playerLevel >= maxPlayerLevel)
                return 0;
            
            return Mathf.RoundToInt(baseExperienceRequired * Mathf.Pow(experienceMultiplier, progressionData.playerLevel));
        }
        
        public int GetCurrentLevelExperience()
        {
            int totalExpForCurrentLevel = 0;
            
            for (int i = 1; i < progressionData.playerLevel; i++)
            {
                totalExpForCurrentLevel += Mathf.RoundToInt(baseExperienceRequired * Mathf.Pow(experienceMultiplier, i - 1));
            }
            
            return progressionData.totalExperience - totalExpForCurrentLevel;
        }
        
        #endregion
        
        #region Meta Currency
        
        public void AddMetaCurrency(int amount)
        {
            progressionData.metaCurrency = Mathf.Min(progressionData.metaCurrency + amount, maxMetaCurrency);
            OnMetaCurrencyChanged?.Invoke(progressionData.metaCurrency);
            SaveProgressionData();
        }
        
        public bool SpendMetaCurrency(int amount)
        {
            if (progressionData.metaCurrency >= amount)
            {
                progressionData.metaCurrency -= amount;
                OnMetaCurrencyChanged?.Invoke(progressionData.metaCurrency);
                SaveProgressionData();
                return true;
            }
            return false;
        }
        
        public int GetMetaCurrency()
        {
            return progressionData.metaCurrency;
        }
        
        #endregion
        
        #region Meta Upgrades
        
        public bool PurchaseMetaUpgrade(string upgradeId)
        {
            MetaUpgrade upgrade = availableUpgrades.FirstOrDefault(u => u.id == upgradeId);
            if (upgrade == null) return false;
            
            int currentLevel = GetMetaUpgradeLevel(upgradeId);
            if (currentLevel >= upgrade.maxLevel) return false;
            
            int cost = GetMetaUpgradeCost(upgradeId);
            if (!SpendMetaCurrency(cost)) return false;
            
            // Increase upgrade level
            if (progressionData.metaUpgrades.ContainsKey(upgradeId))
            {
                progressionData.metaUpgrades[upgradeId]++;
            }
            else
            {
                progressionData.metaUpgrades[upgradeId] = 1;
            }
            
            OnMetaUpgradePurchased?.Invoke(upgrade);
            SaveProgressionData();
            
            UnityEngine.Debug.Log($"💰 Purchased {upgrade.name} level {progressionData.metaUpgrades[upgradeId]}");
            return true;
        }
        
        public int GetMetaUpgradeLevel(string upgradeId)
        {
            return progressionData.metaUpgrades.ContainsKey(upgradeId) ? progressionData.metaUpgrades[upgradeId] : 0;
        }
        
        public int GetMetaUpgradeCost(string upgradeId)
        {
            MetaUpgrade upgrade = availableUpgrades.FirstOrDefault(u => u.id == upgradeId);
            if (upgrade == null) return 0;
            
            int currentLevel = GetMetaUpgradeLevel(upgradeId);
            return Mathf.RoundToInt(upgrade.baseCost * Mathf.Pow(upgrade.costMultiplier, currentLevel));
        }
        
        public float GetMetaUpgradeValue(string upgradeId)
        {
            MetaUpgrade upgrade = availableUpgrades.FirstOrDefault(u => u.id == upgradeId);
            if (upgrade == null) return 0f;
            
            int level = GetMetaUpgradeLevel(upgradeId);
            return upgrade.valuePerLevel * level;
        }
        
        public List<MetaUpgrade> GetAvailableUpgrades()
        {
            return availableUpgrades.Where(u => GetMetaUpgradeLevel(u.id) < u.maxLevel).ToList();
        }
        
        #endregion
        
        #region Statistics Tracking
        
        public void RecordRunCompletion(bool successful, int floorsReached, int enemiesKilled, float timeElapsed)
        {
            progressionData.totalRuns++;
            
            if (successful)
            {
                progressionData.successfulRuns++;
            }
            
            progressionData.totalEnemiesKilled += enemiesKilled;
            progressionData.totalPlayTime += timeElapsed;
            progressionData.bestFloorReached = Mathf.Max(progressionData.bestFloorReached, floorsReached);
            
            if (successful && (progressionData.bestRunTime == 0 || timeElapsed < progressionData.bestRunTime))
            {
                progressionData.bestRunTime = timeElapsed;
            }
            
            CheckUnlocks();
            SaveProgressionData();
        }
        
        public void RecordEnemyKill(string enemyType)
        {
            if (progressionData.enemyKillCounts.ContainsKey(enemyType))
            {
                progressionData.enemyKillCounts[enemyType]++;
            }
            else
            {
                progressionData.enemyKillCounts[enemyType] = 1;
            }
            
            CheckUnlocks();
        }
        
        public void RecordItemFound(string itemId)
        {
            if (!progressionData.itemsFound.Contains(itemId))
            {
                progressionData.itemsFound.Add(itemId);
            }
        }
        
        #endregion
        
        #region Unlockable Content
        
        private void CheckUnlocks()
        {
            foreach (var content in unlockableContent)
            {
                if (!content.isUnlocked && CheckUnlockCondition(content))
                {
                    UnlockContent(content);
                }
            }
        }
        
        private bool CheckUnlockCondition(UnlockableContent content)
        {
            switch (content.unlockCondition)
            {
                case UnlockCondition.ReachLevel:
                    return progressionData.playerLevel >= content.requiredValue;
                    
                case UnlockCondition.CompleteRuns:
                    return progressionData.successfulRuns >= content.requiredValue;
                    
                case UnlockCondition.KillEnemies:
                    return progressionData.totalEnemiesKilled >= content.requiredValue;
                    
                case UnlockCondition.PlayTime:
                    return progressionData.totalPlayTime >= content.requiredValue;
                    
                case UnlockCondition.ReachFloor:
                    return progressionData.bestFloorReached >= content.requiredValue;
                    
                default:
                    return false;
            }
        }
        
        private void UnlockContent(UnlockableContent content)
        {
            content.isUnlocked = true;
            progressionData.unlockedContent.Add(content.id);
            
            OnContentUnlocked?.Invoke(content.id);
            
            UnityEngine.Debug.Log($"🔓 Unlocked: {content.name}");
        }
        
        public bool IsContentUnlocked(string contentId)
        {
            return progressionData.unlockedContent.Contains(contentId);
        }
        
        public List<UnlockableContent> GetUnlockedContent()
        {
            return unlockableContent.Where(c => c.isUnlocked).ToList();
        }
        
        public List<UnlockableContent> GetLockedContent()
        {
            return unlockableContent.Where(c => !c.isUnlocked).ToList();
        }
        
        #endregion
        
        #region Data Persistence
        
        private void SaveProgressionData()
        {
            string json = JsonUtility.ToJson(progressionData, true);
            PlayerPrefs.SetString("ChronoForge_Progression", json);
            PlayerPrefs.Save();
        }
        
        private void LoadProgressionData()
        {
            if (PlayerPrefs.HasKey("ChronoForge_Progression"))
            {
                string json = PlayerPrefs.GetString("ChronoForge_Progression");
                progressionData = JsonUtility.FromJson<PlayerProgressionData>(json);
                
                // Update unlockable content states
                foreach (var content in unlockableContent)
                {
                    content.isUnlocked = progressionData.unlockedContent.Contains(content.id);
                }
            }
            else
            {
                progressionData = new PlayerProgressionData();
            }
            
            UnityEngine.Debug.Log($"📊 Loaded progression: Level {progressionData.playerLevel}, {progressionData.metaCurrency} currency");
        }
        
        public void ResetProgressionData()
        {
            progressionData = new PlayerProgressionData();
            
            // Reset unlockable content
            foreach (var content in unlockableContent)
            {
                content.isUnlocked = false;
            }
            
            SaveProgressionData();
            
            UnityEngine.Debug.Log("🔄 Progression data reset");
        }
        
        #endregion
        
        #region Public Getters
        
        public PlayerProgressionData GetProgressionData()
        {
            return progressionData;
        }
        
        public int GetPlayerLevel()
        {
            return progressionData.playerLevel;
        }
        
        public int GetTotalExperience()
        {
            return progressionData.totalExperience;
        }
        
        public int GetTotalRuns()
        {
            return progressionData.totalRuns;
        }
        
        public int GetSuccessfulRuns()
        {
            return progressionData.successfulRuns;
        }
        
        public float GetSuccessRate()
        {
            return progressionData.totalRuns > 0 ? (float)progressionData.successfulRuns / progressionData.totalRuns : 0f;
        }
        
        public int GetTotalEnemiesKilled()
        {
            return progressionData.totalEnemiesKilled;
        }
        
        public float GetTotalPlayTime()
        {
            return progressionData.totalPlayTime;
        }
        
        public int GetBestFloorReached()
        {
            return progressionData.bestFloorReached;
        }
        
        public float GetBestRunTime()
        {
            return progressionData.bestRunTime;
        }
        
        #endregion
    }
}
