#!/usr/bin/env python3
"""
Script pour corriger toutes les erreurs dans ChronoForge
"""

import os
import re

def fix_file(file_path):
    """Corrige un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix Debug.Log issues
        content = re.sub(r'ChronoForge\.Debug\.Log\(', 'UnityEngine.Debug.Log(', content)
        content = re.sub(r'ChronoForge\.Debug\.LogWarning\(', 'UnityEngine.Debug.LogWarning(', content)
        content = re.sub(r'ChronoForge\.Debug\.LogError\(', 'UnityEngine.Debug.LogError(', content)
        content = re.sub(r'(?<!UnityEngine\.)Debug\.Log\(', 'UnityEngine.Debug.Log(', content)
        content = re.sub(r'(?<!UnityEngine\.)Debug\.LogWarning\(', 'UnityEngine.Debug.LogWarning(', content)
        content = re.sub(r'(?<!UnityEngine\.)Debug\.LogError\(', 'UnityEngine.Debug.LogError(', content)
        
        # Fix FindObjectOfType warnings
        content = re.sub(r'FindObjectOfType<([^>]+)>\(\)', r'FindFirstObjectByType<\1>()', content)
        
        # Fix AnimationCurve issues
        content = re.sub(r'AnimationCurve\.EaseOut\(', 'AnimationCurve.EaseInOut(', content)
        content = re.sub(r'AnimationCurve\.EaseIn\(', 'AnimationCurve.EaseInOut(', content)
        
        # Fix LineRenderer color (but not Image.color)
        content = re.sub(r'LineRenderer.*\.color = ([^;]+);', r'LineRenderer.material.color = \1;', content)

        # Fix multiple .material chains
        content = re.sub(r'\.material\.material\.material\.color', '.material.color', content)
        content = re.sub(r'\.material\.material\.color', '.color', content)
        
        # Fix Gizmos
        content = re.sub(r'Gizmos\.DrawWireCircle\(', 'Gizmos.DrawWireSphere(', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
    
    return False

def main():
    """Fonction principale"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(script_dir, "Assets", "Scripts")
    
    if not os.path.exists(assets_dir):
        print(f"❌ Directory not found: {assets_dir}")
        return
    
    fixed_count = 0
    
    # Parcourir tous les fichiers .cs
    for root, dirs, files in os.walk(assets_dir):
        for file in files:
            if file.endswith('.cs'):
                file_path = os.path.join(root, file)
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} files!")

if __name__ == "__main__":
    main()
