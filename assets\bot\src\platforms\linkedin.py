"""
Connecteur LinkedIn utilisant l'API REST.
"""

import requests
import os
from typing import Dict, Any, Optional
from .base import PlatformConnector, PublicationResult
import logging

logger = logging.getLogger(__name__)

class LinkedInConnector(PlatformConnector):
    """Connecteur pour LinkedIn."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.access_token = None
        self.user_id = None
        self.max_length = config.get('max_length', 3000)
        self.base_url = "https://api.linkedin.com/v2"
        
    def authenticate(self) -> bool:
        """Authentifie avec l'API LinkedIn."""
        try:
            self.access_token = os.getenv('LINKEDIN_ACCESS_TOKEN')
            if not self.access_token:
                logger.error("Token d'accès LinkedIn manquant")
                return False
            
            # Test de connexion - récupérer le profil utilisateur
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/people/~",
                headers=headers
            )
            
            if response.status_code == 200:
                user_data = response.json()
                self.user_id = user_data.get('id')
                logger.info(f"Authentifié sur LinkedIn - User ID: {self.user_id}")
                self.authenticated = True
                return True
            else:
                logger.error(f"Échec de l'authentification LinkedIn: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur d'authentification LinkedIn: {e}")
            return False
    
    def validate_post(self, title: str, body: str, **kwargs) -> bool:
        """Valide qu'un post respecte les contraintes LinkedIn."""
        full_text = f"{title}\n\n{body}"
        
        if len(full_text) > self.max_length:
            logger.warning(f"Post LinkedIn trop long: {len(full_text)} caractères (max: {self.max_length})")
            return False
            
        return True
    
    def publish_post(self, title: str, body: str, **kwargs) -> PublicationResult:
        """Publie un post sur LinkedIn."""
        if not self.authenticated:
            return PublicationResult(
                success=False,
                platform="linkedin",
                error="Non authentifié"
            )
        
        try:
            # Préparer le contenu du post
            post_text = self._prepare_linkedin_post(title, body, kwargs.get('tags', []))
            
            # Tronquer si nécessaire
            if len(post_text) > self.max_length:
                post_text = self.truncate_text(post_text, self.max_length)
            
            # Préparer la payload
            post_data = {
                "author": f"urn:li:person:{self.user_id}",
                "lifecycleState": "PUBLISHED",
                "specificContent": {
                    "com.linkedin.ugc.ShareContent": {
                        "shareCommentary": {
                            "text": post_text
                        },
                        "shareMediaCategory": "NONE"
                    }
                },
                "visibility": {
                    "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
                }
            }
            
            # Ajouter un lien si présent
            url = self._extract_url(body)
            if url:
                post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "ARTICLE"
                post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [{
                    "status": "READY",
                    "description": {
                        "text": title
                    },
                    "originalUrl": url,
                    "title": {
                        "text": title
                    }
                }]
            
            # Publier le post
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json',
                'X-Restli-Protocol-Version': '2.0.0'
            }
            
            response = requests.post(
                f"{self.base_url}/ugcPosts",
                json=post_data,
                headers=headers
            )
            
            if response.status_code == 201:
                response_data = response.json()
                post_id = response_data.get('id', '')
                
                self.log_success(kwargs.get('post_id', 'unknown'), response_data)
                
                return PublicationResult(
                    success=True,
                    platform="linkedin",
                    post_id=post_id,
                    response_data=response_data
                )
            else:
                error_msg = f"Erreur HTTP {response.status_code}: {response.text}"
                return PublicationResult(
                    success=False,
                    platform="linkedin",
                    error=error_msg
                )
                
        except Exception as e:
            self.log_error(kwargs.get('post_id', 'unknown'), e)
            return PublicationResult(
                success=False,
                platform="linkedin",
                error=str(e)
            )
    
    def _prepare_linkedin_post(self, title: str, body: str, tags: list = None) -> str:
        """Prépare le texte du post LinkedIn."""
        # LinkedIn permet des posts plus longs, on peut inclure titre et corps
        if title.lower() in body.lower():
            post_text = body
        else:
            post_text = f"{title}\n\n{body}"
        
        # Ajouter les hashtags à la fin
        if tags:
            hashtag_text = self.format_hashtags(tags)
            post_text += f"\n\n{hashtag_text}"
        
        return post_text.strip()
    
    def _extract_url(self, text: str) -> str:
        """Extrait la première URL trouvée dans le texte."""
        import re
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text)
        return urls[0] if urls else ""
    
    def get_post_url(self, post_id: str) -> str:
        """Génère l'URL d'un post LinkedIn."""
        # LinkedIn post URLs are complex, return a generic profile URL
        return f"https://www.linkedin.com/in/{self.user_id}/"
