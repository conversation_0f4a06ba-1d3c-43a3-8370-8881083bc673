using UnityEngine;
using UnityEditor;

namespace ChronoForge.Editor
{
    public class MenuLauncher
    {
        [MenuItem("Tools/ChronoForge/🚀 Setup Wizard")]
        public static void OpenSetupWizard()
        {
            ChronoForgeSetupWizard.ShowWindow();
        }

        [MenuItem("Tools/ChronoForge/🎮 Generate Prefabs")]
        public static void OpenPrefabGenerator()
        {
            PrefabAutoGenerator.ShowWindow();
        }

        [MenuItem("Tools/ChronoForge/🎬 Setup Scene")]
        public static void OpenSceneSetup()
        {
            SceneAutoSetup.ShowWindow();
        }

        [MenuItem("Tools/ChronoForge/📖 Open Documentation")]
        public static void OpenDocumentation()
        {
            string docPath = Application.dataPath + "/../PREFAB_SETUP_GUIDE.md";
            if (System.IO.File.Exists(docPath))
            {
                Application.OpenURL("file://" + docPath);
            }
            else
            {
                Debug.Log("📖 Documentation disponible dans le projet ChronoForge");
            }
        }
    }
}
