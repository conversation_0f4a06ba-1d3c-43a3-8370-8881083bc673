using UnityEngine;
using System.Collections.Generic;

namespace ChronoForge.Procedural
{
    /// <summary>
    /// Types de salles disponibles dans ChronoForge
    /// </summary>
    public enum RoomType
    {
        Start,      // Salle de départ
        Combat,     // Salle de combat standard
        Boss,       // Salle de boss
        Treasure,   // Salle de trésor
        Shop,       // Salle de marchand
        Rest,       // Salle de repos/guérison
        Secret,     // Salle secrète
        Event,      // Salle d'événement narratif
        Challenge,  // Salle de défi spécial
        Exit        // Salle de sortie
    }
    
    /// <summary>
    /// Données d'une salle générée
    /// </summary>
    [System.Serializable]
    public class RoomData
    {
        [Header("Position")]
        public Vector2Int gridPosition;
        public Vector3 worldPosition;
        
        [Header("Room Info")]
        public RoomType roomType;
        public string roomName;
        public RoomTemplate template;
        
        [Header("State")]
        public bool isVisited = false;
        public bool isCleared = false;
        public bool isLocked = false;
        public bool hasSecrets = false;
        
        [Header("Connections")]
        public List<Vector2Int> connections = new List<Vector2Int>();
        public List<DoorData> doors = new List<DoorData>();
        
        [Header("Contents")]
        public List<EnemySpawnData> enemies = new List<EnemySpawnData>();
        public List<ItemSpawnData> items = new List<ItemSpawnData>();
        public List<InteractableData> interactables = new List<InteractableData>();
        
        [Header("Modifiers")]
        public float difficultyMultiplier = 1f;
        public List<RoomModifier> modifiers = new List<RoomModifier>();
        
        /// <summary>
        /// Obtient la description de la salle
        /// </summary>
        public string GetDescription()
        {
            switch (roomType)
            {
                case RoomType.Start:
                    return "Point de départ de votre exploration";
                case RoomType.Combat:
                    return "Une salle infestée d'ennemis";
                case RoomType.Boss:
                    return "Repaire d'un puissant gardien";
                case RoomType.Treasure:
                    return "Salle remplie de trésors précieux";
                case RoomType.Shop:
                    return "Un marchand mystérieux vous attend";
                case RoomType.Rest:
                    return "Un havre de paix pour récupérer";
                case RoomType.Secret:
                    return "Une salle cachée aux secrets anciens";
                case RoomType.Event:
                    return "Quelque chose d'inhabituel se produit ici";
                case RoomType.Challenge:
                    return "Un défi particulier vous attend";
                case RoomType.Exit:
                    return "La sortie vers le niveau suivant";
                default:
                    return "Une salle mystérieuse";
            }
        }
        
        /// <summary>
        /// Vérifie si la salle peut être entrée
        /// </summary>
        public bool CanEnter()
        {
            return !isLocked;
        }
        
        /// <summary>
        /// Vérifie si la salle a des ennemis vivants
        /// </summary>
        public bool HasLivingEnemies()
        {
            return enemies.Exists(e => e.isAlive);
        }
        
        /// <summary>
        /// Marque tous les ennemis comme morts
        /// </summary>
        public void ClearAllEnemies()
        {
            foreach (var enemy in enemies)
            {
                enemy.isAlive = false;
            }
        }
        
        /// <summary>
        /// Ajoute un modificateur à la salle
        /// </summary>
        public void AddModifier(RoomModifier modifier)
        {
            modifiers.Add(modifier);
            ApplyModifier(modifier);
        }
        
        /// <summary>
        /// Applique un modificateur
        /// </summary>
        private void ApplyModifier(RoomModifier modifier)
        {
            switch (modifier.type)
            {
                case RoomModifierType.DifficultyIncrease:
                    difficultyMultiplier *= modifier.value;
                    break;
                case RoomModifierType.ExtraEnemies:
                    // Ajouter des ennemis supplémentaires
                    break;
                case RoomModifierType.BetterLoot:
                    // Améliorer le loot
                    break;
            }
        }
    }
    
    /// <summary>
    /// Données d'une porte
    /// </summary>
    [System.Serializable]
    public class DoorData
    {
        public Vector2Int direction;
        public bool isOpen = true;
        public bool isLocked = false;
        public string keyRequired = "";
        public DoorType doorType = DoorType.Normal;
    }
    
    /// <summary>
    /// Types de portes
    /// </summary>
    public enum DoorType
    {
        Normal,
        Locked,
        Secret,
        Boss,
        OneWay
    }
    
    /// <summary>
    /// Données de spawn d'ennemi
    /// </summary>
    [System.Serializable]
    public class EnemySpawnData
    {
        public string enemyId;
        public Vector3 spawnPosition;
        public bool isAlive = true;
        public bool isBoss = false;
        public float difficultyMultiplier = 1f;
        public List<string> modifiers = new List<string>();
    }
    
    /// <summary>
    /// Données de spawn d'objet
    /// </summary>
    [System.Serializable]
    public class ItemSpawnData
    {
        public string itemId;
        public Vector3 spawnPosition;
        public bool isCollected = false;
        public ItemRarity rarity = ItemRarity.Common;
        public bool isHidden = false;
    }
    
    /// <summary>
    /// Rareté des objets
    /// </summary>
    public enum ItemRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
    
    /// <summary>
    /// Données d'interactable
    /// </summary>
    [System.Serializable]
    public class InteractableData
    {
        public string interactableId;
        public Vector3 position;
        public InteractableType type;
        public bool isUsed = false;
        public Dictionary<string, object> properties = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// Types d'interactables
    /// </summary>
    public enum InteractableType
    {
        Chest,
        Switch,
        Altar,
        Terminal,
        Shrine,
        Portal,
        Merchant,
        CraftingStation
    }
    
    /// <summary>
    /// Modificateur de salle
    /// </summary>
    [System.Serializable]
    public class RoomModifier
    {
        public string name;
        public string description;
        public RoomModifierType type;
        public float value;
        public float duration = -1f; // -1 = permanent
    }
    
    /// <summary>
    /// Types de modificateurs de salle
    /// </summary>
    public enum RoomModifierType
    {
        DifficultyIncrease,
        DifficultyDecrease,
        ExtraEnemies,
        FewerEnemies,
        BetterLoot,
        WorseLoot,
        HealthDrain,
        HealthRegen,
        SpeedBoost,
        SpeedReduction,
        DamageBoost,
        DamageReduction,
        TimeLimit,
        NoHealing,
        DoubleRewards
    }
    
    /// <summary>
    /// Template de salle
    /// </summary>
    [CreateAssetMenu(fileName = "RoomTemplate", menuName = "ChronoForge/Room Template")]
    public class RoomTemplate : ScriptableObject
    {
        [Header("Basic Info")]
        public string roomName;
        public RoomType roomType;
        public string description;
        
        [Header("Generation")]
        public GameObject roomPrefab;
        public float weight = 1f;
        public int minLevel = 1;
        public int maxLevel = 100;
        
        [Header("Enemy Spawns")]
        public List<EnemySpawnTemplate> enemySpawns = new List<EnemySpawnTemplate>();
        public int minEnemies = 1;
        public int maxEnemies = 5;
        
        [Header("Item Spawns")]
        public List<ItemSpawnTemplate> itemSpawns = new List<ItemSpawnTemplate>();
        public int minItems = 0;
        public int maxItems = 3;
        
        [Header("Interactables")]
        public List<InteractableSpawnTemplate> interactableSpawns = new List<InteractableSpawnTemplate>();
        
        [Header("Modifiers")]
        public List<RoomModifier> defaultModifiers = new List<RoomModifier>();
        
        /// <summary>
        /// Vérifie si ce template peut être utilisé au niveau donné
        /// </summary>
        public bool CanUseAtLevel(int level)
        {
            return level >= minLevel && level <= maxLevel;
        }
    }
    
    /// <summary>
    /// Template de spawn d'ennemi
    /// </summary>
    [System.Serializable]
    public class EnemySpawnTemplate
    {
        public string enemyId;
        public float spawnChance = 1f;
        public Vector3 relativePosition;
        public bool isBoss = false;
        public List<string> requiredModifiers = new List<string>();
    }
    
    /// <summary>
    /// Template de spawn d'objet
    /// </summary>
    [System.Serializable]
    public class ItemSpawnTemplate
    {
        public string itemId;
        public float spawnChance = 1f;
        public Vector3 relativePosition;
        public ItemRarity minRarity = ItemRarity.Common;
        public ItemRarity maxRarity = ItemRarity.Legendary;
        public bool isHidden = false;
    }
    
    /// <summary>
    /// Template de spawn d'interactable
    /// </summary>
    [System.Serializable]
    public class InteractableSpawnTemplate
    {
        public string interactableId;
        public InteractableType type;
        public float spawnChance = 1f;
        public Vector3 relativePosition;
        public Dictionary<string, object> defaultProperties = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// Données d'un niveau généré
    /// </summary>
    [System.Serializable]
    public class LevelData
    {
        public int seed;
        public BiomeData biome;
        public int roomCount;
        public Vector2Int gridSize;
        public float generationTime;
        public Dictionary<Vector2Int, RoomData> rooms = new Dictionary<Vector2Int, RoomData>();
        public List<Vector2Int> mainPath = new List<Vector2Int>();
        public List<Vector2Int> sidePaths = new List<Vector2Int>();
        
        /// <summary>
        /// Obtient le nombre de salles visitées
        /// </summary>
        public int GetVisitedRoomCount()
        {
            int count = 0;
            foreach (var room in rooms.Values)
            {
                if (room.isVisited) count++;
            }
            return count;
        }
        
        /// <summary>
        /// Obtient le nombre de salles nettoyées
        /// </summary>
        public int GetClearedRoomCount()
        {
            int count = 0;
            foreach (var room in rooms.Values)
            {
                if (room.isCleared) count++;
            }
            return count;
        }
        
        /// <summary>
        /// Obtient le pourcentage de progression
        /// </summary>
        public float GetProgressPercentage()
        {
            return rooms.Count > 0 ? (float)GetClearedRoomCount() / rooms.Count : 0f;
        }
    }
    
    /// <summary>
    /// Données d'un biome
    /// </summary>
    [CreateAssetMenu(fileName = "BiomeData", menuName = "ChronoForge/Biome Data")]
    public class BiomeData : ScriptableObject
    {
        [Header("Basic Info")]
        public string biomeName;
        public string description;
        public Sprite biomeIcon;
        
        [Header("Visual Theme")]
        public Color primaryColor = Color.white;
        public Color secondaryColor = Color.gray;
        public Material biomeMaterial;
        public List<GameObject> decorationPrefabs = new List<GameObject>();
        
        [Header("Audio")]
        public AudioClip ambientMusic;
        public List<AudioClip> ambientSounds = new List<AudioClip>();
        
        [Header("Gameplay")]
        public float difficultyMultiplier = 1f;
        public List<string> commonEnemies = new List<string>();
        public List<string> rareEnemies = new List<string>();
        public List<string> bossEnemies = new List<string>();
        
        [Header("Loot Tables")]
        public List<string> commonItems = new List<string>();
        public List<string> rareItems = new List<string>();
        public List<string> uniqueItems = new List<string>();
        
        [Header("Special Features")]
        public List<RoomModifier> biomeModifiers = new List<RoomModifier>();
        public List<string> specialRoomTypes = new List<string>();
    }
}
