using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using ChronoForge.Core;
using ChronoForge.Player;
using ChronoForge.Progression;

namespace ChronoForge.Debug
{
    /// <summary>
    /// Console de debug pour ChronoForge
    /// </summary>
    public class DebugConsole : MonoBehaviour
    {
        [Header("Console Settings")]
        public KeyCode toggleKey = KeyCode.BackQuote; // ~
        public int maxLogEntries = 100;
        public int maxCommandHistory = 20;
        
        [Header("Visual Settings")]
        public Color backgroundColor = new Color(0, 0, 0, 0.8f);
        public Color textColor = Color.white;
        public Color errorColor = Color.red;
        public Color warningColor = Color.yellow;
        public Color commandColor = Color.cyan;
        
        // Console state
        private bool isVisible = false;
        private string currentInput = "";
        private Vector2 scrollPosition = Vector2.zero;
        private List<LogEntry> logEntries = new List<LogEntry>();
        private List<string> commandHistory = new List<string>();
        private int historyIndex = -1;
        
        // Commands
        private Dictionary<string, DebugCommand> commands = new Dictionary<string, DebugCommand>();
        
        // UI
        private GUIStyle consoleStyle;
        private GUIStyle inputStyle;
        private GUIStyle logStyle;
        private bool stylesInitialized = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            RegisterCommands();
            
            // Subscribe to Unity log messages
            Application.logMessageReceived += HandleUnityLog;
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleConsole();
            }
            
            if (isVisible)
            {
                HandleInput();
            }
        }
        
        private void OnGUI()
        {
            if (!isVisible) return;
            
            if (!stylesInitialized)
            {
                InitializeStyles();
            }
            
            DrawConsole();
        }
        
        private void OnDestroy()
        {
            Application.logMessageReceived -= HandleUnityLog;
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeStyles()
        {
            consoleStyle = new GUIStyle(GUI.skin.box);
            consoleStyle.normal.background = MakeTexture(2, 2, backgroundColor);
            
            inputStyle = new GUIStyle(GUI.skin.textField);
            inputStyle.normal.textColor = textColor;
            inputStyle.fontSize = 14;
            
            logStyle = new GUIStyle(GUI.skin.label);
            logStyle.normal.textColor = textColor;
            logStyle.fontSize = 12;
            logStyle.wordWrap = true;
            logStyle.richText = true;
            
            stylesInitialized = true;
        }
        
        private Texture2D MakeTexture(int width, int height, Color color)
        {
            Color[] pixels = new Color[width * height];
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = color;
            }
            
            Texture2D texture = new Texture2D(width, height);
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        #endregion
        
        #region Command Registration
        
        private void RegisterCommands()
        {
            // Help command
            RegisterCommand("help", "Shows all available commands", (args) =>
            {
                Log("Available commands:", LogType.Log);
                foreach (var cmd in commands.OrderBy(c => c.Key))
                {
                    Log($"  {cmd.Key} - {cmd.Value.description}", LogType.Log);
                }
            });
            
            // Clear command
            RegisterCommand("clear", "Clears the console", (args) =>
            {
                logEntries.Clear();
            });
            
            // Player commands
            RegisterCommand("god", "Toggle god mode", (args) =>
            {
                PlayerController player = FindObjectOfType<PlayerController>();
                if (player != null)
                {
                    HealthSystem health = player.GetComponent<HealthSystem>();
                    if (health != null)
                    {
                        health.isInvulnerable = !health.isInvulnerable;
                        Log($"God mode: {(health.isInvulnerable ? "ON" : "OFF")}", LogType.Log);
                    }
                }
                else
                {
                    Log("Player not found!", LogType.Error);
                }
            });
            
            RegisterCommand("heal", "Heal player to full health", (args) =>
            {
                PlayerController player = FindObjectOfType<PlayerController>();
                if (player != null)
                {
                    HealthSystem health = player.GetComponent<HealthSystem>();
                    if (health != null)
                    {
                        health.HealToFull();
                        Log("Player healed to full health", LogType.Log);
                    }
                }
                else
                {
                    Log("Player not found!", LogType.Error);
                }
            });
            
            RegisterCommand("addexp", "Add experience to player", (args) =>
            {
                if (args.Length > 0 && int.TryParse(args[0], out int exp))
                {
                    if (ProgressionManager.Instance != null)
                    {
                        ProgressionManager.Instance.AddExperience(exp);
                        Log($"Added {exp} experience", LogType.Log);
                    }
                    else
                    {
                        Log("ProgressionManager not found!", LogType.Error);
                    }
                }
                else
                {
                    Log("Usage: addexp <amount>", LogType.Warning);
                }
            });
            
            RegisterCommand("addcurrency", "Add meta currency", (args) =>
            {
                if (args.Length > 0 && int.TryParse(args[0], out int currency))
                {
                    if (ProgressionManager.Instance != null)
                    {
                        ProgressionManager.Instance.AddMetaCurrency(currency);
                        Log($"Added {currency} meta currency", LogType.Log);
                    }
                    else
                    {
                        Log("ProgressionManager not found!", LogType.Error);
                    }
                }
                else
                {
                    Log("Usage: addcurrency <amount>", LogType.Warning);
                }
            });
            
            // Game commands
            RegisterCommand("timescale", "Set game time scale", (args) =>
            {
                if (args.Length > 0 && float.TryParse(args[0], out float scale))
                {
                    Time.timeScale = Mathf.Clamp(scale, 0f, 10f);
                    Log($"Time scale set to {Time.timeScale}", LogType.Log);
                }
                else
                {
                    Log("Usage: timescale <value>", LogType.Warning);
                }
            });
            
            RegisterCommand("fps", "Set target frame rate", (args) =>
            {
                if (args.Length > 0 && int.TryParse(args[0], out int fps))
                {
                    Application.targetFrameRate = fps;
                    Log($"Target FPS set to {fps}", LogType.Log);
                }
                else
                {
                    Log("Usage: fps <value>", LogType.Warning);
                }
            });
            
            RegisterCommand("quit", "Quit the game", (args) =>
            {
                Log("Quitting game...", LogType.Log);
                #if UNITY_EDITOR
                    UnityEditor.EditorApplication.isPlaying = false;
                #else
                    Application.Quit();
                #endif
            });
            
            // Level commands
            RegisterCommand("newrun", "Start a new run", (args) =>
            {
                RunManager runManager = FindObjectOfType<RunManager>();
                if (runManager != null)
                {
                    runManager.StartNewRun();
                    Log("Started new run", LogType.Log);
                }
                else
                {
                    Log("RunManager not found!", LogType.Error);
                }
            });
            
            RegisterCommand("completeroom", "Complete current room", (args) =>
            {
                // This would complete the current room
                Log("Room completed", LogType.Log);
            });
            
            // Debug commands
            RegisterCommand("showfps", "Toggle FPS display", (args) =>
            {
                PerformanceMonitor monitor = FindObjectOfType<PerformanceMonitor>();
                if (monitor != null)
                {
                    monitor.ToggleVisibility();
                    Log("FPS display toggled", LogType.Log);
                }
                else
                {
                    Log("PerformanceMonitor not found!", LogType.Error);
                }
            });
        }
        
        private void RegisterCommand(string name, string description, System.Action<string[]> action)
        {
            commands[name.ToLower()] = new DebugCommand
            {
                name = name,
                description = description,
                action = action
            };
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleInput()
        {
            Event e = Event.current;
            
            if (e.type == EventType.KeyDown)
            {
                switch (e.keyCode)
                {
                    case KeyCode.Return:
                    case KeyCode.KeypadEnter:
                        ExecuteCommand(currentInput);
                        currentInput = "";
                        break;
                        
                    case KeyCode.UpArrow:
                        NavigateHistory(-1);
                        break;
                        
                    case KeyCode.DownArrow:
                        NavigateHistory(1);
                        break;
                        
                    case KeyCode.Tab:
                        AutoComplete();
                        break;
                }
            }
        }
        
        private void NavigateHistory(int direction)
        {
            if (commandHistory.Count == 0) return;
            
            historyIndex += direction;
            historyIndex = Mathf.Clamp(historyIndex, -1, commandHistory.Count - 1);
            
            if (historyIndex >= 0)
            {
                currentInput = commandHistory[commandHistory.Count - 1 - historyIndex];
            }
            else
            {
                currentInput = "";
            }
        }
        
        private void AutoComplete()
        {
            if (string.IsNullOrEmpty(currentInput)) return;
            
            var matches = commands.Keys.Where(cmd => cmd.StartsWith(currentInput.ToLower())).ToList();
            
            if (matches.Count == 1)
            {
                currentInput = matches[0];
            }
            else if (matches.Count > 1)
            {
                Log($"Possible commands: {string.Join(", ", matches)}", LogType.Log);
            }
        }
        
        #endregion
        
        #region Command Execution
        
        private void ExecuteCommand(string input)
        {
            if (string.IsNullOrEmpty(input.Trim())) return;
            
            // Add to history
            commandHistory.Add(input);
            if (commandHistory.Count > maxCommandHistory)
            {
                commandHistory.RemoveAt(0);
            }
            historyIndex = -1;
            
            // Log the command
            Log($"> {input}", LogType.Log, commandColor);
            
            // Parse command
            string[] parts = input.Trim().Split(' ');
            string commandName = parts[0].ToLower();
            string[] args = parts.Skip(1).ToArray();
            
            // Execute command
            if (commands.ContainsKey(commandName))
            {
                try
                {
                    commands[commandName].action(args);
                }
                catch (System.Exception e)
                {
                    Log($"Error executing command: {e.Message}", LogType.Error);
                }
            }
            else
            {
                Log($"Unknown command: {commandName}. Type 'help' for available commands.", LogType.Warning);
            }
        }
        
        #endregion
        
        #region Logging
        
        private void HandleUnityLog(string logString, string stackTrace, LogType type)
        {
            Log(logString, type);
        }
        
        public void Log(string message, LogType type = LogType.Log, Color? customColor = null)
        {
            Color color = customColor ?? GetColorForLogType(type);
            
            logEntries.Add(new LogEntry
            {
                message = message,
                type = type,
                color = color,
                timestamp = System.DateTime.Now
            });
            
            // Limit log entries
            if (logEntries.Count > maxLogEntries)
            {
                logEntries.RemoveAt(0);
            }
            
            // Auto-scroll to bottom
            scrollPosition.y = float.MaxValue;
        }
        
        private Color GetColorForLogType(LogType type)
        {
            switch (type)
            {
                case LogType.Error:
                case LogType.Exception:
                    return errorColor;
                case LogType.Warning:
                    return warningColor;
                default:
                    return textColor;
            }
        }
        
        #endregion
        
        #region UI Drawing
        
        private void DrawConsole()
        {
            float consoleHeight = Screen.height * 0.5f;
            Rect consoleRect = new Rect(0, 0, Screen.width, consoleHeight);
            
            // Background
            GUI.Box(consoleRect, "", consoleStyle);
            
            // Log area
            Rect logRect = new Rect(10, 10, Screen.width - 20, consoleHeight - 60);
            Rect viewRect = new Rect(0, 0, logRect.width - 20, logEntries.Count * 20);
            
            scrollPosition = GUI.BeginScrollView(logRect, scrollPosition, viewRect);
            
            float yPos = 0;
            foreach (var entry in logEntries)
            {
                logStyle.normal.textColor = entry.color;
                string timeStamp = entry.timestamp.ToString("HH:mm:ss");
                string logText = $"[{timeStamp}] {entry.message}";
                
                GUI.Label(new Rect(0, yPos, viewRect.width, 20), logText, logStyle);
                yPos += 20;
            }
            
            GUI.EndScrollView();
            
            // Input field
            Rect inputRect = new Rect(10, consoleHeight - 40, Screen.width - 20, 25);
            GUI.SetNextControlName("ConsoleInput");
            currentInput = GUI.TextField(inputRect, currentInput, inputStyle);
            
            // Focus input field
            if (isVisible)
            {
                GUI.FocusControl("ConsoleInput");
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void ToggleConsole()
        {
            isVisible = !isVisible;
            
            if (isVisible)
            {
                Log("Debug console opened. Type 'help' for commands.", LogType.Log);
            }
        }
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
        }
        
        public bool IsVisible()
        {
            return isVisible;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Commande de debug
    /// </summary>
    public class DebugCommand
    {
        public string name;
        public string description;
        public System.Action<string[]> action;
    }
    
    /// <summary>
    /// Entrée de log
    /// </summary>
    public class LogEntry
    {
        public string message;
        public LogType type;
        public Color color;
        public System.DateTime timestamp;
    }
}
