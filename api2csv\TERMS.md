# Conditions d'Utilisation - API2CSV

**Dernière mise à jour : 24 juin 2025**

## 1. Acceptation des Conditions

En utilisant API2CSV ("le Service"), vous acceptez d'être lié par ces conditions d'utilisation. Si vous n'acceptez pas ces conditions, veuillez ne pas utiliser le Service.

## 2. Description du Service

API2CSV est un outil gratuit de conversion de données JSON vers CSV qui fonctionne entièrement côté client. Le Service permet aux utilisateurs de :

- Convertir des données JSON en format CSV
- Télécharger les fichiers convertis
- Utiliser l'outil sans inscription
- Traiter les données localement dans le navigateur

## 3. Utilisation Autorisée

Vous pouvez utiliser API2CSV pour :

- ✅ Convertir vos propres données JSON
- ✅ Utiliser l'outil à des fins personnelles et commerciales
- ✅ Intégrer l'outil dans vos projets (sous licence MIT)
- ✅ Partager le lien vers l'outil

## 4. Utilisation Interdite

Il est interdit d'utiliser API2CSV pour :

- ❌ Traiter des données illégales ou malveillantes
- ❌ Tenter de compromettre la sécurité du Service
- ❌ Utiliser l'outil pour des activités frauduleuses
- ❌ Surcharger le Service avec des requêtes automatisées excessives
- ❌ Copier ou redistribuer le code sans respecter la licence MIT

## 5. Propriété Intellectuelle

- Le code source d'API2CSV est disponible sous licence MIT
- Vous conservez tous les droits sur vos données
- Les données traitées ne sont pas stockées sur nos serveurs
- Le nom "API2CSV" et le design sont la propriété de NeethDseven

## 6. Traitement des Données

- **Traitement local** : Toutes les conversions sont effectuées dans votre navigateur
- **Aucun stockage** : Nous ne stockons aucune de vos données
- **Aucune transmission** : Vos données ne quittent jamais votre appareil
- **Confidentialité** : Nous n'avons pas accès à vos fichiers ou données

## 7. Disponibilité du Service

- Le Service est fourni "tel quel" sans garantie de disponibilité
- Nous nous réservons le droit de modifier ou interrompre le Service
- Aucune garantie de temps de fonctionnement n'est fournie
- Les mises à jour peuvent modifier les fonctionnalités

## 8. Limitation de Responsabilité

Dans les limites autorisées par la loi :

- API2CSV est fourni sans garantie d'aucune sorte
- Nous ne sommes pas responsables des dommages directs ou indirects
- Vous utilisez le Service à vos propres risques
- Nous ne garantissons pas l'exactitude des conversions

## 9. Modifications des Conditions

- Nous pouvons modifier ces conditions à tout moment
- Les modifications seront publiées sur cette page
- La date de dernière mise à jour sera mise à jour
- L'utilisation continue constitue une acceptation des nouvelles conditions

## 10. Résiliation

- Vous pouvez cesser d'utiliser le Service à tout moment
- Nous pouvons restreindre l'accès en cas de violation des conditions
- Ces conditions restent en vigueur même après cessation d'utilisation

## 11. Loi Applicable

Ces conditions sont régies par le droit français. Tout litige sera soumis aux tribunaux compétents de France.

## 12. Contact

Pour toute question concernant ces conditions d'utilisation :

- **GitHub** : [https://github.com/NeethDseven/api2csv](https://github.com/NeethDseven/api2csv)
- **Issues** : [https://github.com/NeethDseven/api2csv/issues](https://github.com/NeethDseven/api2csv/issues)

## 13. Licence Open Source

API2CSV est distribué sous licence MIT. Vous êtes libre de :

- Utiliser le code à des fins commerciales et personnelles
- Modifier et distribuer le code
- Inclure le code dans des projets propriétaires

**Condition** : Inclure la notice de copyright et la licence dans toute distribution.

## 14. Contributions

En contribuant au projet API2CSV :

- Vous accordez une licence perpétuelle et irrévocable pour utiliser vos contributions
- Vous confirmez que vous avez le droit de faire ces contributions
- Vos contributions seront soumises à la même licence MIT

## 15. Clause de Sauvegarde

Si une partie de ces conditions est jugée invalide, les autres parties restent en vigueur.

---

**API2CSV - Convertisseur JSON vers CSV**  
**Créé par NeethDseven**  
**Licence MIT - Open Source**
