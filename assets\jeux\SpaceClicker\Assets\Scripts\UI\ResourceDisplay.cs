using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Numerics;
using SpaceClicker.Core;
using SpaceClicker.Utils;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Affichage d'une ressource dans l'UI
    /// </summary>
    public class ResourceDisplay : MonoBehaviour
    {
        [Header("UI Components")]
        public TextMeshProUGUI resourceNameText;
        public TextMeshProUGUI resourceAmountText;
        public TextMeshProUGUI generationRateText;
        public Image resourceIcon;
        public Slider resourceBar;
        
        [Header("Settings")]
        public ResourceType resourceType;
        public bool showGenerationRate = true;
        public bool useShortFormat = true;
        public bool animateChanges = true;
        
        [Header("Animation")]
        public float animationDuration = 0.5f;
        public AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Colors")]
        public Color positiveChangeColor = Color.green;
        public Color negativeChangeColor = Color.red;
        public Color normalColor = Color.white;
        
        // Current values
        private BigInteger currentAmount = BigInteger.Zero;
        private float currentGenerationRate = 0f;
        private BigInteger displayedAmount = BigInteger.Zero;
        
        // Animation
        private Coroutine animationCoroutine;
        
        // Components
        private ResourceManager resourceManager;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeDisplay();
        }
        
        private void Start()
        {
            resourceManager = FindObjectOfType<ResourceManager>();
            SubscribeToEvents();
            UpdateDisplay();
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeDisplay()
        {
            // Setup default texts if not assigned
            if (resourceNameText != null)
            {
                resourceNameText.text = resourceType.ToString();
            }
            
            if (resourceAmountText != null)
            {
                resourceAmountText.text = "0";
            }
            
            if (generationRateText != null)
            {
                generationRateText.text = "+0/s";
                generationRateText.gameObject.SetActive(showGenerationRate);
            }
            
            // Setup resource bar if present
            if (resourceBar != null)
            {
                resourceBar.gameObject.SetActive(false); // Hidden by default for clicker games
            }
        }
        
        #endregion
        
        #region Event Subscription
        
        private void SubscribeToEvents()
        {
            ResourceManager.OnResourceChanged += HandleResourceChanged;
            ResourceManager.OnGenerationRateChanged += HandleGenerationRateChanged;
        }
        
        private void UnsubscribeFromEvents()
        {
            ResourceManager.OnResourceChanged -= HandleResourceChanged;
            ResourceManager.OnGenerationRateChanged -= HandleGenerationRateChanged;
        }
        
        #endregion
        
        #region Event Handlers
        
        private void HandleResourceChanged(ResourceType type, BigInteger amount)
        {
            if (type == resourceType)
            {
                BigInteger previousAmount = currentAmount;
                currentAmount = amount;
                
                if (animateChanges)
                {
                    AnimateAmountChange(previousAmount, amount);
                }
                else
                {
                    displayedAmount = amount;
                    UpdateAmountText();
                }
                
                // Show change indicator
                if (amount != previousAmount)
                {
                    ShowChangeIndicator(amount - previousAmount);
                }
            }
        }
        
        private void HandleGenerationRateChanged(ResourceType type, float rate)
        {
            if (type == resourceType)
            {
                currentGenerationRate = rate;
                UpdateGenerationRateText();
            }
        }
        
        #endregion
        
        #region Display Updates
        
        public void UpdateDisplay()
        {
            if (resourceManager != null)
            {
                currentAmount = resourceManager.GetResource(resourceType);
                currentGenerationRate = resourceManager.GetGenerationRate(resourceType);
                displayedAmount = currentAmount;
                
                UpdateAmountText();
                UpdateGenerationRateText();
            }
        }
        
        private void UpdateAmountText()
        {
            if (resourceAmountText != null)
            {
                string amountText = useShortFormat ? 
                    displayedAmount.ToShortString() : 
                    displayedAmount.ToFormattedString();
                
                resourceAmountText.text = amountText;
            }
        }
        
        private void UpdateGenerationRateText()
        {
            if (generationRateText != null && showGenerationRate)
            {
                if (currentGenerationRate > 0)
                {
                    string rateText = useShortFormat ?
                        $"+{new BigInteger(currentGenerationRate).ToShortString()}/s" :
                        $"+{currentGenerationRate:F1}/s";
                    
                    generationRateText.text = rateText;
                    generationRateText.color = positiveChangeColor;
                }
                else
                {
                    generationRateText.text = "+0/s";
                    generationRateText.color = normalColor;
                }
            }
        }
        
        #endregion
        
        #region Animation
        
        private void AnimateAmountChange(BigInteger fromAmount, BigInteger toAmount)
        {
            if (animationCoroutine != null)
            {
                StopCoroutine(animationCoroutine);
            }
            
            animationCoroutine = StartCoroutine(AnimateAmountCoroutine(fromAmount, toAmount));
        }
        
        private System.Collections.IEnumerator AnimateAmountCoroutine(BigInteger fromAmount, BigInteger toAmount)
        {
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / animationDuration;
                float curveValue = animationCurve.Evaluate(progress);
                
                // Interpolate between amounts
                double fromDouble = (double)fromAmount;
                double toDouble = (double)toAmount;
                double currentDouble = Mathf.Lerp((float)fromDouble, (float)toDouble, curveValue);
                
                displayedAmount = new BigInteger(currentDouble);
                UpdateAmountText();
                
                yield return null;
            }
            
            displayedAmount = toAmount;
            UpdateAmountText();
            animationCoroutine = null;
        }
        
        #endregion
        
        #region Change Indicators
        
        private void ShowChangeIndicator(BigInteger change)
        {
            if (change == 0) return;
            
            // Create floating text effect
            CreateFloatingText(change);
            
            // Flash effect on the amount text
            if (resourceAmountText != null)
            {
                StartCoroutine(FlashText(change > 0));
            }
        }
        
        private void CreateFloatingText(BigInteger change)
        {
            // This would create a floating text effect showing the change
            // For now, just log it
            string changeText = change > 0 ? $"+{change.ToShortString()}" : change.ToShortString();
            Debug.Log($"{resourceType}: {changeText}");
        }
        
        private System.Collections.IEnumerator FlashText(bool isPositive)
        {
            if (resourceAmountText == null) yield break;
            
            Color originalColor = resourceAmountText.color;
            Color flashColor = isPositive ? positiveChangeColor : negativeChangeColor;
            
            // Flash to change color
            resourceAmountText.color = flashColor;
            yield return new WaitForSeconds(0.1f);
            
            // Fade back to original color
            float elapsed = 0f;
            float duration = 0.3f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                resourceAmountText.color = Color.Lerp(flashColor, originalColor, progress);
                yield return null;
            }
            
            resourceAmountText.color = originalColor;
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetResourceType(ResourceType type)
        {
            resourceType = type;
            
            if (resourceNameText != null)
            {
                resourceNameText.text = type.ToString();
            }
            
            UpdateDisplay();
        }
        
        public void SetUseShortFormat(bool useShort)
        {
            useShortFormat = useShort;
            UpdateAmountText();
            UpdateGenerationRateText();
        }
        
        public void SetShowGenerationRate(bool show)
        {
            showGenerationRate = show;
            
            if (generationRateText != null)
            {
                generationRateText.gameObject.SetActive(show);
            }
        }
        
        public BigInteger GetCurrentAmount()
        {
            return currentAmount;
        }
        
        public float GetGenerationRate()
        {
            return currentGenerationRate;
        }
        
        #endregion
    }
}
