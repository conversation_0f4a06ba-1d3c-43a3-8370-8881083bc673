using UnityEngine;
using UnityEditor;
using System.IO;
using System.Diagnostics;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Gestionnaire de déploiement pour Space Clicker
    /// </summary>
    public class DeploymentManager : EditorWindow
    {
        [Header("Deployment Settings")]
        public string version = "1.0.0";
        public string buildPath = "Builds/";
        public string deploymentPath = "Deployment/";
        
        [Header("GitHub Pages (WebGL)")]
        public bool deployToGitHubPages = true;
        public string githubRepository = "username/space-clicker";
        public string githubBranch = "gh-pages";
        
        [Header("Itch.io")]
        public bool deployToItchIo = false;
        public string itchIoProject = "username/space-clicker";
        public string itchIoApiKey = "";
        
        [Header("Steam")]
        public bool deployToSteam = false;
        public string steamAppId = "";
        public string steamUsername = "";
        
        [Header("Mobile Stores")]
        public bool deployToGooglePlay = false;
        public bool deployToAppStore = false;
        
        private Vector2 scrollPosition;
        private string deploymentLog = "";
        
        [MenuItem("Space Clicker/Deployment Manager")]
        public static void ShowWindow()
        {
            GetWindow<DeploymentManager>("Deployment Manager");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker Deployment Manager", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Deployment Settings
            GUILayout.Label("Deployment Settings", EditorStyles.boldLabel);
            version = EditorGUILayout.TextField("Version", version);
            buildPath = EditorGUILayout.TextField("Build Path", buildPath);
            deploymentPath = EditorGUILayout.TextField("Deployment Path", deploymentPath);
            
            GUILayout.Space(10);
            
            // GitHub Pages Settings
            GUILayout.Label("GitHub Pages (WebGL)", EditorStyles.boldLabel);
            deployToGitHubPages = EditorGUILayout.Toggle("Deploy to GitHub Pages", deployToGitHubPages);
            if (deployToGitHubPages)
            {
                githubRepository = EditorGUILayout.TextField("Repository", githubRepository);
                githubBranch = EditorGUILayout.TextField("Branch", githubBranch);
            }
            
            GUILayout.Space(10);
            
            // Itch.io Settings
            GUILayout.Label("Itch.io", EditorStyles.boldLabel);
            deployToItchIo = EditorGUILayout.Toggle("Deploy to Itch.io", deployToItchIo);
            if (deployToItchIo)
            {
                itchIoProject = EditorGUILayout.TextField("Project", itchIoProject);
                itchIoApiKey = EditorGUILayout.PasswordField("API Key", itchIoApiKey);
            }
            
            GUILayout.Space(10);
            
            // Steam Settings
            GUILayout.Label("Steam", EditorStyles.boldLabel);
            deployToSteam = EditorGUILayout.Toggle("Deploy to Steam", deployToSteam);
            if (deployToSteam)
            {
                steamAppId = EditorGUILayout.TextField("App ID", steamAppId);
                steamUsername = EditorGUILayout.TextField("Username", steamUsername);
            }
            
            GUILayout.Space(10);
            
            // Mobile Stores
            GUILayout.Label("Mobile Stores", EditorStyles.boldLabel);
            deployToGooglePlay = EditorGUILayout.Toggle("Deploy to Google Play", deployToGooglePlay);
            deployToAppStore = EditorGUILayout.Toggle("Deploy to App Store", deployToAppStore);
            
            GUILayout.Space(20);
            
            // Action Buttons
            if (GUILayout.Button("Build and Deploy All", GUILayout.Height(30)))
            {
                BuildAndDeployAll();
            }
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Build Only"))
            {
                BuildForDeployment();
            }
            if (GUILayout.Button("Deploy Only"))
            {
                DeployBuilds();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Utility Buttons
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Prepare Release"))
            {
                PrepareRelease();
            }
            if (GUILayout.Button("Create Release Notes"))
            {
                CreateReleaseNotes();
            }
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Deployment Log
            if (!string.IsNullOrEmpty(deploymentLog))
            {
                GUILayout.Label("Deployment Log:", EditorStyles.boldLabel);
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
                EditorGUILayout.TextArea(deploymentLog, GUILayout.ExpandHeight(true));
                EditorGUILayout.EndScrollView();
                
                if (GUILayout.Button("Clear Log"))
                {
                    deploymentLog = "";
                }
            }
        }
        
        private void BuildAndDeployAll()
        {
            LogMessage("🚀 Starting build and deployment process...");
            
            // Prepare for release
            PrepareRelease();
            
            // Build for all platforms
            BuildForDeployment();
            
            // Deploy to all configured platforms
            DeployBuilds();
            
            LogMessage("✅ Build and deployment completed!");
        }
        
        private void PrepareRelease()
        {
            LogMessage("📋 Preparing release...");
            
            // Update version in PlayerSettings
            PlayerSettings.bundleVersion = version;
            
            // Update version in code if needed
            UpdateVersionInCode();
            
            // Create deployment directory
            if (!Directory.Exists(deploymentPath))
            {
                Directory.CreateDirectory(deploymentPath);
                LogMessage($"Created deployment directory: {deploymentPath}");
            }
            
            // Validate build settings
            ValidateBuildSettings();
            
            LogMessage("✅ Release preparation completed");
        }
        
        private void UpdateVersionInCode()
        {
            // Update GameConstants with new version
            string constantsPath = "Assets/Scripts/Core/GameConstants.cs";
            if (File.Exists(constantsPath))
            {
                string content = File.ReadAllText(constantsPath);
                
                // Simple regex replacement for version
                string pattern = @"GAME_VERSION\s*=\s*""[^""]*""";
                string replacement = $"GAME_VERSION = \"{version}\"";
                
                content = System.Text.RegularExpressions.Regex.Replace(content, pattern, replacement);
                File.WriteAllText(constantsPath, content);
                
                AssetDatabase.Refresh();
                LogMessage($"Updated version to {version} in GameConstants");
            }
        }
        
        private void ValidateBuildSettings()
        {
            // Check that scenes are in build settings
            if (EditorBuildSettings.scenes.Length == 0)
            {
                LogMessage("⚠️ Warning: No scenes in build settings");
            }
            
            // Check product name
            if (string.IsNullOrEmpty(PlayerSettings.productName))
            {
                PlayerSettings.productName = "Space Clicker";
                LogMessage("Set product name to 'Space Clicker'");
            }
            
            // Check company name
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                PlayerSettings.companyName = "Space Clicker Studio";
                LogMessage("Set company name to 'Space Clicker Studio'");
            }
        }
        
        private void BuildForDeployment()
        {
            LogMessage("🔨 Building for deployment...");
            
            // Use BuildManager to build for selected platforms
            BuildManager buildManager = GetWindow<BuildManager>();
            
            // Configure build settings for release
            buildManager.developmentBuild = false;
            buildManager.autoRunBuild = false;
            
            // Build WebGL for GitHub Pages
            if (deployToGitHubPages)
            {
                LogMessage("Building WebGL for GitHub Pages...");
                // BuildManager will handle the actual building
            }
            
            // Build Windows for Itch.io/Steam
            if (deployToItchIo || deployToSteam)
            {
                LogMessage("Building Windows for distribution...");
            }
            
            // Build Android for Google Play
            if (deployToGooglePlay)
            {
                LogMessage("Building Android for Google Play...");
            }
            
            LogMessage("✅ Building completed");
        }
        
        private void DeployBuilds()
        {
            LogMessage("📤 Starting deployment...");
            
            if (deployToGitHubPages)
            {
                DeployToGitHubPages();
            }
            
            if (deployToItchIo)
            {
                DeployToItchIo();
            }
            
            if (deployToSteam)
            {
                DeployToSteam();
            }
            
            if (deployToGooglePlay)
            {
                DeployToGooglePlay();
            }
            
            if (deployToAppStore)
            {
                DeployToAppStore();
            }
            
            LogMessage("✅ Deployment completed");
        }
        
        private void DeployToGitHubPages()
        {
            LogMessage("🐙 Deploying to GitHub Pages...");
            
            string webglBuildPath = Path.Combine(buildPath, "WebGL");
            string githubPagesPath = Path.Combine(deploymentPath, "github-pages");
            
            if (!Directory.Exists(webglBuildPath))
            {
                LogMessage("❌ WebGL build not found. Build first.");
                return;
            }
            
            try
            {
                // Copy WebGL build to deployment folder
                CopyDirectory(webglBuildPath, githubPagesPath);
                
                // Create or update index.html for GitHub Pages
                CreateGitHubPagesIndex(githubPagesPath);
                
                // Create deployment script
                CreateGitHubDeploymentScript(githubPagesPath);
                
                LogMessage("✅ GitHub Pages deployment prepared");
                LogMessage($"📁 Files ready in: {githubPagesPath}");
                LogMessage("💡 Run the deploy script to push to GitHub Pages");
            }
            catch (System.Exception e)
            {
                LogMessage($"❌ GitHub Pages deployment failed: {e.Message}");
            }
        }
        
        private void DeployToItchIo()
        {
            LogMessage("🎮 Deploying to Itch.io...");
            
            if (string.IsNullOrEmpty(itchIoApiKey))
            {
                LogMessage("❌ Itch.io API key not set");
                return;
            }
            
            // This would require the Butler tool from Itch.io
            LogMessage("💡 Install Butler tool and configure for automatic deployment");
            LogMessage($"Project: {itchIoProject}");
        }
        
        private void DeployToSteam()
        {
            LogMessage("🚂 Deploying to Steam...");
            
            if (string.IsNullOrEmpty(steamAppId))
            {
                LogMessage("❌ Steam App ID not set");
                return;
            }
            
            // This would require SteamCMD and proper Steam partner setup
            LogMessage("💡 Configure SteamCMD for automatic deployment");
            LogMessage($"App ID: {steamAppId}");
        }
        
        private void DeployToGooglePlay()
        {
            LogMessage("📱 Preparing for Google Play deployment...");
            
            string androidBuildPath = Path.Combine(buildPath, "Android");
            if (!Directory.Exists(androidBuildPath))
            {
                LogMessage("❌ Android build not found. Build first.");
                return;
            }
            
            LogMessage("💡 Upload APK/AAB to Google Play Console manually");
            LogMessage($"📁 Android build location: {androidBuildPath}");
        }
        
        private void DeployToAppStore()
        {
            LogMessage("🍎 Preparing for App Store deployment...");
            LogMessage("💡 Use Xcode to build and upload to App Store Connect");
        }
        
        private void CreateReleaseNotes()
        {
            LogMessage("📝 Creating release notes...");
            
            string releaseNotesPath = Path.Combine(deploymentPath, $"release-notes-v{version}.md");
            
            string releaseNotes = $@"# Space Clicker v{version}

## 🚀 New Features
- [Add new features here]

## 🐛 Bug Fixes
- [Add bug fixes here]

## ⚡ Improvements
- [Add improvements here]

## 🔧 Technical Changes
- [Add technical changes here]

## 📋 Known Issues
- [Add known issues here]

---

**Download Links:**
- [GitHub Pages (WebGL)](https://{githubRepository.Split('/')[0]}.github.io/{githubRepository.Split('/')[1]}/)
- [Itch.io]({itchIoProject})

**System Requirements:**
- Modern web browser with WebGL support
- 512 MB RAM
- 100 MB storage space

**Credits:**
Space Clicker development team
";
            
            File.WriteAllText(releaseNotesPath, releaseNotes);
            LogMessage($"✅ Release notes created: {releaseNotesPath}");
            
            // Open the file for editing
            Process.Start(releaseNotesPath);
        }
        
        #region Utility Methods
        
        private void LogMessage(string message)
        {
            deploymentLog += $"[{System.DateTime.Now:HH:mm:ss}] {message}\n";
            UnityEngine.Debug.Log(message);
        }
        
        private void CopyDirectory(string sourceDir, string destDir)
        {
            if (!Directory.Exists(destDir))
            {
                Directory.CreateDirectory(destDir);
            }
            
            foreach (string file in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
            {
                string relativePath = Path.GetRelativePath(sourceDir, file);
                string destFile = Path.Combine(destDir, relativePath);
                
                Directory.CreateDirectory(Path.GetDirectoryName(destFile));
                File.Copy(file, destFile, true);
            }
        }
        
        private void CreateGitHubPagesIndex(string deployPath)
        {
            string indexPath = Path.Combine(deployPath, "index.html");
            
            // If Unity WebGL build already has index.html, we might want to modify it
            if (File.Exists(indexPath))
            {
                LogMessage("Index.html already exists from WebGL build");
                return;
            }
            
            // Create a simple index.html if needed
            string indexContent = $@"<!DOCTYPE html>
<html>
<head>
    <title>Space Clicker v{version}</title>
    <meta charset=""utf-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
</head>
<body>
    <h1>Space Clicker v{version}</h1>
    <p>Loading game...</p>
    <!-- Unity WebGL content will be here -->
</body>
</html>";
            
            File.WriteAllText(indexPath, indexContent);
        }
        
        private void CreateGitHubDeploymentScript(string deployPath)
        {
            string scriptPath = Path.Combine(deployPath, "deploy.bat");
            
            string scriptContent = $@"@echo off
echo Deploying Space Clicker v{version} to GitHub Pages...

git init
git add .
git commit -m ""Deploy Space Clicker v{version}""
git branch -M {githubBranch}
git remote add origin https://github.com/{githubRepository}.git
git push -u origin {githubBranch} --force

echo Deployment completed!
pause
";
            
            File.WriteAllText(scriptPath, scriptContent);
            LogMessage($"Created deployment script: {scriptPath}");
        }
        
        #endregion
    }
}
