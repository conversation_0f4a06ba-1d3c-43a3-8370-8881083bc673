# Exemple de posts pour le bot de publication
# Format: liste de posts avec titre, contenu, plateformes et planification

posts:
  - id: "api2csv_launch"
    title: "Mon outil JSON → CSV (open source)"
    body: "Un convertisseur JSON en CSV 100% client-side 👉 https://github.com/NeethDseven/api2csv"
    platforms: ["twitter", "reddit"]
    schedule: "2025-07-01 14:00"
    tags: ["webdev", "javascript", "opensource"]
    subreddit: "webdev"  # pour Reddit
    image: null
    
  - id: "api2csv_features"
    title: "API2CSV - Toutes les fonctionnalités"
    body: |
      🛠️ API2CSV : Le convertisseur JSON→CSV ultime

      ✅ Conversion en temps réel
      ✅ 100% client-side (sécurisé)
      ✅ Support JSON complexes et imbriqués
      ✅ Interface intuitive
      ✅ Téléchargement direct
      ✅ Gratuit et open source

      👉 https://neethdseven.github.io/moneybby/

      #webdev #json #csv #opensource #tools
    platforms: ["twitter", "devto"]
    schedule: "2025-07-02 10:00"
    tags: ["webdev", "json", "csv", "opensource", "tools"]
    
  - id: "dev_tips_json"
    title: "Astuce dev : Manipuler JSON efficacement"
    body: |
      💡 Astuce du jour : Convertir JSON en CSV sans backend
      
      Avec PapaParse + JavaScript vanilla, vous pouvez :
      ✅ Parser JSON complexe
      ✅ Transformer en CSV
      ✅ Télécharger directement
      
      Exemple d'outil : https://github.com/NeethDseven/api2csv
      
      #javascript #webdev #tips
    platforms: ["twitter", "reddit"]
    schedule: "recurring"
    frequency: "weekly"
    subreddit: "javascript"
    
  - id: "api2csv_use_cases"
    title: "API2CSV - Cas d'usage pratiques"
    body: |
      🎯 Quand utiliser API2CSV ?

      ✅ Analyser des données d'API REST
      ✅ Convertir des exports JSON
      ✅ Préparer des données pour Excel
      ✅ Transformer des logs JSON
      ✅ Migrer des données entre systèmes

      Exemple : Convertir les données GitHub API en CSV pour analyse
      👉 https://neethdseven.github.io/moneybby/

      Quel est votre cas d'usage préféré ?
    platforms: ["devto"]
    schedule: "2025-07-03 16:00"
    tags: ["json", "csv", "data", "api", "tools"]
