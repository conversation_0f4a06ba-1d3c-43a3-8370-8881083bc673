using UnityEngine;
using System.Collections.Generic;
using System.IO;

namespace ChronoForge.Localization
{
    /// <summary>
    /// Gestionnaire de localisation pour ChronoForge
    /// </summary>
    public class LocalizationManager : MonoBehaviour
    {
        [Header("Localization Settings")]
        public SystemLanguage defaultLanguage = SystemLanguage.English;
        public List<SystemLanguage> supportedLanguages = new List<SystemLanguage>
        {
            SystemLanguage.English,
            SystemLanguage.French,
            SystemLanguage.Spanish,
            SystemLanguage.German,
            SystemLanguage.Japanese
        };
        
        [Header("File Settings")]
        public string localizationFolder = "Localization";
        public string fileExtension = ".json";
        
        // Events
        public static System.Action<SystemLanguage> OnLanguageChanged;
        
        // Current language and data
        private SystemLanguage currentLanguage;
        private Dictionary<string, string> localizedText = new Dictionary<string, string>();
        private Dictionary<SystemLanguage, Dictionary<string, string>> languageData = 
            new Dictionary<SystemLanguage, Dictionary<string, string>>();
        
        // Singleton
        public static LocalizationManager Instance { get; private set; }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeLocalization();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeLocalization()
        {
            // Load saved language or use system language
            SystemLanguage savedLanguage = (SystemLanguage)PlayerPrefs.GetInt("Language", (int)Application.systemLanguage);
            
            // Check if saved language is supported
            if (!supportedLanguages.Contains(savedLanguage))
            {
                savedLanguage = defaultLanguage;
            }
            
            // Load all language files
            LoadAllLanguages();
            
            // Set current language
            SetLanguage(savedLanguage);
            
            Debug.Log($"🌍 LocalizationManager initialized with language: {currentLanguage}");
        }
        
        private void LoadAllLanguages()
        {
            foreach (SystemLanguage language in supportedLanguages)
            {
                LoadLanguageFile(language);
            }
        }
        
        private void LoadLanguageFile(SystemLanguage language)
        {
            string fileName = GetLanguageFileName(language);
            string filePath = Path.Combine(Application.streamingAssetsPath, localizationFolder, fileName);
            
            if (File.Exists(filePath))
            {
                try
                {
                    string jsonContent = File.ReadAllText(filePath);
                    LocalizationData data = JsonUtility.FromJson<LocalizationData>(jsonContent);
                    
                    Dictionary<string, string> languageDict = new Dictionary<string, string>();
                    foreach (var entry in data.entries)
                    {
                        languageDict[entry.key] = entry.value;
                    }
                    
                    languageData[language] = languageDict;
                    
                    Debug.Log($"📄 Loaded {languageDict.Count} entries for {language}");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load language file for {language}: {e.Message}");
                    CreateDefaultLanguageData(language);
                }
            }
            else
            {
                Debug.LogWarning($"Language file not found for {language}, creating default data");
                CreateDefaultLanguageData(language);
            }
        }
        
        private void CreateDefaultLanguageData(SystemLanguage language)
        {
            Dictionary<string, string> defaultData = GetDefaultLocalizationData(language);
            languageData[language] = defaultData;
            
            // Save default data to file
            SaveLanguageFile(language, defaultData);
        }
        
        #endregion
        
        #region Language Management
        
        public void SetLanguage(SystemLanguage language)
        {
            if (!supportedLanguages.Contains(language))
            {
                Debug.LogWarning($"Language {language} not supported, using {defaultLanguage}");
                language = defaultLanguage;
            }
            
            currentLanguage = language;
            
            // Load localized text for current language
            if (languageData.ContainsKey(language))
            {
                localizedText = languageData[language];
            }
            else
            {
                localizedText = languageData[defaultLanguage];
            }
            
            // Save language preference
            PlayerPrefs.SetInt("Language", (int)language);
            PlayerPrefs.Save();
            
            // Notify listeners
            OnLanguageChanged?.Invoke(language);
            
            Debug.Log($"🌍 Language changed to: {language}");
        }
        
        public string GetLocalizedText(string key, params object[] args)
        {
            if (localizedText.ContainsKey(key))
            {
                string text = localizedText[key];
                
                // Format with arguments if provided
                if (args.Length > 0)
                {
                    try
                    {
                        text = string.Format(text, args);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Failed to format localized text for key '{key}': {e.Message}");
                    }
                }
                
                return text;
            }
            else
            {
                Debug.LogWarning($"Localization key not found: {key}");
                return $"[{key}]"; // Return key in brackets to indicate missing translation
            }
        }
        
        public bool HasKey(string key)
        {
            return localizedText.ContainsKey(key);
        }
        
        #endregion
        
        #region File Operations
        
        private string GetLanguageFileName(SystemLanguage language)
        {
            return $"{language.ToString().ToLower()}{fileExtension}";
        }
        
        private void SaveLanguageFile(SystemLanguage language, Dictionary<string, string> data)
        {
            try
            {
                string folderPath = Path.Combine(Application.streamingAssetsPath, localizationFolder);
                
                // Create directory if it doesn't exist
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                
                LocalizationData locData = new LocalizationData();
                locData.entries = new List<LocalizationEntry>();
                
                foreach (var kvp in data)
                {
                    locData.entries.Add(new LocalizationEntry { key = kvp.Key, value = kvp.Value });
                }
                
                string json = JsonUtility.ToJson(locData, true);
                string filePath = Path.Combine(folderPath, GetLanguageFileName(language));
                
                File.WriteAllText(filePath, json);
                
                Debug.Log($"💾 Saved language file for {language}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save language file for {language}: {e.Message}");
            }
        }
        
        #endregion
        
        #region Default Localization Data
        
        private Dictionary<string, string> GetDefaultLocalizationData(SystemLanguage language)
        {
            Dictionary<string, string> data = new Dictionary<string, string>();
            
            switch (language)
            {
                case SystemLanguage.English:
                    data = GetEnglishData();
                    break;
                case SystemLanguage.French:
                    data = GetFrenchData();
                    break;
                case SystemLanguage.Spanish:
                    data = GetSpanishData();
                    break;
                case SystemLanguage.German:
                    data = GetGermanData();
                    break;
                case SystemLanguage.Japanese:
                    data = GetJapaneseData();
                    break;
                default:
                    data = GetEnglishData();
                    break;
            }
            
            return data;
        }
        
        private Dictionary<string, string> GetEnglishData()
        {
            return new Dictionary<string, string>
            {
                // UI
                {"ui.start", "Start"},
                {"ui.continue", "Continue"},
                {"ui.settings", "Settings"},
                {"ui.quit", "Quit"},
                {"ui.pause", "Pause"},
                {"ui.resume", "Resume"},
                {"ui.back", "Back"},
                {"ui.confirm", "Confirm"},
                {"ui.cancel", "Cancel"},
                
                // Game
                {"game.level", "Level"},
                {"game.health", "Health"},
                {"game.experience", "Experience"},
                {"game.currency", "Meta Currency"},
                {"game.room", "Room"},
                {"game.floor", "Floor"},
                {"game.time", "Time"},
                {"game.enemies_killed", "Enemies Killed"},
                
                // Classes
                {"class.cyber_warrior", "Cyber Warrior"},
                {"class.techno_mage", "Techno Mage"},
                {"class.dimensional_rogue", "Dimensional Rogue"},
                
                // Weapons
                {"weapon.plasma_sword", "Plasma Sword"},
                {"weapon.quantum_rifle", "Quantum Rifle"},
                {"weapon.nano_blade", "Nano Blade"},
                
                // Rooms
                {"room.start", "Start Room"},
                {"room.combat", "Combat Room"},
                {"room.boss", "Boss Room"},
                {"room.treasure", "Treasure Room"},
                {"room.shop", "Shop"},
                {"room.rest", "Rest Area"},
                {"room.secret", "Secret Room"},
                
                // Achievements
                {"achievement.first_kill", "First Blood"},
                {"achievement.enemy_slayer", "Enemy Slayer"},
                {"achievement.level_up", "Growing Stronger"},
                {"achievement.first_run", "First Steps"},
                
                // Messages
                {"msg.game_over", "Game Over"},
                {"msg.victory", "Victory!"},
                {"msg.level_up", "Level Up!"},
                {"msg.achievement_unlocked", "Achievement Unlocked!"},
                {"msg.item_found", "Item Found!"},
                {"msg.room_cleared", "Room Cleared!"}
            };
        }
        
        private Dictionary<string, string> GetFrenchData()
        {
            return new Dictionary<string, string>
            {
                // UI
                {"ui.start", "Commencer"},
                {"ui.continue", "Continuer"},
                {"ui.settings", "Paramètres"},
                {"ui.quit", "Quitter"},
                {"ui.pause", "Pause"},
                {"ui.resume", "Reprendre"},
                {"ui.back", "Retour"},
                {"ui.confirm", "Confirmer"},
                {"ui.cancel", "Annuler"},
                
                // Game
                {"game.level", "Niveau"},
                {"game.health", "Vie"},
                {"game.experience", "Expérience"},
                {"game.currency", "Méta-Monnaie"},
                {"game.room", "Salle"},
                {"game.floor", "Étage"},
                {"game.time", "Temps"},
                {"game.enemies_killed", "Ennemis Tués"},
                
                // Classes
                {"class.cyber_warrior", "Guerrier Cyber"},
                {"class.techno_mage", "Techno Mage"},
                {"class.dimensional_rogue", "Voleur Dimensionnel"},
                
                // Weapons
                {"weapon.plasma_sword", "Épée Plasma"},
                {"weapon.quantum_rifle", "Fusil Quantique"},
                {"weapon.nano_blade", "Lame Nano"},
                
                // Rooms
                {"room.start", "Salle de Départ"},
                {"room.combat", "Salle de Combat"},
                {"room.boss", "Salle du Boss"},
                {"room.treasure", "Salle au Trésor"},
                {"room.shop", "Boutique"},
                {"room.rest", "Zone de Repos"},
                {"room.secret", "Salle Secrète"},
                
                // Messages
                {"msg.game_over", "Fin de Partie"},
                {"msg.victory", "Victoire !"},
                {"msg.level_up", "Niveau Supérieur !"},
                {"msg.achievement_unlocked", "Succès Débloqué !"},
                {"msg.item_found", "Objet Trouvé !"},
                {"msg.room_cleared", "Salle Nettoyée !"}
            };
        }
        
        private Dictionary<string, string> GetSpanishData()
        {
            return new Dictionary<string, string>
            {
                {"ui.start", "Iniciar"},
                {"ui.continue", "Continuar"},
                {"ui.settings", "Configuración"},
                {"ui.quit", "Salir"},
                {"game.level", "Nivel"},
                {"game.health", "Salud"},
                {"msg.game_over", "Fin del Juego"},
                {"msg.victory", "¡Victoria!"}
            };
        }
        
        private Dictionary<string, string> GetGermanData()
        {
            return new Dictionary<string, string>
            {
                {"ui.start", "Starten"},
                {"ui.continue", "Fortsetzen"},
                {"ui.settings", "Einstellungen"},
                {"ui.quit", "Beenden"},
                {"game.level", "Level"},
                {"game.health", "Gesundheit"},
                {"msg.game_over", "Spiel Vorbei"},
                {"msg.victory", "Sieg!"}
            };
        }
        
        private Dictionary<string, string> GetJapaneseData()
        {
            return new Dictionary<string, string>
            {
                {"ui.start", "開始"},
                {"ui.continue", "続行"},
                {"ui.settings", "設定"},
                {"ui.quit", "終了"},
                {"game.level", "レベル"},
                {"game.health", "体力"},
                {"msg.game_over", "ゲームオーバー"},
                {"msg.victory", "勝利！"}
            };
        }
        
        #endregion
        
        #region Public Methods
        
        public SystemLanguage GetCurrentLanguage()
        {
            return currentLanguage;
        }
        
        public List<SystemLanguage> GetSupportedLanguages()
        {
            return new List<SystemLanguage>(supportedLanguages);
        }
        
        public void AddLocalizationEntry(string key, string value)
        {
            localizedText[key] = value;
            
            // Also add to current language data
            if (languageData.ContainsKey(currentLanguage))
            {
                languageData[currentLanguage][key] = value;
            }
        }
        
        public void RefreshLocalization()
        {
            LoadAllLanguages();
            SetLanguage(currentLanguage);
        }
        
        #endregion
    }
    
    /// <summary>
    /// Structure de données de localisation
    /// </summary>
    [System.Serializable]
    public class LocalizationData
    {
        public List<LocalizationEntry> entries = new List<LocalizationEntry>();
    }
    
    /// <summary>
    /// Entrée de localisation
    /// </summary>
    [System.Serializable]
    public class LocalizationEntry
    {
        public string key;
        public string value;
    }
}
