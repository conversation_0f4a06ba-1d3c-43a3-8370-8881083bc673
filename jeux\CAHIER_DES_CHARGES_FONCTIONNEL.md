# Cahier des Charges Fonctionnel - Space Clicker

**Projet** : <PERSON><PERSON>/Idle - Exploration Spatiale  
**Version** : 1.0  
**Date** : 24 juin 2025  

## 1. Présentation Générale

### 1.1 Concept du Jeu
**Space Clicker** est un jeu mobile casual de type clicker/idle où le joueur incarne le commandant d'une base spatiale d'exploration. L'objectif est de développer progressivement son empire spatial en collectant des ressources, améliorant ses installations et explorant de nouvelles planètes.

### 1.2 Public Cible
- **Âge** : 13-45 ans
- **Profil** : Joueurs casual, fans de jeux idle/incremental
- **Plateformes** : Android (API 21+), iOS (12.0+)
- **Temps de session** : 2-15 minutes par session

### 1.3 Objectifs du Jeu
- Collecter et optimiser la production de ressources
- Débloquer de nouvelles technologies et planètes
- Construire et améliorer sa flotte spatiale
- Progresser de façon continue même hors ligne

## 2. Gameplay Principal

### 2.1 Boucle de Gameplay Core

```
Collecter Ressources → Acheter Upgrades → Augmenter Production → Débloquer Contenu
       ↑                                                                    ↓
Optimiser Stratégie ←─────────────── Explorer Nouvelles Planètes ←─────────┘
```

### 2.2 Mécaniques de Base

#### 2.2.1 Collecte Manuelle (Clicker)
- **Tap sur modules** : Génère des ressources instantanément
- **Multiplicateur de clic** : Augmente avec les upgrades
- **Effets visuels** : Particules et animations de feedback
- **Son** : Bips spatiaux satisfaisants

#### 2.2.2 Production Automatique (Idle)
- **Extracteurs automatiques** : Produisent en continu
- **Taux de production** : Affiché en temps réel
- **Production hors ligne** : Continue pendant l'absence (max 1h)
- **Bonus de retour** : Popup avec récompenses accumulées

## 3. Système de Ressources

### 3.1 Ressources Principales

| Ressource | Icône | Description | Usage Principal |
|-----------|-------|-------------|-----------------|
| **Énergie** | ⚡ | Carburant de base | Faire fonctionner les modules |
| **Minéraux** | 💎 | Matériaux rares | Constructions et upgrades |
| **Données** | 📡 | Recherche scientifique | Débloquer technologies |
| **Crédits** | 💰 | Monnaie premium | Achats IAP, boosters |

### 3.2 Conversion et Échange
- **Ratio de base** : 1 Énergie = 0.1 Minéraux = 0.01 Données
- **Marché spatial** : Échange entre ressources (débloqué niveau 10)
- **Bonus temporaires** : Événements spéciaux avec taux améliorés

## 4. Système de Progression

### 4.1 Upgrades et Améliorations

#### 4.1.1 Modules de Production
| Module | Niveau Max | Effet | Coût Base |
|--------|------------|-------|-----------|
| **Réacteur Solaire** | 100 | +10% énergie/sec | 10 Énergie |
| **Foreuse Quantique** | 100 | +15% minéraux/sec | 50 Minéraux |
| **Antenne Deep Space** | 50 | +20% données/sec | 100 Données |
| **Générateur Fusion** | 25 | +50% production globale | 1000 Énergie |

#### 4.1.2 Technologies de Recherche
- **Efficacité Énergétique** : Réduit consommation modules (-5% par niveau)
- **Extraction Avancée** : Augmente rendement foreuses (+10% par niveau)
- **IA de Gestion** : Automatise certaines tâches
- **Propulsion Warp** : Débloque nouvelles planètes

### 4.2 Système de Prestige
- **Condition** : Atteindre 1M de chaque ressource
- **Récompense** : Multiplicateur permanent (+10% production)
- **Reset** : Repart à zéro mais avec bonus permanent
- **Prestige Points** : Monnaie spéciale pour upgrades uniques

## 5. Contenu et Progression

### 5.1 Planètes et Environnements

#### Phase 1 : Système Solaire
1. **Station Orbitale** (Tutoriel) - Niveaux 1-10
2. **Lune** (Facile) - Niveaux 11-25
3. **Mars** (Moyen) - Niveaux 26-50
4. **Europa** (Difficile) - Niveaux 51-75

#### Phase 2 : Exploration Galactique
5. **Proxima Centauri** - Niveaux 76-100
6. **Kepler-442b** - Niveaux 101-150
7. **Trappist-1e** - Niveaux 151-200

### 5.2 Missions et Objectifs

#### 5.2.1 Missions Quotidiennes
- Collecter X ressources
- Effectuer Y upgrades
- Rester connecté Z minutes
- **Récompenses** : Crédits premium, boosters

#### 5.2.2 Achievements
- **Collectionneur** : Collecter 1M de chaque ressource
- **Explorateur** : Débloquer toutes les planètes
- **Ingénieur** : Maxer tous les modules de base
- **Commandant** : Effectuer 10 prestiges

## 6. Monétisation

### 6.1 Publicités Récompensées

#### 6.1.1 Boost de Production
- **Durée** : 10 minutes
- **Effet** : x2 production toutes ressources
- **Cooldown** : 5 minutes
- **Limite** : 6 par jour

#### 6.1.2 Récompenses Instantanées
- **Ressources bonus** : +30 minutes de production
- **Accélération construction** : -50% temps restant
- **Multiplicateur de clic** : x5 pendant 2 minutes

### 6.2 Achats Intégrés (IAP)

| Pack | Prix | Contenu | Valeur |
|------|------|---------|--------|
| **Starter Pack** | 2,99€ | 1000 Crédits + Boost 24h | ⭐⭐⭐ |
| **Explorer Pack** | 9,99€ | 5000 Crédits + Modules premium | ⭐⭐⭐⭐ |
| **Commander Pack** | 19,99€ | 15000 Crédits + Prestige bonus | ⭐⭐⭐⭐⭐ |
| **Remove Ads** | 4,99€ | Supprime pubs + bonus permanent | ⭐⭐⭐⭐ |

### 6.3 Stratégie de Monétisation
- **Freemium** : Jeu gratuit avec achats optionnels
- **Pas de pay-to-win** : Progression possible sans achats
- **Valeur ajoutée** : IAP accélèrent mais ne bloquent pas
- **Respect du joueur** : Pas de murs de paiement

## 7. Interface Utilisateur

### 7.1 Écran Principal

```
┌─────────────────────────────────────┐
│ [⚡ 1.2K] [💎 456] [📡 78] [💰 12] │ ← Ressources
├─────────────────────────────────────┤
│                                     │
│        🚀 STATION SPATIALE 🚀       │ ← Vue principale
│                                     │
│   [⚡ Réacteur] [💎 Foreuse]        │ ← Modules cliquables
│                                     │
│   [📡 Antenne] [🔧 Labo]           │
│                                     │
├─────────────────────────────────────┤
│ [Upgrades] [Recherche] [Planètes]   │ ← Navigation
└─────────────────────────────────────┘
```

### 7.2 Écrans Secondaires

#### 7.2.1 Menu Upgrades
- Liste scrollable des améliorations
- Filtres par type (Production, Efficacité, Spécial)
- Aperçu des effets avant achat
- Indicateur "Recommandé" pour optimiser

#### 7.2.2 Arbre de Recherche
- Interface en arbre/grille
- Prérequis visuels (lignes de connexion)
- Temps de recherche en temps réel
- Possibilité d'accélérer avec crédits

#### 7.2.3 Carte Galactique
- Vue d'ensemble des planètes
- Progression visuelle (étoiles, pourcentages)
- Conditions de débloquage affichées
- Aperçu des récompenses

## 8. Expérience Utilisateur

### 8.1 Onboarding et Tutoriel

#### Étape 1 : Premier Contact (30 secondes)
1. Animation d'intro spatiale
2. "Tapez pour collecter de l'énergie"
3. Premier achat d'upgrade
4. "Félicitations ! Votre base produit maintenant automatiquement"

#### Étape 2 : Mécaniques Core (2 minutes)
1. Introduction des 3 ressources
2. Premier upgrade de module
3. Déblocage du laboratoire
4. Première recherche

#### Étape 3 : Progression (5 minutes)
1. Objectif : Débloquer la Lune
2. Introduction des missions
3. Première publicité récompensée
4. Système de prestige expliqué

### 8.2 Rétention et Engagement

#### 8.2.1 Hooks de Rétention
- **Progression visible** : Barres de progression, compteurs
- **Récompenses fréquentes** : Toutes les 30 secondes minimum
- **Objectifs courts** : Missions de 2-5 minutes
- **FOMO** : Événements temporaires, bonus quotidiens

#### 8.2.2 Notifications Push
- **Ressources prêtes** : "Vos extracteurs ont collecté 2.5K énergie !"
- **Recherche terminée** : "Nouvelle technologie débloquée !"
- **Missions quotidiennes** : "3 nouvelles missions vous attendent"
- **Événements** : "Bonus x2 production pendant 2h !"

## 9. Métriques et Balancing

### 9.1 Courbes de Progression
- **Croissance exponentielle** : Coûts x1.15 par niveau
- **Plateau évités** : Nouveaux contenus tous les 10 niveaux
- **Temps de session** : Objectifs atteignables en 5-10 minutes
- **Progression hors ligne** : 20-30% de la progression active

### 9.2 Économie du Jeu
- **Inflation contrôlée** : Prestige reset pour éviter les nombres trop grands
- **Équilibre ressources** : Aucune ressource ne doit être inutile
- **Choix stratégiques** : Plusieurs chemins d'optimisation viables
- **Respect du temps** : Progression significative même en sessions courtes

## 10. Événements et Contenu Live

### 10.1 Événements Saisonniers
- **Exploration Week** : Bonus découverte planètes
- **Research Festival** : Temps de recherche réduit
- **Harvest Moon** : Production minéraux doublée
- **Space Race** : Classements communautaires

### 10.2 Mises à Jour Prévues
- **Nouvelles planètes** : Tous les 2 mois
- **Modules spéciaux** : Événements mensuels
- **Fonctionnalités sociales** : Guildes, échanges
- **Mode PvP** : Compétitions de progression
