using UnityEngine;
using System.Collections.Generic;
using ChronoForge.Progression;

namespace ChronoForge.Core
{
    /// <summary>
    /// Structure complète des données de sauvegarde pour ChronoForge
    /// </summary>
    [System.Serializable]
    public class GameSaveData
    {
        [Header("Save Metadata")]
        public string saveVersion = "1.0.0";
        public long saveTimestamp;
        public float totalPlayTime;
        public string saveDescription = "";
        
        [<PERSON><PERSON>("Progression Data")]
        public PlayerProgressionData progressionData;
        
        [Header("Settings")]
        public PlayerSettings playerSettings;
        public GameSettings gameSettings;
        
        [<PERSON>er("Current Run")]
        public CurrentRunData currentRunData;
        
        [Header("Statistics")]
        public GameStatistics statistics;
        
        /// <summary>
        /// Obtient la date de sauvegarde formatée
        /// </summary>
        public string GetFormattedSaveDate()
        {
            System.DateTime saveDate = System.DateTime.FromBinary(saveTimestamp);
            return saveDate.ToString("dd/MM/yyyy HH:mm");
        }
        
        /// <summary>
        /// Obtient le temps de jeu formaté
        /// </summary>
        public string GetFormattedPlayTime()
        {
            int hours = Mathf.FloorToInt(totalPlayTime / 3600f);
            int minutes = Mathf.FloorToInt((totalPlayTime % 3600f) / 60f);
            
            if (hours > 0)
                return $"{hours}h {minutes}m";
            else
                return $"{minutes}m";
        }
        
        /// <summary>
        /// Vérifie si la sauvegarde est compatible avec la version actuelle
        /// </summary>
        public bool IsCompatible()
        {
            // Simple version check - in a real game you'd have more sophisticated logic
            return saveVersion == Application.version;
        }
    }
    
    /// <summary>
    /// Paramètres du joueur
    /// </summary>
    [System.Serializable]
    public class PlayerSettings
    {
        [Header("Audio Settings")]
        public float masterVolume = 1f;
        public float musicVolume = 0.7f;
        public float sfxVolume = 0.8f;
        public float ambientVolume = 0.5f;
        public float uiVolume = 1f;
        
        [Header("Display Settings")]
        public bool fullscreen = true;
        public Vector2Int resolution = new Vector2Int(1920, 1080);
        public int refreshRate = 60;
        public bool vsync = true;
        public int qualityLevel = 2;
        
        [Header("Gameplay Settings")]
        public bool showDamageNumbers = true;
        public bool screenShakeEnabled = true;
        public bool autoAim = false;
        public float mouseSensitivity = 1f;
        
        [Header("UI Settings")]
        public bool showMinimap = true;
        public bool showHealthBar = true;
        public bool showCrosshair = true;
        public float uiScale = 1f;
        
        [Header("Accessibility")]
        public bool colorBlindMode = false;
        public bool highContrastMode = false;
        public float textSize = 1f;
        public bool subtitlesEnabled = false;
    }
    
    /// <summary>
    /// Paramètres de jeu
    /// </summary>
    [System.Serializable]
    public class GameSettings
    {
        [Header("Difficulty")]
        public string difficulty = "Normal";
        public bool permadeathMode = false;
        public bool ironmanMode = false;
        
        [Header("Gameplay")]
        public bool pauseOnFocusLoss = true;
        public bool autoSaveEnabled = true;
        public float autoSaveInterval = 300f; // 5 minutes
        
        [Header("Tutorial")]
        public bool showTutorials = true;
        public bool showHints = true;
        public bool showTooltips = true;
        
        [Header("Debug")]
        public bool debugMode = false;
        public bool showFPS = false;
        public bool showDebugInfo = false;
    }
    
    /// <summary>
    /// Données de la run actuelle
    /// </summary>
    [System.Serializable]
    public class CurrentRunData
    {
        [Header("Run State")]
        public bool isInRun = false;
        public int runNumber = 0;
        public int currentFloor = 1;
        public int currentRoom = 1;
        public float runStartTime = 0f;
        public string selectedBiome = "";
        
        [Header("Player State")]
        public Vector3 playerPosition = Vector3.zero;
        public string playerClass = "CyberWarrior";
        public int runLevel = 1;
        public int runExperience = 0;
        public float currentHealth = 100f;
        public float currentShield = 0f;
        
        [Header("Equipment")]
        public string currentWeapon = "PlasmaSword";
        public List<string> inventory = new List<string>();
        public List<string> artifacts = new List<string>();
        public List<string> temporaryUpgrades = new List<string>();
        
        [Header("Run Progress")]
        public int enemiesKilled = 0;
        public int roomsCleared = 0;
        public int itemsFound = 0;
        public int secretsDiscovered = 0;
        public List<string> visitedRooms = new List<string>();
        public List<string> clearedRooms = new List<string>();
        
        [Header("Run Modifiers")]
        public List<string> activeModifiers = new List<string>();
        public Dictionary<string, float> runStats = new Dictionary<string, float>();
        
        /// <summary>
        /// Obtient le temps de run actuel
        /// </summary>
        public float GetCurrentRunTime()
        {
            return Time.time - runStartTime;
        }
        
        /// <summary>
        /// Obtient le temps de run formaté
        /// </summary>
        public string GetFormattedRunTime()
        {
            float runTime = GetCurrentRunTime();
            int minutes = Mathf.FloorToInt(runTime / 60f);
            int seconds = Mathf.FloorToInt(runTime % 60f);
            return $"{minutes:00}:{seconds:00}";
        }
    }
    
    /// <summary>
    /// Statistiques globales du jeu
    /// </summary>
    [System.Serializable]
    public class GameStatistics
    {
        [Header("General Stats")]
        public int totalSessions = 0;
        public float totalPlayTime = 0f;
        public int totalClicks = 0;
        public int totalKeyPresses = 0;
        
        [Header("Run Stats")]
        public int totalRuns = 0;
        public int successfulRuns = 0;
        public int failedRuns = 0;
        public float averageRunTime = 0f;
        public float bestRunTime = 0f;
        public int longestRunFloors = 0;
        
        [Header("Combat Stats")]
        public int totalEnemiesKilled = 0;
        public int totalBossesDefeated = 0;
        public float totalDamageDealt = 0f;
        public float totalDamageTaken = 0f;
        public int totalDeaths = 0;
        public int totalCriticalHits = 0;
        
        [Header("Collection Stats")]
        public int totalItemsFound = 0;
        public int totalArtifactsCollected = 0;
        public int totalSecretsFound = 0;
        public int totalChestsOpened = 0;
        
        [Header("Progression Stats")]
        public int highestLevelReached = 1;
        public int totalExperienceGained = 0;
        public int totalMetaCurrencyEarned = 0;
        public int totalMetaCurrencySpent = 0;
        
        [Header("Achievement Stats")]
        public int achievementsUnlocked = 0;
        public int hiddenAchievementsFound = 0;
        public float achievementCompletionRate = 0f;
        
        /// <summary>
        /// Met à jour les statistiques après une run
        /// </summary>
        public void UpdateRunStats(bool successful, float runTime, int floorsReached, int enemiesKilled)
        {
            totalRuns++;
            
            if (successful)
            {
                successfulRuns++;
                
                if (bestRunTime == 0f || runTime < bestRunTime)
                {
                    bestRunTime = runTime;
                }
            }
            else
            {
                failedRuns++;
            }
            
            // Update averages
            averageRunTime = (averageRunTime * (totalRuns - 1) + runTime) / totalRuns;
            
            // Update records
            longestRunFloors = Mathf.Max(longestRunFloors, floorsReached);
            totalEnemiesKilled += enemiesKilled;
        }
        
        /// <summary>
        /// Obtient le taux de réussite
        /// </summary>
        public float GetSuccessRate()
        {
            return totalRuns > 0 ? (float)successfulRuns / totalRuns : 0f;
        }
        
        /// <summary>
        /// Obtient les KDA (Kills/Deaths/Assists)
        /// </summary>
        public float GetKDRatio()
        {
            return totalDeaths > 0 ? (float)totalEnemiesKilled / totalDeaths : totalEnemiesKilled;
        }
    }
    
    /// <summary>
    /// Informations sur un slot de sauvegarde
    /// </summary>
    [System.Serializable]
    public class SaveSlotInfo
    {
        public int slotIndex;
        public bool isEmpty = true;
        public bool isCorrupted = false;
        public System.DateTime saveDate;
        public float playTime;
        public int playerLevel;
        public bool isInRun;
        public long fileSize;
        public string saveDescription;
        public string gameVersion;
        
        /// <summary>
        /// Obtient la description du slot
        /// </summary>
        public string GetSlotDescription()
        {
            if (isEmpty)
                return "Empty Slot";
            
            if (isCorrupted)
                return "Corrupted Save";
            
            string desc = $"Level {playerLevel}";
            
            if (isInRun)
                desc += " (In Run)";
            
            return desc;
        }
        
        /// <summary>
        /// Obtient la taille du fichier formatée
        /// </summary>
        public string GetFormattedFileSize()
        {
            if (fileSize < 1024)
                return $"{fileSize} B";
            else if (fileSize < 1024 * 1024)
                return $"{fileSize / 1024:F1} KB";
            else
                return $"{fileSize / (1024 * 1024):F1} MB";
        }
    }
    
    /// <summary>
    /// Données de run pour la sauvegarde
    /// </summary>
    [System.Serializable]
    public class RunData
    {
        public int runId;
        public string biome;
        public int floor;
        public int room;
        public float startTime;
        public bool isCompleted;
        public List<string> roomsVisited;
        public Dictionary<string, object> runState;
    }
}
