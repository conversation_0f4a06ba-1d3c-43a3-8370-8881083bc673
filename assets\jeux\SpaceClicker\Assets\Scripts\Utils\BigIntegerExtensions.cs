using System.Numerics;

namespace SpaceClicker.Utils
{
    /// <summary>
    /// Extensions pour BigInteger dans SpaceClicker
    /// </summary>
    public static class BigIntegerExtensions
    {
        private static readonly string[] suffixes = new string[]
        {
            "", "K", "M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc",
            "UDc", "DDc", "TDc", "QaDc", "QiDc", "SxDc", "SpDc", "OcDc", "NoDc", "Vg",
            "UVg", "DVg", "TVg", "QaVg", "QiVg", "SxVg", "SpVg", "OcVg", "NoVg", "Tg"
        };
        
        /// <summary>
        /// Formate un BigInteger en notation scientifique simplifiée
        /// </summary>
        public static string ToShortString(this BigInteger value)
        {
            if (value < 1000)
                return value.ToString();
            
            double doubleValue = (double)value;
            int suffixIndex = 0;
            
            while (doubleValue >= 1000 && suffixIndex < suffixes.Length - 1)
            {
                doubleValue /= 1000;
                suffixIndex++;
            }
            
            if (suffixIndex >= suffixes.Length)
            {
                // Use scientific notation for very large numbers
                return value.ToString("E2");
            }
            
            return $"{doubleValue:F2}{suffixes[suffixIndex]}";
        }
        
        /// <summary>
        /// Formate un BigInteger avec des séparateurs de milliers
        /// </summary>
        public static string ToFormattedString(this BigInteger value)
        {
            return value.ToString("N0");
        }
        
        /// <summary>
        /// Convertit un BigInteger en float (avec perte de précision possible)
        /// </summary>
        public static float ToFloat(this BigInteger value)
        {
            if (value > float.MaxValue)
                return float.MaxValue;
            if (value < float.MinValue)
                return float.MinValue;
            
            return (float)value;
        }
        
        /// <summary>
        /// Convertit un BigInteger en double (avec perte de précision possible)
        /// </summary>
        public static double ToDouble(this BigInteger value)
        {
            if (value > (BigInteger)double.MaxValue)
                return double.MaxValue;
            if (value < (BigInteger)double.MinValue)
                return double.MinValue;
            
            return (double)value;
        }
        
        /// <summary>
        /// Vérifie si un BigInteger peut être représenté comme int
        /// </summary>
        public static bool CanBeInt(this BigInteger value)
        {
            return value >= int.MinValue && value <= int.MaxValue;
        }
        
        /// <summary>
        /// Convertit en int si possible, sinon retourne la valeur par défaut
        /// </summary>
        public static int ToIntOrDefault(this BigInteger value, int defaultValue = 0)
        {
            return value.CanBeInt() ? (int)value : defaultValue;
        }
        
        /// <summary>
        /// Calcule un pourcentage d'un BigInteger
        /// </summary>
        public static BigInteger Percentage(this BigInteger value, float percentage)
        {
            return (BigInteger)((double)value * (percentage / 100.0));
        }
        
        /// <summary>
        /// Clamp un BigInteger entre min et max
        /// </summary>
        public static BigInteger Clamp(this BigInteger value, BigInteger min, BigInteger max)
        {
            if (value < min) return min;
            if (value > max) return max;
            return value;
        }
        
        /// <summary>
        /// Retourne le plus grand entre deux BigInteger
        /// </summary>
        public static BigInteger Max(BigInteger a, BigInteger b)
        {
            return a > b ? a : b;
        }
        
        /// <summary>
        /// Retourne le plus petit entre deux BigInteger
        /// </summary>
        public static BigInteger Min(BigInteger a, BigInteger b)
        {
            return a < b ? a : b;
        }
        
        /// <summary>
        /// Calcule la racine carrée approximative d'un BigInteger
        /// </summary>
        public static BigInteger Sqrt(this BigInteger value)
        {
            if (value == 0) return 0;
            if (value < 0) throw new System.ArgumentException("Cannot calculate square root of negative number");
            
            BigInteger x = value;
            BigInteger y = (x + 1) / 2;
            
            while (y < x)
            {
                x = y;
                y = (x + value / x) / 2;
            }
            
            return x;
        }
        
        /// <summary>
        /// Calcule une puissance approximative pour BigInteger
        /// </summary>
        public static BigInteger Power(this BigInteger baseValue, int exponent)
        {
            if (exponent < 0) throw new System.ArgumentException("Exponent must be non-negative");
            if (exponent == 0) return 1;
            if (exponent == 1) return baseValue;
            
            BigInteger result = 1;
            BigInteger currentBase = baseValue;
            
            while (exponent > 0)
            {
                if (exponent % 2 == 1)
                {
                    result *= currentBase;
                }
                currentBase *= currentBase;
                exponent /= 2;
            }
            
            return result;
        }
        
        /// <summary>
        /// Formate pour l'affichage dans l'UI avec unités appropriées
        /// </summary>
        public static string ToUIString(this BigInteger value, bool useShortFormat = true)
        {
            if (useShortFormat)
            {
                return value.ToShortString();
            }
            else
            {
                return value.ToFormattedString();
            }
        }
        
        /// <summary>
        /// Parse une string vers BigInteger avec gestion d'erreur
        /// </summary>
        public static bool TryParseBigInteger(string value, out BigInteger result)
        {
            return BigInteger.TryParse(value, out result);
        }

        /// <summary>
        /// Formate en string compacte pour les tests
        /// </summary>
        public static string ToCompactString(this BigInteger value)
        {
            return value.ToShortString();
        }

        /// <summary>
        /// Formate en string d'affichage pour les tests
        /// </summary>
        public static string ToDisplayString(this BigInteger value)
        {
            return value.ToFormattedString();
        }
    }
}
