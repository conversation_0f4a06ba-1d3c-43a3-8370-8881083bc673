using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using ChronoForge.Player;

namespace ChronoForge.Combat
{
    /// <summary>
    /// Intelligence artificielle des ennemis pour ChronoForge
    /// </summary>
    [RequireComponent(typeof(HealthSystem))]
    [RequireComponent(typeof(Rigidbody2D))]
    public class EnemyAI : MonoBehaviour
    {
        [Header("Enemy Type")]
        public EnemyType enemyType = EnemyType.Grunt;
        public string enemyName = "Enemy";
        public bool isBoss = false;
        
        [Header("Movement")]
        public float moveSpeed = 3f;
        public float acceleration = 20f;
        public float stoppingDistance = 1.5f;
        public float wanderRadius = 5f;
        
        [Header("Combat")]
        public float attackDamage = 10f;
        public float attackRange = 2f;
        public float attackCooldown = 1.5f;
        public float detectionRange = 8f;
        public LayerMask playerLayer = -1;
        
        [Header("Behavior")]
        public AIBehavior currentBehavior = AIBehavior.Patrol;
        public float aggroTime = 5f;
        public float patrolWaitTime = 2f;
        public bool canFly = false;
        
        [Header("Visual")]
        public SpriteRenderer spriteRenderer;
        public Animator animator;
        public GameObject attackEffect;
        public GameObject deathEffect;
        
        [Header("Audio")]
        public AudioSource audioSource;
        public AudioClip attackSound;
        public AudioClip hurtSound;
        public AudioClip deathSound;
        public AudioClip aggroSound;
        
        // Events
        public static System.Action<EnemyAI> OnEnemyDeath;
        public static System.Action<EnemyAI> OnEnemyAggro;
        public static System.Action<EnemyAI, float> OnEnemyAttack;
        
        // Components
        private Rigidbody2D rb;
        private HealthSystem healthSystem;
        private PlayerController targetPlayer;
        
        // AI State
        private Vector3 startPosition;
        private Vector3 patrolTarget;
        private Vector3 lastKnownPlayerPosition;
        private float lastAttackTime = 0f;
        private float aggroTimer = 0f;
        private float patrolTimer = 0f;
        private bool isAlive = true;
        private bool hasTarget = false;
        
        // Movement
        private Vector2 moveDirection;
        private bool isMoving = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeEnemy();
        }
        
        private void Start()
        {
            SetupEnemy();
        }
        
        private void Update()
        {
            if (!isAlive) return;
            
            UpdateAI();
            UpdateAnimation();
        }
        
        private void FixedUpdate()
        {
            if (!isAlive) return;
            
            HandleMovement();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeEnemy()
        {
            rb = GetComponent<Rigidbody2D>();
            healthSystem = GetComponent<HealthSystem>();
            
            if (spriteRenderer == null)
                spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            
            if (animator == null)
                animator = GetComponentInChildren<Animator>();
            
            if (audioSource == null)
                audioSource = GetComponent<AudioSource>();
            
            // Configure rigidbody
            rb.gravityScale = 0f;
            rb.drag = 5f;
            rb.freezeRotation = true;
            
            // Subscribe to health events
            if (healthSystem != null)
            {
                healthSystem.OnDeath += HandleDeath;
                healthSystem.OnDamageTaken += HandleDamageTaken;
            }
        }
        
        private void SetupEnemy()
        {
            startPosition = transform.position;
            patrolTarget = GetRandomPatrolPoint();
            
            // Find player
            targetPlayer = FindFirstObjectByType<PlayerController>();
            
            // Set initial behavior
            SetBehavior(AIBehavior.Patrol);
            
            UnityEngine.Debug.Log($"👹 Enemy AI initialized: {enemyName} ({enemyType})");
        }
        
        #endregion
        
        #region AI Update
        
        private void UpdateAI()
        {
            // Check for player detection
            CheckPlayerDetection();
            
            // Update behavior
            UpdateBehavior();
            
            // Update timers
            UpdateTimers();
        }
        
        private void CheckPlayerDetection()
        {
            if (targetPlayer == null || !targetPlayer.IsAlive()) return;
            
            float distanceToPlayer = Vector3.Distance(transform.position, targetPlayer.transform.position);
            
            // Check if player is in detection range
            if (distanceToPlayer <= detectionRange)
            {
                // Check line of sight
                if (HasLineOfSight(targetPlayer.transform.position))
                {
                    if (!hasTarget)
                    {
                        // First time detecting player
                        OnPlayerDetected();
                    }
                    
                    hasTarget = true;
                    lastKnownPlayerPosition = targetPlayer.transform.position;
                    aggroTimer = aggroTime;
                    
                    // Switch to chase behavior if not already aggressive
                    if (currentBehavior == AIBehavior.Patrol || currentBehavior == AIBehavior.Wander)
                    {
                        SetBehavior(AIBehavior.Chase);
                    }
                }
            }
        }
        
        private bool HasLineOfSight(Vector3 targetPosition)
        {
            Vector3 direction = (targetPosition - transform.position).normalized;
            float distance = Vector3.Distance(transform.position, targetPosition);
            
            RaycastHit2D hit = Physics2D.Raycast(transform.position, direction, distance, ~playerLayer);
            
            return hit.collider == null; // No obstacles in the way
        }
        
        private void OnPlayerDetected()
        {
            // Play aggro sound
            if (audioSource != null && aggroSound != null)
            {
                audioSource.PlayOneShot(aggroSound);
            }
            
            OnEnemyAggro?.Invoke(this);
            
            UnityEngine.Debug.Log($"👹 {enemyName} detected player!");
        }
        
        #endregion
        
        #region Behavior System
        
        private void UpdateBehavior()
        {
            switch (currentBehavior)
            {
                case AIBehavior.Patrol:
                    UpdatePatrolBehavior();
                    break;
                case AIBehavior.Wander:
                    UpdateWanderBehavior();
                    break;
                case AIBehavior.Chase:
                    UpdateChaseBehavior();
                    break;
                case AIBehavior.Attack:
                    UpdateAttackBehavior();
                    break;
                case AIBehavior.Flee:
                    UpdateFleeBehavior();
                    break;
                case AIBehavior.Stunned:
                    UpdateStunnedBehavior();
                    break;
            }
        }
        
        private void UpdatePatrolBehavior()
        {
            // Move towards patrol target
            MoveTowards(patrolTarget);
            
            // Check if reached patrol target
            if (Vector3.Distance(transform.position, patrolTarget) < 0.5f)
            {
                patrolTimer = patrolWaitTime;
                SetBehavior(AIBehavior.Wander);
            }
        }
        
        private void UpdateWanderBehavior()
        {
            // Wait at patrol point
            if (patrolTimer <= 0f)
            {
                patrolTarget = GetRandomPatrolPoint();
                SetBehavior(AIBehavior.Patrol);
            }
        }
        
        private void UpdateChaseBehavior()
        {
            if (!hasTarget)
            {
                SetBehavior(AIBehavior.Patrol);
                return;
            }
            
            float distanceToPlayer = Vector3.Distance(transform.position, lastKnownPlayerPosition);
            
            // Check if close enough to attack
            if (distanceToPlayer <= attackRange)
            {
                SetBehavior(AIBehavior.Attack);
            }
            else
            {
                // Chase player
                MoveTowards(lastKnownPlayerPosition);
            }
        }
        
        private void UpdateAttackBehavior()
        {
            if (!hasTarget)
            {
                SetBehavior(AIBehavior.Patrol);
                return;
            }
            
            float distanceToPlayer = Vector3.Distance(transform.position, lastKnownPlayerPosition);
            
            // Check if still in attack range
            if (distanceToPlayer > attackRange)
            {
                SetBehavior(AIBehavior.Chase);
                return;
            }
            
            // Stop moving
            moveDirection = Vector2.zero;
            
            // Attack if cooldown is ready
            if (Time.time - lastAttackTime >= attackCooldown)
            {
                PerformAttack();
            }
        }
        
        private void UpdateFleeBehavior()
        {
            if (!hasTarget)
            {
                SetBehavior(AIBehavior.Patrol);
                return;
            }
            
            // Move away from player
            Vector3 fleeDirection = (transform.position - lastKnownPlayerPosition).normalized;
            Vector3 fleeTarget = transform.position + fleeDirection * 5f;
            
            MoveTowards(fleeTarget);
        }
        
        private void UpdateStunnedBehavior()
        {
            // Can't move while stunned
            moveDirection = Vector2.zero;
        }
        
        private void SetBehavior(AIBehavior newBehavior)
        {
            if (currentBehavior == newBehavior) return;
            
            currentBehavior = newBehavior;
            
            // Behavior-specific setup
            switch (newBehavior)
            {
                case AIBehavior.Attack:
                    // Face the player
                    FaceTarget(lastKnownPlayerPosition);
                    break;
            }
        }
        
        #endregion
        
        #region Movement
        
        private void MoveTowards(Vector3 target)
        {
            Vector3 direction = (target - transform.position).normalized;
            moveDirection = direction;
            isMoving = true;
        }
        
        private void HandleMovement()
        {
            if (moveDirection != Vector2.zero)
            {
                // Apply movement
                Vector2 targetVelocity = moveDirection * moveSpeed;
                rb.velocity = Vector2.MoveTowards(rb.velocity, targetVelocity, acceleration * Time.fixedDeltaTime);
                
                // Face movement direction
                FaceDirection(moveDirection);
            }
            else
            {
                // Decelerate
                rb.velocity = Vector2.MoveTowards(rb.velocity, Vector2.zero, acceleration * Time.fixedDeltaTime);
                isMoving = rb.velocity.magnitude > 0.1f;
            }
        }
        
        private void FaceDirection(Vector2 direction)
        {
            if (spriteRenderer != null && direction.x != 0)
            {
                spriteRenderer.flipX = direction.x < 0;
            }
        }
        
        private void FaceTarget(Vector3 target)
        {
            Vector3 direction = (target - transform.position).normalized;
            FaceDirection(direction);
        }
        
        private Vector3 GetRandomPatrolPoint()
        {
            Vector2 randomDirection = Random.insideUnitCircle * wanderRadius;
            return startPosition + (Vector3)randomDirection;
        }
        
        #endregion
        
        #region Combat
        
        private void PerformAttack()
        {
            lastAttackTime = Time.time;
            
            // Play attack animation
            if (animator != null)
            {
                animator.SetTrigger("Attack");
            }
            
            // Play attack sound
            if (audioSource != null && attackSound != null)
            {
                audioSource.PlayOneShot(attackSound);
            }
            
            // Deal damage to player
            if (targetPlayer != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, targetPlayer.transform.position);
                
                if (distanceToPlayer <= attackRange)
                {
                    HealthSystem playerHealth = targetPlayer.GetComponent<HealthSystem>();
                    if (playerHealth != null)
                    {
                        playerHealth.TakeDamage(attackDamage);
                    }
                    
                    // Spawn attack effect
                    if (attackEffect != null)
                    {
                        Instantiate(attackEffect, targetPlayer.transform.position, Quaternion.identity);
                    }
                }
            }
            
            OnEnemyAttack?.Invoke(this, attackDamage);
            
            UnityEngine.Debug.Log($"👹 {enemyName} attacked for {attackDamage} damage!");
        }
        
        #endregion
        
        #region Health Events
        
        private void HandleDamageTaken(float damage)
        {
            // Play hurt sound
            if (audioSource != null && hurtSound != null)
            {
                audioSource.PlayOneShot(hurtSound);
            }
            
            // Play hurt animation
            if (animator != null)
            {
                animator.SetTrigger("Hurt");
            }
            
            // Become aggressive if not already
            if (currentBehavior == AIBehavior.Patrol || currentBehavior == AIBehavior.Wander)
            {
                if (targetPlayer != null)
                {
                    hasTarget = true;
                    lastKnownPlayerPosition = targetPlayer.transform.position;
                    aggroTimer = aggroTime;
                    SetBehavior(AIBehavior.Chase);
                }
            }
        }
        
        private void HandleDeath()
        {
            if (!isAlive) return;
            
            isAlive = false;
            
            // Play death sound
            if (audioSource != null && deathSound != null)
            {
                audioSource.PlayOneShot(deathSound);
            }
            
            // Play death animation
            if (animator != null)
            {
                animator.SetTrigger("Death");
            }
            
            // Spawn death effect
            if (deathEffect != null)
            {
                Instantiate(deathEffect, transform.position, Quaternion.identity);
            }
            
            // Stop movement
            rb.velocity = Vector2.zero;
            moveDirection = Vector2.zero;
            
            // Disable collider
            Collider2D col = GetComponent<Collider2D>();
            if (col != null)
            {
                col.enabled = false;
            }
            
            OnEnemyDeath?.Invoke(this);
            
            UnityEngine.Debug.Log($"💀 {enemyName} died!");
            
            // Destroy after delay
            StartCoroutine(DestroyAfterDelay(2f));
        }
        
        private IEnumerator DestroyAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            Destroy(gameObject);
        }
        
        #endregion
        
        #region Animation
        
        private void UpdateAnimation()
        {
            if (animator == null) return;
            
            // Movement animation
            animator.SetBool("IsMoving", isMoving);
            animator.SetFloat("MoveSpeed", rb.velocity.magnitude);
            
            // Behavior animation
            animator.SetBool("IsAggressive", hasTarget);
            animator.SetBool("IsAttacking", currentBehavior == AIBehavior.Attack);
        }
        
        #endregion
        
        #region Timers
        
        private void UpdateTimers()
        {
            // Aggro timer
            if (hasTarget)
            {
                aggroTimer -= Time.deltaTime;
                if (aggroTimer <= 0f)
                {
                    hasTarget = false;
                    SetBehavior(AIBehavior.Patrol);
                }
            }
            
            // Patrol timer
            if (currentBehavior == AIBehavior.Wander)
            {
                patrolTimer -= Time.deltaTime;
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetStunned(float duration)
        {
            SetBehavior(AIBehavior.Stunned);
            StartCoroutine(StunCoroutine(duration));
        }
        
        private IEnumerator StunCoroutine(float duration)
        {
            yield return new WaitForSeconds(duration);
            
            if (isAlive)
            {
                SetBehavior(hasTarget ? AIBehavior.Chase : AIBehavior.Patrol);
            }
        }
        
        public void ForceAggro(Vector3 playerPosition)
        {
            hasTarget = true;
            lastKnownPlayerPosition = playerPosition;
            aggroTimer = aggroTime;
            SetBehavior(AIBehavior.Chase);
        }
        
        public bool IsAlive()
        {
            return isAlive;
        }
        
        public bool HasTarget()
        {
            return hasTarget;
        }
        
        public AIBehavior GetCurrentBehavior()
        {
            return currentBehavior;
        }
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            // Detection range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // Attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
            
            // Patrol radius
            Gizmos.color = Color.blue;
            Vector3 patrolCenter = Application.isPlaying ? startPosition : transform.position;
            Gizmos.DrawWireSphere(patrolCenter, wanderRadius);
            
            // Current target
            if (Application.isPlaying)
            {
                if (currentBehavior == AIBehavior.Patrol)
                {
                    Gizmos.color = Color.green;
                    Gizmos.DrawLine(transform.position, patrolTarget);
                    Gizmos.DrawSphere(patrolTarget, 0.3f);
                }
                
                if (hasTarget)
                {
                    Gizmos.color = Color.red;
                    Gizmos.DrawLine(transform.position, lastKnownPlayerPosition);
                }
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Types d'ennemis
    /// </summary>
    public enum EnemyType
    {
        Grunt,          // Ennemi de base
        Ranger,         // Ennemi à distance
        Tank,           // Ennemi résistant
        Assassin,       // Ennemi rapide
        Mage,           // Ennemi magique
        Boss,           // Boss
        Elite,          // Ennemi élite
        Swarm           // Ennemi en essaim
    }
    
    /// <summary>
    /// Comportements d'IA
    /// </summary>
    public enum AIBehavior
    {
        Patrol,         // Patrouille
        Wander,         // Errance
        Chase,          // Poursuite
        Attack,         // Attaque
        Flee,           // Fuite
        Stunned,        // Étourdi
        Guard,          // Garde
        Investigate     // Investigation
    }
}
