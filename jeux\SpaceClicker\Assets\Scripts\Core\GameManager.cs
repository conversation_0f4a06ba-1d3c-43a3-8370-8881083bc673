using System;
using UnityEngine;
using System.Collections;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Gestionnaire principal du jeu - Point d'entrée et coordination des systèmes
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public float autoSaveInterval = 30f;
        public bool enableDebugMode = false;
        
        [Header("References")]
        public ResourceManager resourceManager;
        public UpgradeManager upgradeManager;
        public UIManager uiManager;
        public SaveSystem saveSystem;
        
        // Singleton
        public static GameManager Instance { get; private set; }
        
        // Events
        public static event Action OnGameInitialized;
        public static event Action OnGamePaused;
        public static event Action OnGameResumed;
        
        // State
        private bool isGameInitialized = false;
        private bool isGamePaused = false;
        private Coroutine autoSaveCoroutine;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (Instance == this)
            {
                StartGame();
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }
        
        private void OnDestroy()
        {
            if (Instance == this)
            {
                SaveGame();
            }
        }
        
        #endregion
        
        #region Game Lifecycle
        
        private void InitializeGame()
        {
            Debug.Log("🚀 Initializing Space Clicker...");
            
            // Vérifier les références
            ValidateReferences();
            
            // Initialiser les systèmes
            InitializeSystems();
            
            // Charger la sauvegarde
            LoadGame();
            
            // Calculer la progression hors ligne
            CalculateOfflineProgress();
            
            isGameInitialized = true;
            OnGameInitialized?.Invoke();
            
            Debug.Log("✅ Game initialized successfully!");
        }
        
        private void StartGame()
        {
            if (!isGameInitialized) return;
            
            Debug.Log("🎮 Starting game...");
            
            // Démarrer la sauvegarde automatique
            StartAutoSave();
            
            // Démarrer la production automatique
            resourceManager.StartIdleProduction();
            
            // Mettre à jour l'UI
            uiManager.RefreshAllUI();
        }
        
        private void PauseGame()
        {
            if (isGamePaused) return;
            
            isGamePaused = true;
            Time.timeScale = 0f;
            
            // Sauvegarder avant la pause
            SaveGame();
            
            OnGamePaused?.Invoke();
            Debug.Log("⏸️ Game paused");
        }
        
        private void ResumeGame()
        {
            if (!isGamePaused) return;
            
            isGamePaused = false;
            Time.timeScale = 1f;
            
            // Calculer la progression hors ligne
            CalculateOfflineProgress();
            
            OnGameResumed?.Invoke();
            Debug.Log("▶️ Game resumed");
        }
        
        #endregion
        
        #region System Management
        
        private void ValidateReferences()
        {
            if (resourceManager == null)
                resourceManager = FindObjectOfType<ResourceManager>();
            
            if (upgradeManager == null)
                upgradeManager = FindObjectOfType<UpgradeManager>();
            
            if (uiManager == null)
                uiManager = FindObjectOfType<UIManager>();
            
            if (saveSystem == null)
                saveSystem = FindObjectOfType<SaveSystem>();
            
            // Vérifications critiques
            if (resourceManager == null)
                Debug.LogError("❌ ResourceManager not found!");
            
            if (saveSystem == null)
                Debug.LogError("❌ SaveSystem not found!");
        }
        
        private void InitializeSystems()
        {
            // Initialiser dans l'ordre de dépendance
            resourceManager?.Initialize();
            upgradeManager?.Initialize();
            uiManager?.Initialize();
            saveSystem?.Initialize();
        }
        
        #endregion
        
        #region Save/Load System
        
        public void SaveGame()
        {
            if (saveSystem != null)
            {
                saveSystem.SaveGame();
                Debug.Log("💾 Game saved");
            }
        }
        
        public void LoadGame()
        {
            if (saveSystem != null)
            {
                saveSystem.LoadGame();
                Debug.Log("📁 Game loaded");
            }
        }
        
        private void StartAutoSave()
        {
            if (autoSaveCoroutine != null)
                StopCoroutine(autoSaveCoroutine);
            
            autoSaveCoroutine = StartCoroutine(AutoSaveCoroutine());
        }
        
        private IEnumerator AutoSaveCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(autoSaveInterval);
                
                if (!isGamePaused)
                {
                    SaveGame();
                }
            }
        }
        
        #endregion
        
        #region Offline Progress
        
        private void CalculateOfflineProgress()
        {
            if (saveSystem == null || resourceManager == null) return;
            
            DateTime lastSaveTime = saveSystem.GetLastSaveTime();
            DateTime currentTime = DateTime.Now;
            
            if (lastSaveTime == default(DateTime)) return;
            
            TimeSpan offlineTime = currentTime - lastSaveTime;
            float offlineSeconds = (float)offlineTime.TotalSeconds;
            
            // Limiter la progression hors ligne à 1 heure
            float maxOfflineSeconds = 3600f; // 1 heure
            offlineSeconds = Mathf.Min(offlineSeconds, maxOfflineSeconds);
            
            if (offlineSeconds > 60f) // Plus d'1 minute hors ligne
            {
                var offlineRewards = resourceManager.CalculateOfflineProduction(offlineSeconds);
                
                if (offlineRewards.HasRewards())
                {
                    // Afficher popup de récompenses hors ligne
                    uiManager.ShowOfflineRewardsPopup(offlineRewards, offlineSeconds);
                }
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public bool IsGameInitialized() => isGameInitialized;
        public bool IsGamePaused() => isGamePaused;
        
        public void RestartGame()
        {
            // Reset complet du jeu
            saveSystem.DeleteSave();
            
            // Recharger la scène
            UnityEngine.SceneManagement.SceneManager.LoadScene(0);
        }
        
        public void QuitGame()
        {
            SaveGame();
            
            #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
            #else
            Application.Quit();
            #endif
        }
        
        #endregion
        
        #region Debug
        
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void DebugAddResources()
        {
            if (enableDebugMode && resourceManager != null)
            {
                resourceManager.AddResource(ResourceType.Energy, 1000);
                resourceManager.AddResource(ResourceType.Minerals, 500);
                resourceManager.AddResource(ResourceType.ResearchData, 100);
                Debug.Log("🔧 Debug resources added");
            }
        }
        
        #endregion
    }
}
