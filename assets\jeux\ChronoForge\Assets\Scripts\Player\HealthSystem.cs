using UnityEngine;
using System.Collections;

namespace ChronoForge.Player
{
    /// <summary>
    /// Système de vie et bouclier pour ChronoForge
    /// </summary>
    public class HealthSystem : MonoBehaviour
    {
        [Header("Health Settings")]
        public float maxHealth = 100f;
        public float currentHealth = 100f;
        public bool canRegenerate = true;
        public float healthRegenRate = 2f; // HP per second
        public float healthRegenDelay = 5f; // Delay after taking damage
        
        [Header("Shield Settings")]
        public float maxShield = 50f;
        public float currentShield = 0f;
        public bool hasShield = true;
        public float shieldRegenRate = 5f; // Shield per second
        public float shieldRegenDelay = 3f; // Delay after taking damage
        public float shieldBreakStunDuration = 0.5f;
        
        [Header("Damage Settings")]
        public bool isInvulnerable = false;
        public float invulnerabilityDuration = 1f;
        public float damageReduction = 0f; // 0-1 range
        
        [Header("Visual Feedback")]
        public bool flashOnDamage = true;
        public Color damageFlashColor = Color.red;
        public float flashDuration = 0.1f;
        
        // State tracking
        private float lastDamageTime = 0f;
        private bool isRegenerating = false;
        private bool isShieldRegenerating = false;
        private bool isInvulnerabilityActive = false;
        private Coroutine invulnerabilityCoroutine;
        private Coroutine flashCoroutine;
        
        // Components
        private SpriteRenderer spriteRenderer;
        private Color originalColor;
        
        // Events
        public static System.Action<float, float> OnHealthChanged; // current, max
        public static System.Action<float, float> OnShieldChanged; // current, max
        public static System.Action OnDeath;
        public static System.Action OnShieldBreak;
        public static System.Action OnHealthCritical; // Below 25%
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeHealthSystem();
        }
        
        private void Start()
        {
            SetupComponents();
            InitializeValues();
        }
        
        private void Update()
        {
            HandleRegeneration();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeHealthSystem()
        {
            // Ensure health is not above max
            currentHealth = Mathf.Min(currentHealth, maxHealth);
            currentShield = Mathf.Min(currentShield, maxShield);
            
            Debug.Log("❤️ HealthSystem initialized");
        }
        
        private void SetupComponents()
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                originalColor = spriteRenderer.color;
            }
        }
        
        private void InitializeValues()
        {
            // Notify UI of initial values
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            OnShieldChanged?.Invoke(currentShield, maxShield);
        }
        
        #endregion
        
        #region Damage System
        
        public void TakeDamage(float damage, bool ignoreShield = false, bool ignoreInvulnerability = false)
        {
            if (damage <= 0) return;
            
            // Check invulnerability
            if (!ignoreInvulnerability && (isInvulnerable || isInvulnerabilityActive))
            {
                Debug.Log("💫 Damage blocked by invulnerability");
                return;
            }
            
            // Apply damage reduction
            float finalDamage = damage * (1f - damageReduction);
            
            // Update last damage time
            lastDamageTime = Time.time;
            
            // Handle shield damage first
            if (!ignoreShield && hasShield && currentShield > 0)
            {
                float shieldDamage = Mathf.Min(finalDamage, currentShield);
                currentShield -= shieldDamage;
                finalDamage -= shieldDamage;
                
                OnShieldChanged?.Invoke(currentShield, maxShield);
                
                // Check for shield break
                if (currentShield <= 0)
                {
                    OnShieldBreak?.Invoke();
                    StartCoroutine(ShieldBreakStun());
                }
            }
            
            // Apply remaining damage to health
            if (finalDamage > 0)
            {
                currentHealth -= finalDamage;
                currentHealth = Mathf.Max(0, currentHealth);
                
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
                
                // Check for critical health
                if (currentHealth <= maxHealth * 0.25f && currentHealth > 0)
                {
                    OnHealthCritical?.Invoke();
                }
                
                // Check for death
                if (currentHealth <= 0)
                {
                    Die();
                    return;
                }
            }
            
            // Visual feedback
            if (flashOnDamage)
            {
                StartDamageFlash();
            }
            
            // Start invulnerability frames
            if (invulnerabilityDuration > 0)
            {
                StartInvulnerability();
            }
            
            Debug.Log($"💔 Took {damage:F1} damage. Health: {currentHealth:F1}/{maxHealth:F1}, Shield: {currentShield:F1}/{maxShield:F1}");
        }
        
        private IEnumerator ShieldBreakStun()
        {
            // Disable player movement/actions briefly
            PlayerController player = GetComponent<PlayerController>();
            if (player != null)
            {
                player.SetCanMove(false);
            }
            
            yield return new WaitForSeconds(shieldBreakStunDuration);
            
            if (player != null)
            {
                player.SetCanMove(true);
            }
        }
        
        #endregion
        
        #region Healing System
        
        public void Heal(float amount)
        {
            if (amount <= 0) return;
            
            float oldHealth = currentHealth;
            currentHealth = Mathf.Min(currentHealth + amount, maxHealth);
            
            if (currentHealth != oldHealth)
            {
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
                Debug.log($"💚 Healed {amount:F1}. Health: {currentHealth:F1}/{maxHealth:F1}");
            }
        }
        
        public void HealToFull()
        {
            Heal(maxHealth);
        }
        
        public void RestoreShield(float amount)
        {
            if (amount <= 0 || !hasShield) return;
            
            float oldShield = currentShield;
            currentShield = Mathf.Min(currentShield + amount, maxShield);
            
            if (currentShield != oldShield)
            {
                OnShieldChanged?.Invoke(currentShield, maxShield);
                Debug.Log($"🛡️ Shield restored {amount:F1}. Shield: {currentShield:F1}/{maxShield:F1}");
            }
        }
        
        public void RestoreShieldToFull()
        {
            RestoreShield(maxShield);
        }
        
        #endregion
        
        #region Regeneration
        
        private void HandleRegeneration()
        {
            float timeSinceLastDamage = Time.time - lastDamageTime;
            
            // Health regeneration
            if (canRegenerate && currentHealth < maxHealth && timeSinceLastDamage >= healthRegenDelay)
            {
                if (!isRegenerating)
                {
                    isRegenerating = true;
                    Debug.Log("💚 Health regeneration started");
                }
                
                float regenAmount = healthRegenRate * Time.deltaTime;
                Heal(regenAmount);
            }
            else if (isRegenerating && (currentHealth >= maxHealth || timeSinceLastDamage < healthRegenDelay))
            {
                isRegenerating = false;
            }
            
            // Shield regeneration
            if (hasShield && currentShield < maxShield && timeSinceLastDamage >= shieldRegenDelay)
            {
                if (!isShieldRegenerating)
                {
                    isShieldRegenerating = true;
                    Debug.Log("🛡️ Shield regeneration started");
                }
                
                float regenAmount = shieldRegenRate * Time.deltaTime;
                RestoreShield(regenAmount);
            }
            else if (isShieldRegenerating && (currentShield >= maxShield || timeSinceLastDamage < shieldRegenDelay))
            {
                isShieldRegenerating = false;
            }
        }
        
        #endregion
        
        #region Invulnerability
        
        private void StartInvulnerability()
        {
            if (invulnerabilityCoroutine != null)
            {
                StopCoroutine(invulnerabilityCoroutine);
            }
            
            invulnerabilityCoroutine = StartCoroutine(InvulnerabilityCoroutine());
        }
        
        private IEnumerator InvulnerabilityCoroutine()
        {
            isInvulnerabilityActive = true;
            yield return new WaitForSeconds(invulnerabilityDuration);
            isInvulnerabilityActive = false;
        }
        
        #endregion
        
        #region Visual Effects
        
        private void StartDamageFlash()
        {
            if (flashCoroutine != null)
            {
                StopCoroutine(flashCoroutine);
            }
            
            flashCoroutine = StartCoroutine(DamageFlashCoroutine());
        }
        
        private IEnumerator DamageFlashCoroutine()
        {
            if (spriteRenderer == null) yield break;
            
            spriteRenderer.color = damageFlashColor;
            yield return new WaitForSeconds(flashDuration);
            spriteRenderer.color = originalColor;
        }
        
        #endregion
        
        #region Death
        
        private void Die()
        {
            Debug.Log("💀 Player died");
            OnDeath?.Invoke();
            
            // Disable player controls
            PlayerController player = GetComponent<PlayerController>();
            if (player != null)
            {
                player.SetCanMove(false);
                player.SetCanAttack(false);
            }
        }
        
        public void Revive(float healthPercentage = 1f)
        {
            currentHealth = maxHealth * Mathf.Clamp01(healthPercentage);
            currentShield = maxShield; // Full shield on revive
            
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            OnShieldChanged?.Invoke(currentShield, maxShield);
            
            // Re-enable player controls
            PlayerController player = GetComponent<PlayerController>();
            if (player != null)
            {
                player.SetCanMove(true);
                player.SetCanAttack(true);
            }
            
            Debug.Log("✨ Player revived");
        }
        
        #endregion
        
        #region Stat Modifications
        
        public void SetMaxHealth(float newMaxHealth)
        {
            float healthRatio = currentHealth / maxHealth;
            maxHealth = newMaxHealth;
            currentHealth = maxHealth * healthRatio;
            
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        public void SetMaxShield(float newMaxShield)
        {
            float shieldRatio = maxShield > 0 ? currentShield / maxShield : 0f;
            maxShield = newMaxShield;
            currentShield = maxShield * shieldRatio;
            
            OnShieldChanged?.Invoke(currentShield, maxShield);
        }
        
        public void SetDamageReduction(float reduction)
        {
            damageReduction = Mathf.Clamp01(reduction);
        }
        
        #endregion
        
        #region Public Getters
        
        public float GetHealthPercentage()
        {
            return maxHealth > 0 ? currentHealth / maxHealth : 0f;
        }
        
        public float GetShieldPercentage()
        {
            return maxShield > 0 ? currentShield / maxShield : 0f;
        }
        
        public bool IsAlive()
        {
            return currentHealth > 0;
        }
        
        public bool IsCriticalHealth()
        {
            return currentHealth <= maxHealth * 0.25f;
        }
        
        public bool HasShieldActive()
        {
            return hasShield && currentShield > 0;
        }
        
        public bool IsRegenerating()
        {
            return isRegenerating || isShieldRegenerating;
        }
        
        #endregion
    }
}
