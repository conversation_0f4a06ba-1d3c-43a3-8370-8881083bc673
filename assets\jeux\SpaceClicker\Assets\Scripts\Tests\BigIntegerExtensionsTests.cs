using NUnit.Framework;
using System.Numerics;
using SpaceClicker.Utils;

namespace SpaceClicker.Tests
{
    /// <summary>
    /// Tests unitaires pour les extensions BigInteger
    /// </summary>
    public class BigIntegerExtensionsTests
    {
        [Test]
        public void ToCompactString_SmallNumbers_ShouldReturnExactValue()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("0", BigInteger.Zero.ToCompactString());
            Assert.AreEqual("1", new BigInteger(1).ToCompactString());
            Assert.AreEqual("42", new BigInteger(42).ToCompactString());
            Assert.AreEqual("999", new BigInteger(999).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_Thousands_ShouldUseKSuffix()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("1.0K", new BigInteger(1000).ToCompactString());
            Assert.AreEqual("1.5K", new BigInteger(1500).ToCompactString());
            Assert.AreEqual("42.3K", new BigInteger(42300).ToCompactString());
            Assert.AreEqual("999.9K", new BigInteger(999900).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_Millions_ShouldUseMSuffix()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("1.0M", new BigInteger(1000000).ToCompactString());
            Assert.AreEqual("2.5M", new BigInteger(2500000).ToCompactString());
            Assert.AreEqual("123.4M", new BigInteger(123400000).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_Billions_ShouldUseBSuffix()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("1.0B", new BigInteger(1000000000).ToCompactString());
            Assert.AreEqual("5.7B", new BigInteger(5700000000).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_Trillions_ShouldUseTSuffix()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("1.0T", new BigInteger(1000000000000).ToCompactString());
            Assert.AreEqual("3.2T", new BigInteger(3200000000000).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_VeryLargeNumbers_ShouldUseScientificNotation()
        {
            // Arrange
            BigInteger veryLarge = BigInteger.Parse("123456789012345678901234567890");
            
            // Act
            string result = veryLarge.ToCompactString();
            
            // Assert
            Assert.IsTrue(result.Contains("e"), "Very large numbers should use scientific notation");
            Assert.IsTrue(result.Length < 15, "Compact string should be reasonably short");
        }
        
        [Test]
        public void ToDisplayString_SmallNumbers_ShouldReturnFormattedValue()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("0", BigInteger.Zero.ToDisplayString());
            Assert.AreEqual("1", new BigInteger(1).ToDisplayString());
            Assert.AreEqual("1,234", new BigInteger(1234).ToDisplayString());
            Assert.AreEqual("999,999", new BigInteger(999999).ToDisplayString());
        }
        
        [Test]
        public void ToDisplayString_LargeNumbers_ShouldUseCompactFormat()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("1.0K", new BigInteger(1000).ToDisplayString());
            Assert.AreEqual("1.0M", new BigInteger(1000000).ToDisplayString());
            Assert.AreEqual("1.0B", new BigInteger(1000000000).ToDisplayString());
        }
        
        [Test]
        public void ToBigInteger_Float_ShouldConvertCorrectly()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(new BigInteger(0), 0f.ToBigInteger());
            Assert.AreEqual(new BigInteger(42), 42.0f.ToBigInteger());
            Assert.AreEqual(new BigInteger(123), 123.7f.ToBigInteger()); // Should truncate
            Assert.AreEqual(new BigInteger(-50), (-50.3f).ToBigInteger());
        }
        
        [Test]
        public void ToBigInteger_Double_ShouldConvertCorrectly()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(new BigInteger(0), 0.0.ToBigInteger());
            Assert.AreEqual(new BigInteger(1000), 1000.0.ToBigInteger());
            Assert.AreEqual(new BigInteger(999), 999.9.ToBigInteger()); // Should truncate
        }
        
        [Test]
        public void ToBigInteger_VeryLargeDouble_ShouldHandleCorrectly()
        {
            // Arrange
            double largeDouble = 1e15; // 1 quadrillion
            
            // Act
            BigInteger result = largeDouble.ToBigInteger();
            
            // Assert
            Assert.IsTrue(result > BigInteger.Zero, "Should convert large double to positive BigInteger");
            Assert.AreEqual(new BigInteger(1000000000000000), result, "Should convert 1e15 correctly");
        }
        
        [Test]
        public void ToCompactString_EdgeCases_ShouldHandleCorrectly()
        {
            // Test boundary values
            Assert.AreEqual("999", new BigInteger(999).ToCompactString());
            Assert.AreEqual("1.0K", new BigInteger(1000).ToCompactString());
            Assert.AreEqual("999.9K", new BigInteger(999999).ToCompactString());
            Assert.AreEqual("1.0M", new BigInteger(1000000).ToCompactString());
        }
        
        [Test]
        public void ToCompactString_NegativeNumbers_ShouldHandleCorrectly()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("-42", new BigInteger(-42).ToCompactString());
            Assert.AreEqual("-1.5K", new BigInteger(-1500).ToCompactString());
            Assert.AreEqual("-2.3M", new BigInteger(-2300000).ToCompactString());
        }
        
        [Test]
        public void ToDisplayString_FormattingConsistency_ShouldBeConsistent()
        {
            // Test that the same number always produces the same string
            BigInteger testNumber = new BigInteger(123456789);
            
            string result1 = testNumber.ToDisplayString();
            string result2 = testNumber.ToDisplayString();
            string result3 = testNumber.ToDisplayString();
            
            Assert.AreEqual(result1, result2, "Display string should be consistent");
            Assert.AreEqual(result2, result3, "Display string should be consistent");
        }
        
        [Test]
        public void ToCompactString_Performance_ShouldBeReasonablyFast()
        {
            // Arrange
            BigInteger[] testNumbers = {
                new BigInteger(123),
                new BigInteger(123456),
                new BigInteger(123456789),
                BigInteger.Parse("123456789012345678901234567890")
            };
            
            // Act & Assert - This test mainly ensures no exceptions are thrown
            // and the method completes in reasonable time
            foreach (var number in testNumbers)
            {
                string result = number.ToCompactString();
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsTrue(result.Length > 0, "Result should not be empty");
                Assert.IsTrue(result.Length < 20, "Result should be reasonably compact");
            }
        }
        
        [Test]
        public void ToBigInteger_EdgeCases_ShouldHandleCorrectly()
        {
            // Test edge cases for float/double conversion
            Assert.AreEqual(BigInteger.Zero, float.NaN.ToBigInteger());
            Assert.AreEqual(BigInteger.Zero, float.PositiveInfinity.ToBigInteger());
            Assert.AreEqual(BigInteger.Zero, float.NegativeInfinity.ToBigInteger());
            
            Assert.AreEqual(BigInteger.Zero, double.NaN.ToBigInteger());
            Assert.AreEqual(BigInteger.Zero, double.PositiveInfinity.ToBigInteger());
            Assert.AreEqual(BigInteger.Zero, double.NegativeInfinity.ToBigInteger());
        }
        
        [Test]
        public void ToCompactString_Precision_ShouldMaintainReadability()
        {
            // Test that precision is appropriate for readability
            BigInteger test1 = new BigInteger(1234567);
            BigInteger test2 = new BigInteger(1999999);
            
            string result1 = test1.ToCompactString();
            string result2 = test2.ToCompactString();
            
            // Results should be readable and not overly precise
            Assert.IsTrue(result1.Length <= 6, "Compact string should not be too long");
            Assert.IsTrue(result2.Length <= 6, "Compact string should not be too long");
            
            // Should contain appropriate decimal places
            if (result1.Contains("."))
            {
                string[] parts = result1.Split('.');
                Assert.IsTrue(parts[1].Length <= 2, "Should not have too many decimal places");
            }
        }
    }
}
