using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using SpaceClicker.Core;
using SpaceClicker.Effects;
using SpaceClicker.Utils;
using BigInteger = System.Numerics.BigInteger;

namespace SpaceClicker.UI
{
    /// <summary>
    /// Composant pour les modules cliquables qui génèrent des ressources
    /// </summary>
    public class ClickableModule : MonoBehaviour, IPointerClickHandler, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("Module Settings")]
        public ResourceType resourceType;
        public string moduleName = "Module";
        public string moduleDescription = "Click to generate resources";
        
        [Header("Visual Components")]
        public Image moduleImage;
        public ParticleSystem clickParticles;
        public TextMeshProUGUI productionText;
        public GameObject glowEffect;
        
        [Header("Animation Settings")]
        public float clickScaleMultiplier = 1.2f;
        public float clickAnimationDuration = 0.1f;
        public float hoverScaleMultiplier = 1.05f;
        public float idleAnimationSpeed = 1f;
        
        [Header("Audio")]
        public AudioSource audioSource;
        public AudioClip clickSound;
        public AudioClip hoverSound;
        
        // Private fields
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        private UnityEngine.Vector3 originalScale;
        private Color originalColor;
        private bool isHovered = false;
        private bool isAnimating = false;
        
        // Production tracking
        private float lastProductionUpdate = 0f;
        private BigInteger baseProduction = 1;
        private float productionMultiplier = 1f;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            // Store original values
            originalScale = transform.localScale;
            
            if (moduleImage != null)
                originalColor = moduleImage.color;
        }
        
        private void Start()
        {
            Initialize();
        }
        
        private void Update()
        {
            UpdateIdleAnimation();
            UpdateProductionDisplay();
        }
        
        private void OnDestroy()
        {
            // Cleanup event listeners
            if (resourceManager != null)
            {
                ResourceManager.OnResourceChanged -= OnResourceChanged;
            }
            
            if (upgradeManager != null)
            {
                UpgradeManager.OnUpgradePurchased -= OnUpgradePurchased;
            }
        }
        
        #endregion
        
        #region Initialization
        
        private void Initialize()
        {
            // Get manager references
            resourceManager = FindObjectOfType<ResourceManager>();
            upgradeManager = FindObjectOfType<UpgradeManager>();
            
            if (resourceManager == null)
            {
                Debug.LogError($"ResourceManager not found for {gameObject.name}");
                return;
            }
            
            // Subscribe to events
            ResourceManager.OnResourceChanged += OnResourceChanged;
            
            if (upgradeManager != null)
            {
                UpgradeManager.OnUpgradePurchased += OnUpgradePurchased;
            }
            
            // Setup initial state
            UpdateProductionValues();
            SetupVisualEffects();
        }
        
        private void SetupVisualEffects()
        {
            // Configure particle system
            if (clickParticles != null)
            {
                var main = clickParticles.main;
                main.startColor = GetResourceColor();
                main.maxParticles = 20;
                
                var emission = clickParticles.emission;
                emission.enabled = false; // Only emit on click
            }
            
            // Setup glow effect
            if (glowEffect != null)
            {
                glowEffect.SetActive(false);
            }
            
            // Set module color based on resource type
            if (moduleImage != null)
            {
                moduleImage.color = GetResourceColor();
            }
        }
        
        #endregion
        
        #region Click Handling
        
        public void OnPointerClick(PointerEventData eventData)
        {
            if (resourceManager == null || isAnimating) return;
            
            // Generate resources
            BigInteger production = GetCurrentProduction();
            resourceManager.AddResource(resourceType, production);
            
            // Play effects
            PlayClickEffects(eventData.position);
            
            // Play animation
            PlayClickAnimation();
            
            // Play sound
            PlaySound(clickSound);
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            isHovered = true;
            PlayHoverAnimation();
            PlaySound(hoverSound);
            
            // Show glow effect
            if (glowEffect != null)
            {
                glowEffect.SetActive(true);
            }
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            isHovered = false;
            PlayUnhoverAnimation();
            
            // Hide glow effect
            if (glowEffect != null)
            {
                glowEffect.SetActive(false);
            }
        }
        
        #endregion
        
        #region Production Calculation
        
        private BigInteger GetCurrentProduction()
        {
            BigInteger production = baseProduction;
            
            // Apply upgrade multipliers
            if (upgradeManager != null)
            {
                float multiplier = upgradeManager.GetProductionMultiplier(resourceType);
                production = new BigInteger((double)production * multiplier);
            }
            
            return production;
        }
        
        private void UpdateProductionValues()
        {
            // Update base production based on upgrades
            if (upgradeManager != null)
            {
                productionMultiplier = upgradeManager.GetProductionMultiplier(resourceType);
            }
        }
        
        #endregion
        
        #region Visual Updates
        
        private void UpdateProductionDisplay()
        {
            if (productionText != null && Time.time - lastProductionUpdate > 1f)
            {
                BigInteger currentProduction = GetCurrentProduction();
                productionText.text = $"+{currentProduction.ToCompactString()}/click";
                lastProductionUpdate = Time.time;
            }
        }
        
        private void UpdateIdleAnimation()
        {
            if (!isHovered && !isAnimating)
            {
                // Gentle breathing animation
                float scale = 1f + Mathf.Sin(Time.time * idleAnimationSpeed) * 0.02f;
                transform.localScale = originalScale * scale;
            }
        }
        
        #endregion
        
        #region Animations
        
        private void PlayClickAnimation()
        {
            if (isAnimating) return;
            
            isAnimating = true;
            
            // Scale up then down
            LeanTween.scale(gameObject, originalScale * clickScaleMultiplier, clickAnimationDuration * 0.5f)
                .setEase(LeanTweenType.easeOutQuad)
                .setOnComplete(() => {
                    LeanTween.scale(gameObject, originalScale, clickAnimationDuration * 0.5f)
                        .setEase(LeanTweenType.easeOutQuad)
                        .setOnComplete(() => {
                            isAnimating = false;
                        });
                });
            
            // Color flash
            if (moduleImage != null)
            {
                Color flashColor = Color.white;
                LeanTween.value(gameObject, 0f, 1f, clickAnimationDuration)
                    .setOnUpdate((float value) => {
                        Color currentColor = Color.Lerp(flashColor, originalColor, value);
                        moduleImage.color = currentColor;
                    });
            }
        }
        
        private void PlayHoverAnimation()
        {
            if (isAnimating) return;
            
            LeanTween.scale(gameObject, originalScale * hoverScaleMultiplier, 0.1f)
                .setEase(LeanTweenType.easeOutQuad);
        }
        
        private void PlayUnhoverAnimation()
        {
            if (isAnimating) return;
            
            LeanTween.scale(gameObject, originalScale, 0.1f)
                .setEase(LeanTweenType.easeOutQuad);
        }
        
        #endregion
        
        #region Effects
        
        private void PlayClickEffects(UnityEngine.Vector3 clickPosition)
        {
            // Play particle effect
            if (clickParticles != null)
            {
                clickParticles.transform.position = Camera.main.ScreenToWorldPoint(new UnityEngine.Vector3(clickPosition.x, clickPosition.y, 10f));
                clickParticles.Emit(10);
            }
            
            // Use global effect manager if available
            if (ClickEffectManager.Instance != null)
            {
                ClickEffectManager.Instance.PlayClickEffect(clickPosition, resourceType);
            }
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnResourceChanged(ResourceType type, BigInteger amount)
        {
            // React to resource changes if needed
            if (type == resourceType)
            {
                // Could add visual feedback here
            }
        }
        
        private void OnUpgradePurchased(UpgradeData upgrade)
        {
            // Update production values when upgrades are purchased
            if (IsUpgradeRelevant(upgrade))
            {
                UpdateProductionValues();
                PlayUpgradeEffect();
            }
        }
        
        private bool IsUpgradeRelevant(UpgradeData upgrade)
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return upgrade.type == UpgradeType.SolarPanelEfficiency;
                case ResourceType.Minerals:
                    return upgrade.type == UpgradeType.MiningDrillSpeed;
                case ResourceType.ResearchData:
                    return upgrade.type == UpgradeType.ResearchLabCapacity;
                default:
                    return false;
            }
        }
        
        private void PlayUpgradeEffect()
        {
            // Visual effect when module is upgraded
            if (moduleImage != null)
            {
                Color upgradeColor = Color.green;
                LeanTween.value(gameObject, 0f, 1f, 0.5f)
                    .setOnUpdate((float value) => {
                        Color currentColor = Color.Lerp(upgradeColor, originalColor, value);
                        moduleImage.color = currentColor;
                    });
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        private Color GetResourceColor()
        {
            switch (resourceType)
            {
                case ResourceType.Energy:
                    return ColorUtility.TryParseHtmlString(GameConstants.ENERGY_COLOR, out Color energyColor) ? energyColor : Color.yellow;
                case ResourceType.Minerals:
                    return ColorUtility.TryParseHtmlString(GameConstants.MINERALS_COLOR, out Color mineralsColor) ? mineralsColor : Color.cyan;
                case ResourceType.ResearchData:
                    return ColorUtility.TryParseHtmlString(GameConstants.RESEARCH_COLOR, out Color researchColor) ? researchColor : Color.green;
                case ResourceType.SpaceCurrency:
                    return ColorUtility.TryParseHtmlString(GameConstants.CURRENCY_COLOR, out Color currencyColor) ? currencyColor : Color.magenta;
                default:
                    return Color.white;
            }
        }
        
        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Set the resource type for this module
        /// </summary>
        public void SetResourceType(ResourceType type)
        {
            resourceType = type;
            UpdateProductionValues();
            SetupVisualEffects();
        }
        
        /// <summary>
        /// Get the current production per click
        /// </summary>
        public BigInteger GetProductionPerClick()
        {
            return GetCurrentProduction();
        }
        
        /// <summary>
        /// Manually trigger a click (for testing or automation)
        /// </summary>
        public void SimulateClick()
        {
            OnPointerClick(new PointerEventData(EventSystem.current));
        }
        
        #endregion
    }
}
