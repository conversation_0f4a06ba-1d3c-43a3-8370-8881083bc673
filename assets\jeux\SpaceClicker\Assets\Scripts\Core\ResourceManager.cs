using UnityEngine;
using System.Collections.Generic;
using System.Numerics;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Types de ressources dans SpaceClicker
    /// </summary>
    public enum ResourceType
    {
        Energy,
        Matter,
        Credits,
        Research,
        Antimatter
    }
    
    /// <summary>
    /// Gestionnaire de ressources pour SpaceClicker
    /// </summary>
    public class ResourceManager : MonoBehaviour
    {
        [Header("Starting Resources")]
        public float startingEnergy = 0f;
        public float startingMatter = 0f;
        public float startingCredits = 100f;
        public float startingResearch = 0f;
        public float startingAntimatter = 0f;
        
        [Header("Generation Rates")]
        public float baseEnergyPerSecond = 1f;
        public float baseMatterPerSecond = 0.1f;
        public float baseCreditsPerSecond = 0.5f;
        
        // Resource storage
        private Dictionary<ResourceType, BigInteger> resources = new Dictionary<ResourceType, BigInteger>();
        private Dictionary<ResourceType, float> generationRates = new Dictionary<ResourceType, float>();
        
        // Events
        public static System.Action<ResourceType, BigInteger> OnResourceChanged;
        public static System.Action<ResourceType, float> OnGenerationRateChanged;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeResources();
        }
        
        private void Start()
        {
            LoadData();
        }
        
        private void Update()
        {
            GenerateResources();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeResources()
        {
            // Initialize resource amounts
            resources[ResourceType.Energy] = new BigInteger(startingEnergy);
            resources[ResourceType.Matter] = new BigInteger(startingMatter);
            resources[ResourceType.Credits] = new BigInteger(startingCredits);
            resources[ResourceType.Research] = new BigInteger(startingResearch);
            resources[ResourceType.Antimatter] = new BigInteger(startingAntimatter);
            
            // Initialize generation rates
            generationRates[ResourceType.Energy] = baseEnergyPerSecond;
            generationRates[ResourceType.Matter] = baseMatterPerSecond;
            generationRates[ResourceType.Credits] = baseCreditsPerSecond;
            generationRates[ResourceType.Research] = 0f;
            generationRates[ResourceType.Antimatter] = 0f;
            
            Debug.Log("💎 ResourceManager initialized");
        }
        
        #endregion
        
        #region Resource Management
        
        public void AddResource(ResourceType type, BigInteger amount)
        {
            if (resources.ContainsKey(type))
            {
                resources[type] += amount;
                OnResourceChanged?.Invoke(type, resources[type]);
            }
        }
        
        public void AddResource(ResourceType type, float amount)
        {
            AddResource(type, new BigInteger(amount));
        }
        
        public bool SpendResource(ResourceType type, BigInteger amount)
        {
            if (HasResource(type, amount))
            {
                resources[type] -= amount;
                OnResourceChanged?.Invoke(type, resources[type]);
                return true;
            }
            return false;
        }
        
        public bool SpendResource(ResourceType type, float amount)
        {
            return SpendResource(type, new BigInteger(amount));
        }
        
        public bool HasResource(ResourceType type, BigInteger amount)
        {
            return resources.ContainsKey(type) && resources[type] >= amount;
        }
        
        public bool HasResource(ResourceType type, float amount)
        {
            return HasResource(type, new BigInteger(amount));
        }
        
        public BigInteger GetResource(ResourceType type)
        {
            return resources.ContainsKey(type) ? resources[type] : BigInteger.Zero;
        }
        
        public float GetResourceFloat(ResourceType type)
        {
            return (float)GetResource(type);
        }
        
        #endregion
        
        #region Generation
        
        private void GenerateResources()
        {
            float deltaTime = Time.deltaTime;
            
            foreach (var rate in generationRates)
            {
                if (rate.Value > 0)
                {
                    float generated = rate.Value * deltaTime;
                    AddResource(rate.Key, generated);
                }
            }
        }
        
        public void SetGenerationRate(ResourceType type, float rate)
        {
            generationRates[type] = rate;
            OnGenerationRateChanged?.Invoke(type, rate);
        }
        
        public void AddGenerationRate(ResourceType type, float additionalRate)
        {
            if (generationRates.ContainsKey(type))
            {
                generationRates[type] += additionalRate;
                OnGenerationRateChanged?.Invoke(type, generationRates[type]);
            }
        }
        
        public float GetGenerationRate(ResourceType type)
        {
            return generationRates.ContainsKey(type) ? generationRates[type] : 0f;
        }
        
        #endregion
        
        #region Click Generation
        
        public void OnClick(ResourceType type, float multiplier = 1f)
        {
            float baseAmount = GetClickValue(type);
            float finalAmount = baseAmount * multiplier;
            
            AddResource(type, finalAmount);
            
            Debug.Log($"🖱️ Clicked {type}: +{finalAmount}");
        }
        
        private float GetClickValue(ResourceType type)
        {
            switch (type)
            {
                case ResourceType.Energy:
                    return 1f;
                case ResourceType.Matter:
                    return 0.1f;
                case ResourceType.Credits:
                    return 0.5f;
                default:
                    return 1f;
            }
        }
        
        #endregion
        
        #region Save/Load
        
        public void SaveData()
        {
            foreach (var resource in resources)
            {
                string key = $"Resource_{resource.Key}";
                PlayerPrefs.SetString(key, resource.Value.ToString());
            }
            
            foreach (var rate in generationRates)
            {
                string key = $"Rate_{rate.Key}";
                PlayerPrefs.SetFloat(key, rate.Value);
            }
            
            PlayerPrefs.Save();
        }
        
        public void LoadData()
        {
            foreach (ResourceType type in System.Enum.GetValues(typeof(ResourceType)))
            {
                string resourceKey = $"Resource_{type}";
                if (PlayerPrefs.HasKey(resourceKey))
                {
                    string valueStr = PlayerPrefs.GetString(resourceKey);
                    if (BigInteger.TryParse(valueStr, out BigInteger value))
                    {
                        resources[type] = value;
                    }
                }
                
                string rateKey = $"Rate_{type}";
                if (PlayerPrefs.HasKey(rateKey))
                {
                    generationRates[type] = PlayerPrefs.GetFloat(rateKey);
                }
            }
            
            // Notify UI of loaded values
            foreach (var resource in resources)
            {
                OnResourceChanged?.Invoke(resource.Key, resource.Value);
            }
        }
        
        #endregion
        
        #region Public Getters
        
        public Dictionary<ResourceType, BigInteger> GetAllResources()
        {
            return new Dictionary<ResourceType, BigInteger>(resources);
        }
        
        public Dictionary<ResourceType, float> GetAllGenerationRates()
        {
            return new Dictionary<ResourceType, float>(generationRates);
        }

        public void ResetResources()
        {
            resources[ResourceType.Energy] = new BigInteger(startingEnergy);
            resources[ResourceType.Matter] = new BigInteger(startingMatter);
            resources[ResourceType.Credits] = new BigInteger(startingCredits);
            resources[ResourceType.Research] = new BigInteger(startingResearch);
            resources[ResourceType.Antimatter] = new BigInteger(startingAntimatter);

            // Notify UI
            foreach (var resource in resources)
            {
                OnResourceChanged?.Invoke(resource.Key, resource.Value);
            }
        }

        public void SetProductionRate(ResourceType type, float rate)
        {
            SetGenerationRate(type, rate);
        }

        #endregion
    }
}
