import os
import stripe
from flask import Flask, request, jsonify

app = Flask(__name__)

# Charge la clé Stripe secrète depuis les variables d'environnement
STRIPE_SECRET_KEY = os.environ.get("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.environ.get("STRIPE_WEBHOOK_SECRET")
if not STRIPE_SECRET_KEY:
    raise RuntimeError("STRIPE_SECRET_KEY n'est pas défini dans les variables d'environnement !")
if not STRIPE_WEBHOOK_SECRET:
    raise RuntimeError("STRIPE_WEBHOOK_SECRET n'est pas défini dans les variables d'environnement !")
stripe.api_key = STRIPE_SECRET_KEY

# Remplace par ton vrai domaine de frontend (ex: https://api2csv.monsite.com)
YOUR_DOMAIN = "https://ton-frontend.com"

@app.route('/api/create-checkout-session', methods=['POST'])
def create_checkout_session():
    try:
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'eur',
                    'product_data': {
                        'name': 'API2CSV Pro (abonnement mensuel)',
                    },
                    'unit_amount': 500,  # 5€ en centimes
                },
                'quantity': 1,
            }],
            mode='subscription',
            success_url=YOUR_DOMAIN + '/success.html',
            cancel_url=YOUR_DOMAIN + '/cancel.html',
        )
        return jsonify({'url': checkout_session.url})
    except Exception as e:
        return jsonify(error=str(e)), 400

# Webhook Stripe pour activer l'accès Pro après paiement
@app.route('/api/stripe-webhook', methods=['POST'])
def stripe_webhook():
    payload = request.data
    sig_header = request.headers.get('stripe-signature')
    event = None
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
    except Exception as e:
        return '', 400

    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        # Ici, active l'accès Pro pour l'utilisateur (ex: génère une clé API, envoie un email, etc.)
        # TODO: Ajouter ta logique d'activation ici
    return '', 200

if __name__ == '__main__':
    app.run(port=5002, debug=True)
