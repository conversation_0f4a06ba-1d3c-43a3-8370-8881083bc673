using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using ChronoForge.Core;
using ChronoForge.Player;
using ChronoForge.Combat;

namespace ChronoForge.Procedural
{
    /// <summary>
    /// Composant de salle individuelle pour ChronoForge
    /// </summary>
    public class Room : MonoBehaviour
    {
        [Header("Room Data")]
        public RoomData roomData;
        
        [Header("Spawn Points")]
        public Transform[] enemySpawnPoints;
        public Transform[] itemSpawnPoints;
        public Transform[] interactableSpawnPoints;
        public Transform playerSpawnPoint;
        
        [Header("Room Bounds")]
        public Collider2D roomBounds;
        public Transform[] doorPositions;
        
        [Header("Visual Elements")]
        public SpriteRenderer roomBackground;
        public GameObject[] decorationObjects;
        public ParticleSystem ambientParticles;
        
        [Header("Audio")]
        public AudioSource roomAudioSource;
        public AudioClip enterSound;
        public AudioClip clearSound;
        public AudioClip ambientSound;
        
        // Events
        public static System.Action<Room> OnRoomEntered;
        public static System.Action<Room> OnRoomCleared;
        public static System.Action<Room> OnRoomExited;
        
        // State
        private bool isPlayerInside = false;
        private bool isInitialized = false;
        private List<GameObject> spawnedEnemies = new List<GameObject>();
        private List<GameObject> spawnedItems = new List<GameObject>();
        private List<GameObject> spawnedInteractables = new List<GameObject>();
        
        // Components
        private LevelGenerator levelGenerator;
        private RunManager runManager;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeRoom();
        }
        
        private void Start()
        {
            FindManagers();
        }
        
        private void Update()
        {
            UpdateRoom();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeRoom()
        {
            // Setup room bounds if not assigned
            if (roomBounds == null)
            {
                roomBounds = GetComponent<Collider2D>();
                if (roomBounds == null)
                {
                    // Create default bounds
                    GameObject boundsObj = new GameObject("RoomBounds");
                    boundsObj.transform.SetParent(transform);
                    boundsObj.transform.localPosition = Vector3.zero;
                    
                    BoxCollider2D boxCollider = boundsObj.AddComponent<BoxCollider2D>();
                    boxCollider.size = Vector2.one * 18f; // Default room size
                    boxCollider.isTrigger = true;
                    
                    roomBounds = boxCollider;
                }
            }
            
            // Setup audio source
            if (roomAudioSource == null)
            {
                roomAudioSource = gameObject.AddComponent<AudioSource>();
                roomAudioSource.playOnAwake = false;
                roomAudioSource.loop = true;
            }
            
            UnityEngine.Debug.Log($"🏠 Room component initialized: {gameObject.name}");
        }
        
        private void FindManagers()
        {
            if (levelGenerator == null)
                levelGenerator = FindFirstObjectByType<LevelGenerator>();
            
            if (runManager == null)
                runManager = FindFirstObjectByType<RunManager>();
        }
        
        public void Initialize(RoomData data)
        {
            roomData = data;
            isInitialized = true;
            
            // Apply room data
            ApplyRoomData();
            
            // Generate room contents
            GenerateRoomContents();
            
            // Setup visual elements
            SetupVisualElements();
            
            // Setup audio
            SetupAudio();
            
            UnityEngine.Debug.Log($"🏠 Room initialized: {roomData.roomType} at {roomData.gridPosition}");
        }
        
        #endregion
        
        #region Room Data Application
        
        private void ApplyRoomData()
        {
            if (roomData == null) return;
            
            // Set room name
            gameObject.name = $"Room_{roomData.gridPosition.x}_{roomData.gridPosition.y}_{roomData.roomType}";
            
            // Apply position
            transform.position = roomData.worldPosition;
            
            // Apply modifiers
            ApplyRoomModifiers();
        }
        
        private void ApplyRoomModifiers()
        {
            foreach (var modifier in roomData.modifiers)
            {
                ApplyModifier(modifier);
            }
        }
        
        private void ApplyModifier(RoomModifier modifier)
        {
            switch (modifier.type)
            {
                case RoomModifierType.DifficultyIncrease:
                    // Increase enemy health/damage
                    break;
                case RoomModifierType.ExtraEnemies:
                    // Spawn additional enemies
                    break;
                case RoomModifierType.BetterLoot:
                    // Improve item quality
                    break;
                case RoomModifierType.HealthDrain:
                    // Start health drain coroutine
                    StartCoroutine(HealthDrainCoroutine(modifier.value));
                    break;
            }
        }
        
        #endregion
        
        #region Content Generation
        
        private void GenerateRoomContents()
        {
            if (roomData == null) return;
            
            // Generate enemies
            GenerateEnemies();
            
            // Generate items
            GenerateItems();
            
            // Generate interactables
            GenerateInteractables();
            
            // Setup doors
            SetupDoors();
        }
        
        private void GenerateEnemies()
        {
            if (roomData.roomType == RoomType.Start || roomData.roomType == RoomType.Rest)
                return; // No enemies in safe rooms
            
            foreach (var enemyData in roomData.enemies)
            {
                SpawnEnemy(enemyData);
            }
        }
        
        private void SpawnEnemy(EnemySpawnData enemyData)
        {
            // This will be implemented when we create the enemy system
            UnityEngine.Debug.Log($"👹 Spawning enemy: {enemyData.enemyId} at {enemyData.spawnPosition}");
            
            // For now, create a placeholder
            GameObject enemyPlaceholder = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            enemyPlaceholder.transform.position = transform.position + enemyData.spawnPosition;
            enemyPlaceholder.transform.SetParent(transform);
            enemyPlaceholder.name = $"Enemy_{enemyData.enemyId}";
            
            // Color based on enemy type
            Renderer renderer = enemyPlaceholder.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = enemyData.isBoss ? Color.red : Color.yellow;
            }
            
            spawnedEnemies.Add(enemyPlaceholder);
        }
        
        private void GenerateItems()
        {
            foreach (var itemData in roomData.items)
            {
                SpawnItem(itemData);
            }
        }
        
        private void SpawnItem(ItemSpawnData itemData)
        {
            UnityEngine.Debug.Log($"💎 Spawning item: {itemData.itemId} at {itemData.spawnPosition}");
            
            // Create placeholder item
            GameObject itemPlaceholder = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            itemPlaceholder.transform.position = transform.position + itemData.spawnPosition;
            itemPlaceholder.transform.SetParent(transform);
            itemPlaceholder.name = $"Item_{itemData.itemId}";
            itemPlaceholder.transform.localScale = Vector3.one * 0.5f;
            
            // Color based on rarity
            Renderer renderer = itemPlaceholder.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = GetRarityColor(itemData.rarity);
            }
            
            // Make it collectible
            itemPlaceholder.AddComponent<SphereCollider>().isTrigger = true;
            
            spawnedItems.Add(itemPlaceholder);
        }
        
        private Color GetRarityColor(ItemRarity rarity)
        {
            switch (rarity)
            {
                case ItemRarity.Common: return Color.white;
                case ItemRarity.Uncommon: return Color.green;
                case ItemRarity.Rare: return Color.blue;
                case ItemRarity.Epic: return Color.magenta;
                case ItemRarity.Legendary: return Color.yellow;
                default: return Color.gray;
            }
        }
        
        private void GenerateInteractables()
        {
            foreach (var interactableData in roomData.interactables)
            {
                SpawnInteractable(interactableData);
            }
        }
        
        private void SpawnInteractable(InteractableData interactableData)
        {
            UnityEngine.Debug.Log($"🔧 Spawning interactable: {interactableData.type} at {interactableData.position}");
            
            // Create placeholder interactable
            GameObject interactablePlaceholder = GameObject.CreatePrimitive(PrimitiveType.Cube);
            interactablePlaceholder.transform.position = transform.position + interactableData.position;
            interactablePlaceholder.transform.SetParent(transform);
            interactablePlaceholder.name = $"Interactable_{interactableData.type}";
            
            // Color based on type
            Renderer renderer = interactablePlaceholder.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = GetInteractableColor(interactableData.type);
            }
            
            spawnedInteractables.Add(interactablePlaceholder);
        }
        
        private Color GetInteractableColor(InteractableType type)
        {
            switch (type)
            {
                case InteractableType.Chest: return Color.yellow;
                case InteractableType.Switch: return Color.red;
                case InteractableType.Altar: return Color.cyan;
                case InteractableType.Terminal: return Color.green;
                case InteractableType.Shrine: return Color.magenta;
                case InteractableType.Portal: return Color.blue;
                case InteractableType.Merchant: return Color.white;
                case InteractableType.CraftingStation: return Color.gray;
                default: return Color.black;
            }
        }
        
        private void SetupDoors()
        {
            // Setup doors based on connections
            foreach (var connection in roomData.connections)
            {
                CreateDoor(connection);
            }
        }
        
        private void CreateDoor(Vector2Int direction)
        {
            // Create door placeholder
            GameObject door = GameObject.CreatePrimitive(PrimitiveType.Cube);
            door.transform.SetParent(transform);
            door.name = $"Door_{direction}";
            
            // Position door at edge of room
            Vector3 doorPosition = Vector3.zero;
            if (direction == Vector2Int.up) doorPosition = Vector3.up * 9f;
            else if (direction == Vector2Int.down) doorPosition = Vector3.down * 9f;
            else if (direction == Vector2Int.left) doorPosition = Vector3.left * 9f;
            else if (direction == Vector2Int.right) doorPosition = Vector3.right * 9f;
            
            door.transform.localPosition = doorPosition;
            door.transform.localScale = new Vector3(2f, 3f, 0.5f);
            
            // Make door brown
            door.GetComponent<Renderer>().material.color = new Color(0.6f, 0.3f, 0.1f);
        }
        
        #endregion
        
        #region Visual and Audio Setup
        
        private void SetupVisualElements()
        {
            // Setup background based on room type
            if (roomBackground != null)
            {
                roomBackground.color = GetRoomTypeColor();
            }
            
            // Setup decorations
            SetupDecorations();
            
            // Setup particles
            SetupParticles();
        }
        
        private Color GetRoomTypeColor()
        {
            switch (roomData.roomType)
            {
                case RoomType.Start: return new Color(0.2f, 0.8f, 0.2f, 0.3f); // Green
                case RoomType.Boss: return new Color(0.8f, 0.2f, 0.2f, 0.3f); // Red
                case RoomType.Combat: return new Color(0.8f, 0.8f, 0.2f, 0.3f); // Yellow
                case RoomType.Treasure: return new Color(0.2f, 0.8f, 0.8f, 0.3f); // Cyan
                case RoomType.Shop: return new Color(0.8f, 0.2f, 0.8f, 0.3f); // Magenta
                case RoomType.Rest: return new Color(0.2f, 0.2f, 0.8f, 0.3f); // Blue
                case RoomType.Secret: return new Color(0.5f, 0.2f, 0.8f, 0.3f); // Purple
                default: return new Color(0.5f, 0.5f, 0.5f, 0.3f); // Gray
            }
        }
        
        private void SetupDecorations()
        {
            // Add decorative elements based on room type and biome
            // This would be expanded with actual decoration prefabs
        }
        
        private void SetupParticles()
        {
            if (ambientParticles != null)
            {
                var main = ambientParticles.main;
                main.startColor = GetRoomTypeColor();
            }
        }
        
        private void SetupAudio()
        {
            if (ambientSound != null && roomAudioSource != null)
            {
                roomAudioSource.clip = ambientSound;
                roomAudioSource.volume = 0.3f;
            }
        }
        
        #endregion
        
        #region Player Interaction
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                EnterRoom();
            }
        }
        
        private void OnTriggerExit2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                ExitRoom();
            }
        }
        
        private void EnterRoom()
        {
            if (isPlayerInside) return;
            
            isPlayerInside = true;
            roomData.isVisited = true;
            
            // Play enter sound
            if (enterSound != null && roomAudioSource != null)
            {
                roomAudioSource.PlayOneShot(enterSound);
            }
            
            // Start ambient sound
            if (ambientSound != null && roomAudioSource != null)
            {
                roomAudioSource.Play();
            }
            
            // Activate room (spawn enemies, etc.)
            ActivateRoom();
            
            // Notify managers
            if (levelGenerator != null)
            {
                levelGenerator.EnterRoom(roomData.gridPosition);
            }
            
            OnRoomEntered?.Invoke(this);
            
            UnityEngine.Debug.Log($"🚪 Player entered room: {roomData.roomType}");
        }
        
        private void ExitRoom()
        {
            if (!isPlayerInside) return;
            
            isPlayerInside = false;
            
            // Stop ambient sound
            if (roomAudioSource != null)
            {
                roomAudioSource.Stop();
            }
            
            OnRoomExited?.Invoke(this);
            
            UnityEngine.Debug.Log($"🚪 Player exited room: {roomData.roomType}");
        }
        
        #endregion
        
        #region Room Activation
        
        private void ActivateRoom()
        {
            // Activate enemies
            foreach (var enemy in spawnedEnemies)
            {
                if (enemy != null)
                {
                    enemy.SetActive(true);
                }
            }
            
            // Start room-specific behavior
            StartRoomBehavior();
        }
        
        private void StartRoomBehavior()
        {
            switch (roomData.roomType)
            {
                case RoomType.Combat:
                    StartCombatRoom();
                    break;
                case RoomType.Boss:
                    StartBossRoom();
                    break;
                case RoomType.Treasure:
                    StartTreasureRoom();
                    break;
                case RoomType.Shop:
                    StartShopRoom();
                    break;
                case RoomType.Rest:
                    StartRestRoom();
                    break;
            }
        }
        
        private void StartCombatRoom()
        {
            // Lock doors until enemies are defeated
            LockDoors();
        }
        
        private void StartBossRoom()
        {
            // Special boss room behavior
            LockDoors();
            // Could trigger boss music, special effects, etc.
        }
        
        private void StartTreasureRoom()
        {
            // Treasure room behavior
            // Items are already spawned
        }
        
        private void StartShopRoom()
        {
            // Shop room behavior
            // Merchant interaction
        }
        
        private void StartRestRoom()
        {
            // Rest room behavior - heal player
            StartCoroutine(HealPlayerOverTime());
        }
        
        #endregion
        
        #region Room Completion
        
        public void CheckRoomCompletion()
        {
            if (roomData.isCleared) return;
            
            bool canComplete = false;
            
            switch (roomData.roomType)
            {
                case RoomType.Combat:
                case RoomType.Boss:
                    canComplete = !roomData.HasLivingEnemies();
                    break;
                case RoomType.Start:
                case RoomType.Rest:
                case RoomType.Shop:
                case RoomType.Treasure:
                    canComplete = true; // These rooms are completed by entering
                    break;
            }
            
            if (canComplete)
            {
                CompleteRoom();
            }
        }
        
        private void CompleteRoom()
        {
            roomData.isCleared = true;
            
            // Play clear sound
            if (clearSound != null && roomAudioSource != null)
            {
                roomAudioSource.PlayOneShot(clearSound);
            }
            
            // Unlock doors
            UnlockDoors();
            
            // Award rewards
            AwardRoomRewards();
            
            // Notify managers
            if (levelGenerator != null)
            {
                levelGenerator.ClearCurrentRoom();
            }
            
            if (runManager != null)
            {
                runManager.ClearRoom();
            }
            
            OnRoomCleared?.Invoke(this);
            
            UnityEngine.Debug.Log($"✅ Room completed: {roomData.roomType}");
        }
        
        private void AwardRoomRewards()
        {
            // Award experience, currency, etc.
            // This would integrate with the progression system
        }
        
        #endregion
        
        #region Door Management
        
        private void LockDoors()
        {
            // Lock all doors
            foreach (var door in roomData.doors)
            {
                door.isLocked = true;
            }
        }
        
        private void UnlockDoors()
        {
            // Unlock all doors
            foreach (var door in roomData.doors)
            {
                door.isLocked = false;
            }
        }
        
        #endregion
        
        #region Coroutines
        
        private IEnumerator HealthDrainCoroutine(float drainRate)
        {
            while (isPlayerInside)
            {
                // Drain player health
                PlayerController player = FindFirstObjectByType<PlayerController>();
                if (player != null)
                {
                    ChronoForge.Player.HealthSystem playerHealth = player.GetComponent<ChronoForge.Player.HealthSystem>();
                    if (playerHealth != null)
                    {
                        playerHealth.TakeDamage(drainRate * Time.deltaTime);
                    }
                }
                
                yield return null;
            }
        }
        
        private IEnumerator HealPlayerOverTime()
        {
            float healRate = 10f; // HP per second
            
            while (isPlayerInside)
            {
                PlayerController player = FindFirstObjectByType<PlayerController>();
                if (player != null)
                {
                    ChronoForge.Player.HealthSystem playerHealth = player.GetComponent<ChronoForge.Player.HealthSystem>();
                    if (playerHealth != null)
                    {
                        playerHealth.Heal(healRate * Time.deltaTime);
                    }
                }
                
                yield return new WaitForSeconds(1f);
            }
        }
        
        #endregion
        
        #region Update
        
        private void UpdateRoom()
        {
            if (!isInitialized || !isPlayerInside) return;
            
            // Check for room completion
            CheckRoomCompletion();
            
            // Update room-specific behavior
            UpdateRoomBehavior();
        }
        
        private void UpdateRoomBehavior()
        {
            // Room-specific update logic
            switch (roomData.roomType)
            {
                case RoomType.Combat:
                    UpdateCombatRoom();
                    break;
                case RoomType.Boss:
                    UpdateBossRoom();
                    break;
            }
        }
        
        private void UpdateCombatRoom()
        {
            // Check if all enemies are defeated
            bool allEnemiesDefeated = true;
            foreach (var enemy in spawnedEnemies)
            {
                if (enemy != null && enemy.activeInHierarchy)
                {
                    allEnemiesDefeated = false;
                    break;
                }
            }
            
            if (allEnemiesDefeated && !roomData.isCleared)
            {
                roomData.ClearAllEnemies();
                CompleteRoom();
            }
        }
        
        private void UpdateBossRoom()
        {
            // Similar to combat room but with boss-specific logic
            UpdateCombatRoom();
        }
        
        #endregion
        
        #region Public Methods
        
        public bool IsPlayerInside()
        {
            return isPlayerInside;
        }
        
        public RoomData GetRoomData()
        {
            return roomData;
        }
        
        public List<GameObject> GetSpawnedEnemies()
        {
            return new List<GameObject>(spawnedEnemies);
        }
        
        public void ForceCompleteRoom()
        {
            CompleteRoom();
        }
        
        #endregion
    }
}
