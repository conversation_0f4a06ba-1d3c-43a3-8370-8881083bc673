using UnityEngine;
using UnityEditor;
using SpaceClicker.Core;
using System.Numerics;

namespace SpaceClicker.Editor
{
    /// <summary>
    /// Outil de balancing pour Space Clicker
    /// </summary>
    public class GameplayBalancer : EditorWindow
    {
        [Header("Resource Generation")]
        public int baseClickPower = 1;
        public float productionMultiplier = 1.5f;
        public float upgradeEffectMultiplier = 1.2f;
        
        [Header("Upgrade Costs")]
        public int baseUpgradeCost = 10;
        public float costMultiplier = 1.5f;
        public int maxUpgradeLevel = 100;
        
        [Header("Production Rates")]
        public float energyProductionRate = 1.0f;
        public float mineralsProductionRate = 0.8f;
        public float researchProductionRate = 0.5f;
        public float currencyProductionRate = 0.3f;
        
        [Header("Balance Testing")]
        public int simulationDuration = 300; // seconds
        public bool showDetailedResults = true;
        
        private Vector2 scrollPosition;
        private string balanceResults = "";
        
        [MenuItem("Space Clicker/Gameplay Balancer")]
        public static void ShowWindow()
        {
            GetWindow<GameplayBalancer>("Gameplay Balancer");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Space Clicker Gameplay Balancer", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            // Resource Generation Settings
            GUILayout.Label("Resource Generation", EditorStyles.boldLabel);
            baseClickPower = EditorGUILayout.IntField("Base Click Power", baseClickPower);
            productionMultiplier = EditorGUILayout.FloatField("Production Multiplier", productionMultiplier);
            upgradeEffectMultiplier = EditorGUILayout.FloatField("Upgrade Effect Multiplier", upgradeEffectMultiplier);
            
            GUILayout.Space(10);
            
            // Upgrade Costs Settings
            GUILayout.Label("Upgrade Costs", EditorStyles.boldLabel);
            baseUpgradeCost = EditorGUILayout.IntField("Base Upgrade Cost", baseUpgradeCost);
            costMultiplier = EditorGUILayout.FloatField("Cost Multiplier", costMultiplier);
            maxUpgradeLevel = EditorGUILayout.IntField("Max Upgrade Level", maxUpgradeLevel);
            
            GUILayout.Space(10);
            
            // Production Rates Settings
            GUILayout.Label("Production Rates (per second)", EditorStyles.boldLabel);
            energyProductionRate = EditorGUILayout.FloatField("Energy Rate", energyProductionRate);
            mineralsProductionRate = EditorGUILayout.FloatField("Minerals Rate", mineralsProductionRate);
            researchProductionRate = EditorGUILayout.FloatField("Research Rate", researchProductionRate);
            currencyProductionRate = EditorGUILayout.FloatField("Currency Rate", currencyProductionRate);
            
            GUILayout.Space(10);
            
            // Balance Testing Settings
            GUILayout.Label("Balance Testing", EditorStyles.boldLabel);
            simulationDuration = EditorGUILayout.IntField("Simulation Duration (seconds)", simulationDuration);
            showDetailedResults = EditorGUILayout.Toggle("Show Detailed Results", showDetailedResults);
            
            GUILayout.Space(20);
            
            // Action Buttons
            if (GUILayout.Button("Apply Settings to Game", GUILayout.Height(30)))
            {
                ApplySettingsToGame();
            }
            
            if (GUILayout.Button("Run Balance Simulation", GUILayout.Height(25)))
            {
                RunBalanceSimulation();
            }
            
            if (GUILayout.Button("Reset to Default Values", GUILayout.Height(25)))
            {
                ResetToDefaults();
            }
            
            GUILayout.Space(10);
            
            // Results Display
            if (!string.IsNullOrEmpty(balanceResults))
            {
                GUILayout.Label("Simulation Results:", EditorStyles.boldLabel);
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
                EditorGUILayout.TextArea(balanceResults, GUILayout.ExpandHeight(true));
                EditorGUILayout.EndScrollView();
            }
        }
        
        private void ApplySettingsToGame()
        {
            Debug.Log("🎮 Applying gameplay settings...");
            
            // Update GameConstants
            UpdateGameConstants();
            
            // Update existing managers in scene if available
            UpdateSceneManagers();
            
            // Mark scene as dirty
            if (Application.isPlaying)
            {
                Debug.Log("⚠️ Settings applied in play mode - restart to see full effect");
            }
            else
            {
                UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(
                    UnityEngine.SceneManagement.SceneManager.GetActiveScene());
            }
            
            Debug.Log("✅ Gameplay settings applied!");
        }
        
        private void UpdateGameConstants()
        {
            // Note: In a real implementation, you'd want to make GameConstants configurable
            // For now, we'll update the values through reflection or create a ScriptableObject
            
            // This is a simplified approach - in production, use ScriptableObjects for configuration
            PlayerPrefs.SetInt("BaseClickPower", baseClickPower);
            PlayerPrefs.SetFloat("ProductionMultiplier", productionMultiplier);
            PlayerPrefs.SetFloat("UpgradeEffectMultiplier", upgradeEffectMultiplier);
            PlayerPrefs.SetInt("BaseUpgradeCost", baseUpgradeCost);
            PlayerPrefs.SetFloat("CostMultiplier", costMultiplier);
            PlayerPrefs.SetInt("MaxUpgradeLevel", maxUpgradeLevel);
            
            PlayerPrefs.SetFloat("EnergyProductionRate", energyProductionRate);
            PlayerPrefs.SetFloat("MineralsProductionRate", mineralsProductionRate);
            PlayerPrefs.SetFloat("ResearchProductionRate", researchProductionRate);
            PlayerPrefs.SetFloat("CurrencyProductionRate", currencyProductionRate);
            
            PlayerPrefs.Save();
        }
        
        private void UpdateSceneManagers()
        {
            // Update ResourceManager if present
            ResourceManager resourceManager = FindObjectOfType<ResourceManager>();
            if (resourceManager != null)
            {
                resourceManager.SetProductionRate(ResourceType.Energy, energyProductionRate);
                resourceManager.SetProductionRate(ResourceType.Minerals, mineralsProductionRate);
                resourceManager.SetProductionRate(ResourceType.ResearchData, researchProductionRate);
                resourceManager.SetProductionRate(ResourceType.SpaceCurrency, currencyProductionRate);
                
                Debug.Log("✅ ResourceManager updated");
            }
            
            // Update UpgradeManager if present
            UpgradeManager upgradeManager = FindObjectOfType<UpgradeManager>();
            if (upgradeManager != null)
            {
                // Update upgrade configurations
                // This would require exposing configuration in UpgradeManager
                Debug.Log("✅ UpgradeManager notified of changes");
            }
        }
        
        private void RunBalanceSimulation()
        {
            Debug.Log("🧮 Running balance simulation...");
            
            balanceResults = "SPACE CLICKER BALANCE SIMULATION\n";
            balanceResults += "================================\n\n";
            
            // Simulation parameters
            balanceResults += $"Simulation Duration: {simulationDuration} seconds\n";
            balanceResults += $"Base Click Power: {baseClickPower}\n";
            balanceResults += $"Production Multiplier: {productionMultiplier:F2}\n";
            balanceResults += $"Upgrade Effect Multiplier: {upgradeEffectMultiplier:F2}\n\n";
            
            // Simulate resource generation
            SimulateResourceGeneration();
            
            // Simulate upgrade progression
            SimulateUpgradeProgression();
            
            // Calculate balance metrics
            CalculateBalanceMetrics();
            
            Debug.Log("✅ Balance simulation completed");
        }
        
        private void SimulateResourceGeneration()
        {
            balanceResults += "RESOURCE GENERATION SIMULATION\n";
            balanceResults += "------------------------------\n";
            
            // Simulate clicking and passive production
            float totalEnergy = 0f;
            float totalMinerals = 0f;
            float totalResearch = 0f;
            float totalCurrency = 0f;
            
            // Assume 1 click per second for simulation
            int clicksPerSecond = 1;
            
            for (int second = 0; second < simulationDuration; second++)
            {
                // Clicking production
                totalEnergy += baseClickPower * clicksPerSecond;
                totalMinerals += baseClickPower * clicksPerSecond * 0.8f; // Slightly less efficient
                totalResearch += baseClickPower * clicksPerSecond * 0.6f;
                totalCurrency += baseClickPower * clicksPerSecond * 0.4f;
                
                // Passive production
                totalEnergy += energyProductionRate;
                totalMinerals += mineralsProductionRate;
                totalResearch += researchProductionRate;
                totalCurrency += currencyProductionRate;
                
                // Log progress at intervals
                if (showDetailedResults && (second + 1) % 60 == 0)
                {
                    int minute = (second + 1) / 60;
                    balanceResults += $"Minute {minute}: Energy={totalEnergy:F0}, Minerals={totalMinerals:F0}, Research={totalResearch:F0}, Currency={totalCurrency:F0}\n";
                }
            }
            
            balanceResults += $"\nFinal Resources after {simulationDuration} seconds:\n";
            balanceResults += $"Energy: {totalEnergy:F0}\n";
            balanceResults += $"Minerals: {totalMinerals:F0}\n";
            balanceResults += $"Research: {totalResearch:F0}\n";
            balanceResults += $"Currency: {totalCurrency:F0}\n\n";
        }
        
        private void SimulateUpgradeProgression()
        {
            balanceResults += "UPGRADE PROGRESSION SIMULATION\n";
            balanceResults += "------------------------------\n";
            
            // Simulate how many upgrades can be purchased with generated resources
            float availableEnergy = energyProductionRate * simulationDuration;
            
            int upgradeLevel = 0;
            float totalCost = 0f;
            
            while (upgradeLevel < maxUpgradeLevel)
            {
                float upgradeCost = baseUpgradeCost * Mathf.Pow(costMultiplier, upgradeLevel);
                
                if (totalCost + upgradeCost > availableEnergy)
                    break;
                
                totalCost += upgradeCost;
                upgradeLevel++;
                
                if (showDetailedResults && upgradeLevel % 10 == 0)
                {
                    balanceResults += $"Level {upgradeLevel}: Cost={upgradeCost:F0}, Total Spent={totalCost:F0}\n";
                }
            }
            
            balanceResults += $"\nMaximum upgrade level reachable: {upgradeLevel}\n";
            balanceResults += $"Total energy spent on upgrades: {totalCost:F0}\n";
            balanceResults += $"Energy remaining: {availableEnergy - totalCost:F0}\n\n";
        }
        
        private void CalculateBalanceMetrics()
        {
            balanceResults += "BALANCE METRICS\n";
            balanceResults += "---------------\n";
            
            // Calculate time to reach certain milestones
            float timeToFirst100Energy = 100f / (energyProductionRate + baseClickPower);
            float timeToFirst1000Energy = 1000f / (energyProductionRate + baseClickPower);
            float timeToFirstUpgrade = baseUpgradeCost / (energyProductionRate + baseClickPower);
            
            balanceResults += $"Time to reach 100 energy: {timeToFirst100Energy:F1} seconds\n";
            balanceResults += $"Time to reach 1000 energy: {timeToFirst1000Energy:F1} seconds\n";
            balanceResults += $"Time to afford first upgrade: {timeToFirstUpgrade:F1} seconds\n";
            
            // Calculate progression rate
            float progressionRate = (energyProductionRate + baseClickPower) / baseUpgradeCost;
            balanceResults += $"Progression rate: {progressionRate:F3} (higher = faster progression)\n";
            
            // Balance recommendations
            balanceResults += "\nBALANCE RECOMMENDATIONS:\n";
            
            if (timeToFirstUpgrade > 60)
            {
                balanceResults += "⚠️ First upgrade takes too long - consider reducing base cost\n";
            }
            else if (timeToFirstUpgrade < 10)
            {
                balanceResults += "⚠️ First upgrade too easy - consider increasing base cost\n";
            }
            else
            {
                balanceResults += "✅ First upgrade timing looks good\n";
            }
            
            if (progressionRate > 1.0f)
            {
                balanceResults += "⚠️ Progression might be too fast - consider balancing\n";
            }
            else if (progressionRate < 0.1f)
            {
                balanceResults += "⚠️ Progression might be too slow - consider balancing\n";
            }
            else
            {
                balanceResults += "✅ Progression rate looks balanced\n";
            }
        }
        
        private void ResetToDefaults()
        {
            baseClickPower = 1;
            productionMultiplier = 1.5f;
            upgradeEffectMultiplier = 1.2f;
            baseUpgradeCost = 10;
            costMultiplier = 1.5f;
            maxUpgradeLevel = 100;
            energyProductionRate = 1.0f;
            mineralsProductionRate = 0.8f;
            researchProductionRate = 0.5f;
            currencyProductionRate = 0.3f;
            simulationDuration = 300;
            showDetailedResults = true;
            
            balanceResults = "";
            
            Debug.Log("🔄 Reset to default values");
        }
    }
}
