#!/usr/bin/env python3
"""
Script de surveillance du bot de publication
Relance automatiquement le bot en cas d'arrêt et envoie des notifications
"""

import subprocess
import time
import sys
import os
import logging
from datetime import datetime

# Configuration
BOT_COMMAND = ["python", "cli.py", "start"]
CHECK_INTERVAL = 60  # Vérifier toutes les 60 secondes
MAX_RESTARTS = 5  # Maximum 5 redémarrages par heure
RESTART_WINDOW = 3600  # Fenêtre de 1 heure

# Logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_monitor.log'),
        logging.StreamHandler()
    ]
)

class BotMonitor:
    def __init__(self):
        self.process = None
        self.restart_times = []
        self.start_time = datetime.now()
        
    def is_bot_running(self):
        """Vérifie si le bot est en cours d'exécution"""
        if self.process is None:
            return False
            
        try:
            # Vérifier si le processus existe encore
            return self.process.poll() is None
        except:
            return False
    
    def start_bot(self):
        """Démarre le bot"""
        try:
            logging.info("🚀 Démarrage du bot...")
            self.process = subprocess.Popen(
                BOT_COMMAND,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            logging.info(f"✅ Bot démarré avec PID: {self.process.pid}")
            return True
        except Exception as e:
            logging.error(f"❌ Erreur lors du démarrage du bot: {e}")
            return False
    
    def stop_bot(self):
        """Arrête le bot"""
        if self.process:
            try:
                logging.info("⏹️ Arrêt du bot...")
                self.process.terminate()
                self.process.wait(timeout=10)
                logging.info("✅ Bot arrêté")
            except subprocess.TimeoutExpired:
                logging.warning("⚠️ Arrêt forcé du bot")
                self.process.kill()
            except Exception as e:
                logging.error(f"❌ Erreur lors de l'arrêt: {e}")
            finally:
                self.process = None
    
    def can_restart(self):
        """Vérifie si on peut redémarrer (limite de redémarrages)"""
        now = datetime.now()
        # Nettoyer les anciens redémarrages (plus d'1 heure)
        self.restart_times = [t for t in self.restart_times if (now - t).seconds < RESTART_WINDOW]
        
        return len(self.restart_times) < MAX_RESTARTS
    
    def restart_bot(self):
        """Redémarre le bot"""
        if not self.can_restart():
            logging.error(f"❌ Trop de redémarrages ({MAX_RESTARTS}) dans la dernière heure")
            return False
        
        logging.warning("🔄 Redémarrage du bot...")
        self.stop_bot()
        time.sleep(5)  # Attendre 5 secondes
        
        if self.start_bot():
            self.restart_times.append(datetime.now())
            return True
        return False
    
    def get_bot_output(self):
        """Récupère la sortie du bot"""
        if not self.process:
            return None
            
        try:
            # Lire la sortie non-bloquante
            output = self.process.stdout.readline()
            if output:
                return output.strip()
        except:
            pass
        return None
    
    def send_notification(self, message):
        """Envoie une notification (peut être étendu)"""
        logging.info(f"📢 NOTIFICATION: {message}")
        # TODO: Ajouter envoi email, Discord, etc.
    
    def get_status_info(self):
        """Obtient les informations de statut"""
        uptime = datetime.now() - self.start_time
        status = {
            'uptime': str(uptime).split('.')[0],  # Sans microsecondes
            'restarts': len(self.restart_times),
            'running': self.is_bot_running(),
            'pid': self.process.pid if self.process else None
        }
        return status
    
    def monitor(self):
        """Boucle principale de surveillance"""
        logging.info("🔍 Démarrage de la surveillance du bot")
        
        # Démarrage initial
        if not self.start_bot():
            logging.error("❌ Impossible de démarrer le bot initialement")
            return
        
        self.send_notification("Bot de publication démarré et surveillé")
        
        try:
            while True:
                # Vérifier si le bot fonctionne
                if not self.is_bot_running():
                    logging.warning("⚠️ Bot arrêté détecté")
                    self.send_notification("Bot arrêté - Tentative de redémarrage")
                    
                    if not self.restart_bot():
                        logging.error("❌ Impossible de redémarrer le bot")
                        self.send_notification("ERREUR: Impossible de redémarrer le bot")
                        break
                    else:
                        self.send_notification("Bot redémarré avec succès")
                
                # Lire et logger la sortie du bot
                output = self.get_bot_output()
                if output:
                    logging.info(f"BOT: {output}")
                
                # Afficher le statut périodiquement
                if int(time.time()) % 300 == 0:  # Toutes les 5 minutes
                    status = self.get_status_info()
                    logging.info(f"📊 Statut: Uptime={status['uptime']}, Redémarrages={status['restarts']}, PID={status['pid']}")
                
                time.sleep(CHECK_INTERVAL)
                
        except KeyboardInterrupt:
            logging.info("🛑 Arrêt demandé par l'utilisateur")
            self.send_notification("Surveillance du bot arrêtée")
        except Exception as e:
            logging.error(f"❌ Erreur dans la surveillance: {e}")
            self.send_notification(f"ERREUR de surveillance: {e}")
        finally:
            self.stop_bot()

def main():
    """Point d'entrée principal"""
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        # Afficher le statut sans démarrer la surveillance
        try:
            result = subprocess.run(["python", "cli.py", "status"], 
                                  capture_output=True, text=True, timeout=30)
            print(result.stdout)
            if result.stderr:
                print("Erreurs:", result.stderr)
        except Exception as e:
            print(f"Erreur lors de la vérification du statut: {e}")
        return
    
    # Vérifier qu'on est dans le bon répertoire
    if not os.path.exists("cli.py"):
        print("❌ Erreur: cli.py non trouvé. Exécutez ce script depuis le dossier du bot.")
        sys.exit(1)
    
    # Démarrer la surveillance
    monitor = BotMonitor()
    monitor.monitor()

if __name__ == "__main__":
    main()
