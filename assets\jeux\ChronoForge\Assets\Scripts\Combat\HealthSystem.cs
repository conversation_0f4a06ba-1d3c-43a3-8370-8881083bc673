using UnityEngine;
using System.Collections;

namespace ChronoForge.Combat
{
    /// <summary>
    /// Système de santé universel pour ChronoForge
    /// </summary>
    public class HealthSystem : MonoBehaviour
    {
        [Header("Health Settings")]
        public float maxHealth = 100f;
        public float currentHealth;
        public bool isInvulnerable = false;
        public float invulnerabilityDuration = 0.5f;
        
        [Header("Regeneration")]
        public bool canRegenerate = false;
        public float regenerationRate = 1f; // HP per second
        public float regenerationDelay = 3f; // Delay after taking damage
        
        [Header("Shield System")]
        public bool hasShield = false;
        public float maxShield = 50f;
        public float currentShield = 0f;
        public float shieldRegenRate = 2f;
        public float shieldRegenDelay = 5f;
        
        [Header("Visual Effects")]
        public GameObject damageEffect;
        public GameObject healEffect;
        public GameObject deathEffect;
        
        [Header("Audio")]
        public AudioClip damageSound;
        public AudioClip healSound;
        public AudioClip deathSound;
        public AudioClip shieldBreakSound;
        
        // Events
        public System.Action<float> OnHealthChanged;
        public System.Action<float> OnDamageTaken;
        public System.Action<float> OnHealed;
        public System.Action OnDeath;
        public System.Action OnShieldBroken;
        public System.Action<float> OnShieldChanged;
        
        // Components
        private AudioSource audioSource;
        private SpriteRenderer spriteRenderer;
        
        // State
        private bool isDead = false;
        private float lastDamageTime = 0f;
        private float lastShieldDamageTime = 0f;
        private Coroutine invulnerabilityCoroutine;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeHealthSystem();
        }
        
        private void Start()
        {
            InitializeHealth();
        }
        
        private void Update()
        {
            UpdateRegeneration();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeHealthSystem()
        {
            audioSource = GetComponent<AudioSource>();
            spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            
            UnityEngine.Debug.Log($"❤️ HealthSystem initialized for {gameObject.name}");
        }
        
        private void InitializeHealth()
        {
            currentHealth = maxHealth;
            
            if (hasShield)
            {
                currentShield = maxShield;
            }
            
            NotifyHealthChanged();
        }
        
        #endregion
        
        #region Damage System
        
        public bool TakeDamage(float damage)
        {
            if (isDead || isInvulnerable || damage <= 0f) return false;
            
            float actualDamage = damage;
            
            // Apply shield first
            if (hasShield && currentShield > 0f)
            {
                actualDamage = ApplyShieldDamage(damage);
            }
            
            // Apply remaining damage to health
            if (actualDamage > 0f)
            {
                ApplyHealthDamage(actualDamage);
            }
            
            // Reset regeneration timers
            lastDamageTime = Time.time;
            lastShieldDamageTime = Time.time;
            
            // Trigger invulnerability
            if (invulnerabilityDuration > 0f)
            {
                StartInvulnerability();
            }
            
            // Play effects
            PlayDamageEffects(damage);
            
            // Check for death
            if (currentHealth <= 0f && !isDead)
            {
                Die();
            }
            
            OnDamageTaken?.Invoke(damage);
            return true;
        }
        
        private float ApplyShieldDamage(float damage)
        {
            float shieldDamage = Mathf.Min(damage, currentShield);
            currentShield -= shieldDamage;
            
            if (currentShield <= 0f)
            {
                currentShield = 0f;
                OnShieldBroken?.Invoke();
                
                // Play shield break effect
                if (audioSource != null && shieldBreakSound != null)
                {
                    audioSource.PlayOneShot(shieldBreakSound);
                }
            }
            
            OnShieldChanged?.Invoke(currentShield);
            
            // Return remaining damage
            return damage - shieldDamage;
        }
        
        private void ApplyHealthDamage(float damage)
        {
            currentHealth -= damage;
            currentHealth = Mathf.Max(0f, currentHealth);
            
            NotifyHealthChanged();
        }
        
        #endregion
        
        #region Healing System
        
        public bool Heal(float amount)
        {
            if (isDead || amount <= 0f) return false;
            
            float oldHealth = currentHealth;
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            
            float actualHealing = currentHealth - oldHealth;
            
            if (actualHealing > 0f)
            {
                PlayHealEffects(actualHealing);
                NotifyHealthChanged();
                OnHealed?.Invoke(actualHealing);
                
                UnityEngine.Debug.Log($"💚 Healed {actualHealing:F1} HP");
                return true;
            }
            
            return false;
        }
        
        public bool HealShield(float amount)
        {
            if (!hasShield || amount <= 0f) return false;
            
            float oldShield = currentShield;
            currentShield = Mathf.Min(maxShield, currentShield + amount);
            
            float actualHealing = currentShield - oldShield;
            
            if (actualHealing > 0f)
            {
                OnShieldChanged?.Invoke(currentShield);
                UnityEngine.Debug.Log($"🛡️ Shield restored {actualHealing:F1}");
                return true;
            }
            
            return false;
        }
        
        #endregion
        
        #region Regeneration
        
        private void UpdateRegeneration()
        {
            if (isDead) return;
            
            // Health regeneration
            if (canRegenerate && currentHealth < maxHealth)
            {
                if (Time.time - lastDamageTime >= regenerationDelay)
                {
                    float regenAmount = regenerationRate * Time.deltaTime;
                    Heal(regenAmount);
                }
            }
            
            // Shield regeneration
            if (hasShield && currentShield < maxShield)
            {
                if (Time.time - lastShieldDamageTime >= shieldRegenDelay)
                {
                    float shieldRegen = shieldRegenRate * Time.deltaTime;
                    HealShield(shieldRegen);
                }
            }
        }
        
        #endregion
        
        #region Invulnerability
        
        private void StartInvulnerability()
        {
            if (invulnerabilityCoroutine != null)
            {
                StopCoroutine(invulnerabilityCoroutine);
            }
            
            invulnerabilityCoroutine = StartCoroutine(InvulnerabilityCoroutine());
        }
        
        private IEnumerator InvulnerabilityCoroutine()
        {
            isInvulnerable = true;
            
            // Visual feedback (flashing)
            if (spriteRenderer != null)
            {
                StartCoroutine(FlashEffect());
            }
            
            yield return new WaitForSeconds(invulnerabilityDuration);
            
            isInvulnerable = false;
        }
        
        private IEnumerator FlashEffect()
        {
            if (spriteRenderer == null) yield break;
            
            Color originalColor = spriteRenderer.color;
            float flashDuration = invulnerabilityDuration;
            float flashInterval = 0.1f;
            
            for (float t = 0; t < flashDuration; t += flashInterval)
            {
                spriteRenderer.color = Color.red;
                yield return new WaitForSeconds(flashInterval / 2);
                
                spriteRenderer.color = originalColor;
                yield return new WaitForSeconds(flashInterval / 2);
            }
            
            spriteRenderer.color = originalColor;
        }
        
        #endregion
        
        #region Death System
        
        private void Die()
        {
            if (isDead) return;
            
            isDead = true;
            
            UnityEngine.Debug.Log($"💀 {gameObject.name} died");
            
            // Play death effects
            PlayDeathEffects();
            
            // Notify death
            OnDeath?.Invoke();
        }
        
        public void Revive(float healthPercentage = 1f)
        {
            if (!isDead) return;
            
            isDead = false;
            currentHealth = maxHealth * healthPercentage;
            
            if (hasShield)
            {
                currentShield = maxShield;
            }
            
            NotifyHealthChanged();
            
            UnityEngine.Debug.Log($"✨ {gameObject.name} revived with {currentHealth:F1} HP");
        }
        
        #endregion
        
        #region Effects
        
        private void PlayDamageEffects(float damage)
        {
            // Visual effect
            if (damageEffect != null)
            {
                Instantiate(damageEffect, transform.position, Quaternion.identity);
            }
            
            // Audio effect
            if (audioSource != null && damageSound != null)
            {
                audioSource.PlayOneShot(damageSound);
            }
            
            // Screen shake for player
            if (CompareTag("Player"))
            {
                // Could trigger camera shake here
            }
        }
        
        private void PlayHealEffects(float healing)
        {
            // Visual effect
            if (healEffect != null)
            {
                Instantiate(healEffect, transform.position, Quaternion.identity);
            }
            
            // Audio effect
            if (audioSource != null && healSound != null)
            {
                audioSource.PlayOneShot(healSound);
            }
        }
        
        private void PlayDeathEffects()
        {
            // Visual effect
            if (deathEffect != null)
            {
                Instantiate(deathEffect, transform.position, Quaternion.identity);
            }
            
            // Audio effect
            if (audioSource != null && deathSound != null)
            {
                audioSource.PlayOneShot(deathSound);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetMaxHealth(float newMaxHealth)
        {
            float healthPercentage = GetHealthPercentage();
            maxHealth = newMaxHealth;
            currentHealth = maxHealth * healthPercentage;
            
            NotifyHealthChanged();
        }
        
        public void SetInvulnerable(bool invulnerable)
        {
            isInvulnerable = invulnerable;
        }
        
        public void ResetHealth()
        {
            isDead = false;
            currentHealth = maxHealth;
            
            if (hasShield)
            {
                currentShield = maxShield;
            }
            
            isInvulnerable = false;
            
            NotifyHealthChanged();
        }
        
        public bool IsAlive()
        {
            return !isDead;
        }
        
        public bool IsDead()
        {
            return isDead;
        }
        
        public float GetHealthPercentage()
        {
            return maxHealth > 0f ? currentHealth / maxHealth : 0f;
        }
        
        public float GetShieldPercentage()
        {
            return hasShield && maxShield > 0f ? currentShield / maxShield : 0f;
        }
        
        public bool IsAtFullHealth()
        {
            return currentHealth >= maxHealth;
        }
        
        public bool HasShieldActive()
        {
            return hasShield && currentShield > 0f;
        }
        
        #endregion
        
        #region Private Methods
        
        private void NotifyHealthChanged()
        {
            OnHealthChanged?.Invoke(currentHealth);
        }
        
        #endregion
        
        #region Gizmos
        
        private void OnDrawGizmosSelected()
        {
            // Draw health bar above object
            Vector3 barPosition = transform.position + Vector3.up * 2f;
            Vector3 barSize = new Vector3(2f, 0.2f, 0f);
            
            // Background
            Gizmos.color = Color.red;
            Gizmos.DrawCube(barPosition, barSize);
            
            // Health
            if (maxHealth > 0f)
            {
                float healthPercentage = currentHealth / maxHealth;
                Vector3 healthSize = new Vector3(barSize.x * healthPercentage, barSize.y, barSize.z);
                Vector3 healthPosition = barPosition - Vector3.right * (barSize.x - healthSize.x) * 0.5f;
                
                Gizmos.color = Color.green;
                Gizmos.DrawCube(healthPosition, healthSize);
            }
            
            // Shield
            if (hasShield && maxShield > 0f)
            {
                Vector3 shieldBarPosition = barPosition + Vector3.up * 0.3f;
                float shieldPercentage = currentShield / maxShield;
                Vector3 shieldSize = new Vector3(barSize.x * shieldPercentage, barSize.y * 0.5f, barSize.z);
                Vector3 shieldPosition = shieldBarPosition - Vector3.right * (barSize.x - shieldSize.x) * 0.5f;
                
                Gizmos.color = Color.cyan;
                Gizmos.DrawCube(shieldPosition, shieldSize);
            }
        }
        
        #endregion
    }
}
