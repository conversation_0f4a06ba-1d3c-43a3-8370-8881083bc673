using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using System.IO;

namespace ChronoForge.Editor
{
    public class SceneAutoSetup : EditorWindow
    {
        private string sceneName = "GameScene";
        private bool setupCamera = true;
        private bool setupLighting = true;
        private bool setupLayers = true;
        private bool setupPhysics = true;
        private bool createTestLevel = true;

        [MenuItem("ChronoForge/Auto Setup Scene")]
        public static void ShowWindow()
        {
            GetWindow<SceneAutoSetup>("ChronoForge Scene Setup");
        }

        private void OnGUI()
        {
            GUILayout.Label("🎬 ChronoForge Scene Auto-Setup", EditorStyles.boldLabel);
            GUILayout.Space(10);

            sceneName = EditorGUILayout.TextField("Nom de la scène :", sceneName);
            GUILayout.Space(5);

            setupCamera = EditorGUILayout.Toggle("📷 Configurer la caméra", setupCamera);
            setupLighting = EditorGUILayout.Toggle("💡 Configurer l'éclairage", setupLighting);
            setupLayers = EditorGUILayout.Toggle("📋 Configurer les layers", setupLayers);
            setupPhysics = EditorGUILayout.Toggle("⚡ Configurer la physique", setupPhysics);
            createTestLevel = EditorGUILayout.Toggle("🏗️ Créer un niveau de test", createTestLevel);

            GUILayout.Space(20);

            if (GUILayout.Button("🚀 CONFIGURER LA SCÈNE COMPLÈTE", GUILayout.Height(40)))
            {
                SetupCompleteScene();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("🎮 Créer Scène de Test Rapide", GUILayout.Height(30)))
            {
                CreateQuickTestScene();
            }
        }

        private void SetupCompleteScene()
        {
            UnityEngine.UnityEngine.Debug.Log("🚀 Configuration automatique de la scène ChronoForge...");

            // Créer nouvelle scène
            Scene newScene = EditorSceneManager.NewScene(NewSceneSetup.EmptyScene, NewSceneMode.Single);
            
            if (setupLayers)
                SetupLayers();
            
            if (setupPhysics)
                SetupPhysicsSettings();
            
            if (setupCamera)
                SetupMainCamera();
            
            if (setupLighting)
                SetupLighting();

            // Instancier les prefabs principaux
            InstantiatePrefabs();

            if (createTestLevel)
                CreateSimpleTestLevel();

            // Sauvegarder la scène
            string scenePath = $"Assets/Scenes/{sceneName}.unity";
            if (!Directory.Exists("Assets/Scenes"))
                AssetDatabase.CreateFolder("Assets", "Scenes");
            
            EditorSceneManager.SaveScene(newScene, scenePath);
            
            UnityEngine.UnityEngine.Debug.Log($"✅ Scène configurée et sauvegardée : {scenePath}");
            EditorUtility.DisplayDialog("ChronoForge", $"🎉 Scène '{sceneName}' configurée avec succès !\n\nAppuyez sur Play pour tester.", "OK");
        }

        private void SetupLayers()
        {
            UnityEngine.UnityEngine.Debug.Log("📋 Configuration des layers...");
            
            // Note: La configuration des layers doit être faite manuellement dans Project Settings
            // ou via un script plus complexe qui modifie les TagManager
            
            UnityEngine.UnityEngine.Debug.Log("⚠️ Configurez manuellement les layers dans Project Settings :");
            UnityEngine.UnityEngine.Debug.Log("   Layer 8: Player");
            UnityEngine.UnityEngine.Debug.Log("   Layer 9: Enemy");
            UnityEngine.UnityEngine.Debug.Log("   Layer 10: Environment");
            UnityEngine.UnityEngine.Debug.Log("   Layer 11: UI");
        }

        private void SetupPhysicsSettings()
        {
            UnityEngine.UnityEngine.Debug.Log("⚡ Configuration de la physique 2D...");
            
            // Configurer la gravité pour un jeu top-down
            Physics2D.gravity = Vector2.zero;
            
            UnityEngine.UnityEngine.Debug.Log("✅ Gravité 2D désactivée pour le gameplay top-down");
        }

        private void SetupMainCamera()
        {
            UnityEngine.UnityEngine.Debug.Log("📷 Configuration de la caméra principale...");

            GameObject cameraObj = GameObject.FindWithTag("MainCamera");
            if (cameraObj == null)
            {
                cameraObj = new GameObject("Main Camera");
                cameraObj.tag = "MainCamera";
            }

            Camera camera = cameraObj.GetComponent<Camera>();
            if (camera == null)
                camera = cameraObj.AddComponent<Camera>();

            // Configuration pour un jeu 2D
            camera.orthographic = true;
            camera.orthographicSize = 5f;
            camera.backgroundColor = new Color(0.1f, 0.1f, 0.2f, 1f);
            camera.cullingMask = -1; // Tout voir
            
            // Position de la caméra
            cameraObj.transform.position = new Vector3(0, 0, -10);

            // Ajouter AudioListener si pas présent
            if (cameraObj.GetComponent<AudioListener>() == null)
                cameraObj.AddComponent<AudioListener>();

            UnityEngine.UnityEngine.Debug.Log("✅ Caméra configurée pour le gameplay 2D");
        }

        private void SetupLighting()
        {
            UnityEngine.UnityEngine.Debug.Log("💡 Configuration de l'éclairage...");

            // Configurer l'éclairage ambiant
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
            RenderSettings.ambientLight = new Color(0.3f, 0.3f, 0.4f, 1f);

            // Créer une lumière directionnelle
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.color = new Color(1f, 0.95f, 0.8f, 1f);
            light.intensity = 1f;
            lightObj.transform.rotation = Quaternion.Euler(50f, -30f, 0f);

            UnityEngine.UnityEngine.Debug.Log("✅ Éclairage configuré");
        }

        private void InstantiatePrefabs()
        {
            UnityEngine.UnityEngine.Debug.Log("🎮 Instanciation des prefabs principaux...");

            // Charger et instancier GameManager
            GameObject gameManagerPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Core/GameManager.prefab");
            if (gameManagerPrefab != null)
            {
                GameObject gameManager = PrefabUtility.InstantiatePrefab(gameManagerPrefab) as GameObject;
                UnityEngine.UnityEngine.Debug.Log("✅ GameManager instancié");
            }
            else
            {
                UnityEngine.UnityEngine.Debug.LogWarning("⚠️ GameManager prefab non trouvé. Exécutez d'abord le générateur de prefabs.");
            }

            // Charger et instancier Player
            GameObject playerPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Player/Player.prefab");
            if (playerPrefab != null)
            {
                GameObject player = PrefabUtility.InstantiatePrefab(playerPrefab) as GameObject;
                player.transform.position = Vector3.zero;
                UnityEngine.UnityEngine.Debug.Log("✅ Player instancié");
            }

            // Charger et instancier UI Canvas
            GameObject canvasPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/UI/MainCanvas.prefab");
            if (canvasPrefab != null)
            {
                GameObject canvas = PrefabUtility.InstantiatePrefab(canvasPrefab) as GameObject;
                UnityEngine.UnityEngine.Debug.Log("✅ UI Canvas instancié");
            }

            // Charger et instancier AudioManager
            GameObject audioPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/Audio/AudioManager.prefab");
            if (audioPrefab != null)
            {
                GameObject audioManager = PrefabUtility.InstantiatePrefab(audioPrefab) as GameObject;
                UnityEngine.UnityEngine.Debug.Log("✅ AudioManager instancié");
            }
        }

        private void CreateSimpleTestLevel()
        {
            UnityEngine.UnityEngine.Debug.Log("🏗️ Création d'un niveau de test simple...");

            // Créer un sol simple
            GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Cube);
            floor.name = "TestFloor";
            floor.transform.position = new Vector3(0, -3, 0);
            floor.transform.localScale = new Vector3(20, 1, 1);
            
            // Configurer le matériau
            Renderer floorRenderer = floor.GetComponent<Renderer>();
            floorRenderer.material.color = new Color(0.3f, 0.3f, 0.3f);

            // Créer quelques murs
            CreateWall("LeftWall", new Vector3(-10, 0, 0), new Vector3(1, 6, 1));
            CreateWall("RightWall", new Vector3(10, 0, 0), new Vector3(1, 6, 1));
            CreateWall("TopWall", new Vector3(0, 3, 0), new Vector3(20, 1, 1));

            UnityEngine.UnityEngine.Debug.Log("✅ Niveau de test créé");
        }

        private void CreateWall(string name, Vector3 position, Vector3 scale)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = name;
            wall.transform.position = position;
            wall.transform.localScale = scale;
            
            Renderer wallRenderer = wall.GetComponent<Renderer>();
            wallRenderer.material.color = new Color(0.5f, 0.5f, 0.5f);
        }

        private void CreateQuickTestScene()
        {
            sceneName = "QuickTest";
            setupCamera = true;
            setupLighting = true;
            setupLayers = false;
            setupPhysics = true;
            createTestLevel = true;
            
            SetupCompleteScene();
        }
    }
}
