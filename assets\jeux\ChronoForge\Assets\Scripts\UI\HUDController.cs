using UnityEngine;
using UnityEngine.UI;
using TMPro;
using ChronoForge.Player;
using ChronoForge.Combat;
using ChronoForge.Core;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur du HUD principal pour ChronoForge
    /// </summary>
    public class HUDController : MonoBehaviour
    {
        [Header("Health UI")]
        public Slider healthSlider;
        public Slider shieldSlider;
        public TextMeshProUGUI healthText;
        public Image healthFillImage;
        public Image shieldFillImage;
        
        [Header("Combat UI")]
        public Image weaponIcon;
        public TextMeshProUGUI weaponName;
        public Slider specialCooldownSlider;
        public Slider ultimateCooldownSlider;
        public TextMeshProUGUI comboText;
        public GameObject comboContainer;
        
        [Header("Movement UI")]
        public Slider dashCooldownSlider;
        public Image dashIcon;
        
        [Header("Level Info")]
        public TextMeshProUGUI levelText;
        public Slider experienceSlider;
        public TextMeshProUGUI experienceText;
        
        [Header("Run Info")]
        public TextMeshProUG<PERSON> roomCountText;
        public TextMeshProUGUI timeText;
        public TextMeshProUGUI enemiesKilledText;
        
        [Header("Crosshair")]
        public GameObject crosshair;
        public Image crosshairImage;
        public float crosshairDistance = 2f;
        
        [Header("Damage Numbers")]
        public GameObject damageNumberPrefab;
        public Transform damageNumberParent;
        
        [Header("Colors")]
        public Color healthColor = Color.red;
        public Color shieldColor = Color.cyan;
        public Color lowHealthColor = Color.yellow;
        public Color criticalHealthColor = Color.red;
        
        // Components
        private PlayerController player;
        private PlayerCombat playerCombat;
        private PlayerStats playerStats;
        private ChronoForge.Player.HealthSystem playerHealth;
        private WeaponSystem weaponSystem;
        private RunManager runManager;
        private Camera playerCamera;
        
        // State
        private bool isVisible = true;
        private float runStartTime;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            FindComponents();
        }
        
        private void Start()
        {
            runStartTime = Time.time;
        }
        
        #endregion
        
        #region Initialization
        
        public void Initialize()
        {
            FindComponents();
            SetupUI();
            SubscribeToEvents();
            
            Debug.Log("🎮 HUDController initialized");
        }
        
        private void FindComponents()
        {
            // Find player components
            player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                playerCombat = player.GetComponent<PlayerCombat>();
                playerStats = player.GetComponent<PlayerStats>();
                playerHealth = player.GetComponent<ChronoForge.Player.HealthSystem>();
                weaponSystem = player.GetComponent<WeaponSystem>();
            }
            
            // Find managers
            runManager = FindObjectOfType<RunManager>();
            
            // Find camera
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindObjectOfType<Camera>();
        }
        
        private void SetupUI()
        {
            // Setup health colors
            if (healthFillImage != null)
                healthFillImage.color = healthColor;
            
            if (shieldFillImage != null)
                shieldFillImage.color = shieldColor;
            
            // Setup initial values
            UpdateHealthUI();
            UpdateCombatUI();
            UpdateMovementUI();
            UpdateLevelUI();
            UpdateRunInfo();
            
            // Setup crosshair
            SetupCrosshair();
        }
        
        private void SetupCrosshair()
        {
            if (crosshair != null && player != null)
            {
                crosshair.SetActive(true);
            }
        }
        
        #endregion
        
        #region Event Subscription
        
        private void SubscribeToEvents()
        {
            // Health events
            if (playerHealth != null)
            {
                playerHealth.OnHealthChanged += UpdateHealthDisplay;
                playerHealth.OnShieldChanged += UpdateShieldDisplay;
            }
            
            // Combat events
            if (playerCombat != null)
            {
                PlayerCombat.OnComboChanged += UpdateComboDisplay;
            }
            
            // Weapon events
            if (weaponSystem != null)
            {
                WeaponSystem.OnWeaponChanged += UpdateWeaponDisplay;
            }
            
            // Stats events
            if (playerStats != null)
            {
                PlayerStats.OnRunLevelUp += UpdateLevelDisplay;
            }
            
            // Run events
            if (runManager != null)
            {
                RunManager.OnEnemyKilled += UpdateEnemyCount;
                RunManager.OnRoomChanged += UpdateRoomCount;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (playerHealth != null)
            {
                playerHealth.OnHealthChanged -= UpdateHealthDisplay;
                playerHealth.OnShieldChanged -= UpdateShieldDisplay;
            }
            
            PlayerCombat.OnComboChanged -= UpdateComboDisplay;
            WeaponSystem.OnWeaponChanged -= UpdateWeaponDisplay;
            PlayerStats.OnRunLevelUp -= UpdateLevelDisplay;
            RunManager.OnEnemyKilled -= UpdateEnemyCount;
            RunManager.OnRoomChanged -= UpdateRoomCount;
        }
        
        #endregion
        
        #region Update Methods
        
        public void UpdateHUD()
        {
            if (!isVisible) return;
            
            UpdateHealthUI();
            UpdateCombatUI();
            UpdateMovementUI();
            UpdateLevelUI();
            UpdateRunInfo();
            UpdateCrosshair();
        }
        
        private void UpdateHealthUI()
        {
            if (playerHealth == null) return;
            
            // Update health slider
            if (healthSlider != null)
            {
                healthSlider.value = playerHealth.GetHealthPercentage();
            }
            
            // Update health text
            if (healthText != null)
            {
                healthText.text = $"{playerHealth.currentHealth:F0}/{playerHealth.maxHealth:F0}";
            }
            
            // Update health color based on percentage
            UpdateHealthColor();
            
            // Update shield
            if (shieldSlider != null && playerHealth.hasShield)
            {
                shieldSlider.gameObject.SetActive(true);
                shieldSlider.value = playerHealth.GetShieldPercentage();
            }
            else if (shieldSlider != null)
            {
                shieldSlider.gameObject.SetActive(false);
            }
        }
        
        private void UpdateHealthColor()
        {
            if (healthFillImage == null || playerHealth == null) return;
            
            float healthPercentage = playerHealth.GetHealthPercentage();
            
            Color targetColor;
            if (healthPercentage > 0.5f)
                targetColor = healthColor;
            else if (healthPercentage > 0.25f)
                targetColor = lowHealthColor;
            else
                targetColor = criticalHealthColor;
            
            healthFillImage.color = targetColor;
        }
        
        private void UpdateCombatUI()
        {
            if (playerCombat == null) return;
            
            // Update special cooldown
            if (specialCooldownSlider != null)
            {
                specialCooldownSlider.value = playerCombat.GetSpecialCooldownProgress();
            }
            
            // Update ultimate cooldown
            if (ultimateCooldownSlider != null)
            {
                ultimateCooldownSlider.value = playerCombat.GetUltimateCooldownProgress();
            }
            
            // Update combo display
            UpdateComboUI();
        }
        
        private void UpdateComboUI()
        {
            if (playerCombat == null) return;
            
            int currentCombo = playerCombat.GetCurrentCombo();
            
            if (comboContainer != null)
            {
                comboContainer.SetActive(currentCombo > 0);
            }
            
            if (comboText != null && currentCombo > 0)
            {
                comboText.text = $"COMBO x{currentCombo}";
                
                // Animate combo text
                float scale = 1f + (currentCombo - 1) * 0.1f;
                comboText.transform.localScale = Vector3.one * scale;
            }
        }
        
        private void UpdateMovementUI()
        {
            if (player == null) return;
            
            // Update dash cooldown
            if (dashCooldownSlider != null)
            {
                float dashProgress = player.GetDashCooldownProgress();
                dashCooldownSlider.value = dashProgress;
                
                // Update dash icon color
                if (dashIcon != null)
                {
                    dashIcon.color = dashProgress >= 1f ? Color.white : Color.gray;
                }
            }
        }
        
        private void UpdateLevelUI()
        {
            if (playerStats == null) return;
            
            // Update level text
            if (levelText != null)
            {
                levelText.text = $"Level {playerStats.runLevel}";
            }
            
            // Update experience slider
            if (experienceSlider != null)
            {
                float expProgress = (float)playerStats.runExperience / playerStats.experienceToNextLevel;
                experienceSlider.value = expProgress;
            }
            
            // Update experience text
            if (experienceText != null)
            {
                experienceText.text = $"{playerStats.runExperience}/{playerStats.experienceToNextLevel}";
            }
        }
        
        private void UpdateRunInfo()
        {
            // Update room count
            if (roomCountText != null && runManager != null)
            {
                roomCountText.text = $"Room {runManager.currentRoom}/{runManager.maxRooms}";
            }
            
            // Update time
            if (timeText != null)
            {
                float runTime = Time.time - runStartTime;
                int minutes = Mathf.FloorToInt(runTime / 60f);
                int seconds = Mathf.FloorToInt(runTime % 60f);
                timeText.text = $"{minutes:00}:{seconds:00}";
            }
            
            // Update enemies killed
            if (enemiesKilledText != null && runManager != null)
            {
                enemiesKilledText.text = $"Enemies: {runManager.enemiesKilled}";
            }
        }
        
        private void UpdateCrosshair()
        {
            if (crosshair == null || player == null || playerCamera == null) return;
            
            // Position crosshair based on player facing direction
            Vector3 playerPos = player.transform.position;
            Vector2 moveDirection = player.GetMoveDirection();
            
            Vector3 crosshairWorldPos = playerPos + (Vector3)moveDirection * crosshairDistance;
            Vector3 crosshairScreenPos = playerCamera.WorldToScreenPoint(crosshairWorldPos);
            
            crosshair.transform.position = crosshairScreenPos;
            
            // Update crosshair color based on target
            if (crosshairImage != null && playerCombat != null)
            {
                Transform target = playerCombat.GetCurrentTarget();
                crosshairImage.color = target != null ? Color.red : Color.white;
            }
        }
        
        #endregion
        
        #region Event Handlers
        
        private void UpdateHealthDisplay(float newHealth)
        {
            // Health is updated in UpdateHealthUI()
        }
        
        private void UpdateShieldDisplay(float newShield)
        {
            // Shield is updated in UpdateHealthUI()
        }
        
        private void UpdateComboDisplay(int newCombo)
        {
            // Combo is updated in UpdateComboUI()
        }
        
        private void UpdateWeaponDisplay(WeaponData newWeapon)
        {
            if (weaponIcon != null && newWeapon.weaponIcon != null)
            {
                weaponIcon.sprite = newWeapon.weaponIcon;
            }
            
            if (weaponName != null)
            {
                weaponName.text = newWeapon.weaponName;
            }
        }
        
        private void UpdateLevelDisplay(int newLevel)
        {
            // Level is updated in UpdateLevelUI()
            
            // Show level up effect
            ShowLevelUpEffect();
        }
        
        private void UpdateEnemyCount()
        {
            // Enemy count is updated in UpdateRunInfo()
        }
        
        private void UpdateRoomCount(int newRoom)
        {
            // Room count is updated in UpdateRunInfo()
        }
        
        #endregion
        
        #region Visual Effects
        
        private void ShowLevelUpEffect()
        {
            // Create level up visual effect
            if (levelText != null)
            {
                StartCoroutine(LevelUpAnimation());
            }
        }
        
        private System.Collections.IEnumerator LevelUpAnimation()
        {
            Vector3 originalScale = levelText.transform.localScale;
            Color originalColor = levelText.color;
            
            // Scale up and change color
            float duration = 0.5f;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                
                float scale = Mathf.Lerp(1f, 1.5f, Mathf.Sin(progress * Mathf.PI));
                levelText.transform.localScale = originalScale * scale;
                
                levelText.color = Color.Lerp(originalColor, Color.yellow, Mathf.Sin(progress * Mathf.PI));
                
                yield return null;
            }
            
            // Reset
            levelText.transform.localScale = originalScale;
            levelText.color = originalColor;
        }
        
        public void ShowDamageNumber(Vector3 worldPosition, float damage, bool isCritical = false)
        {
            if (damageNumberPrefab == null || playerCamera == null) return;
            
            // Convert world position to screen position
            Vector3 screenPosition = playerCamera.WorldToScreenPoint(worldPosition);
            
            // Create damage number
            GameObject damageNumber = Instantiate(damageNumberPrefab, damageNumberParent);
            damageNumber.transform.position = screenPosition;
            
            // Setup damage number
            TextMeshProUGUI damageText = damageNumber.GetComponent<TextMeshProUGUI>();
            if (damageText != null)
            {
                damageText.text = damage.ToString("F0");
                damageText.color = isCritical ? Color.yellow : Color.white;
                damageText.fontSize = isCritical ? 24f : 18f;
            }
            
            // Animate damage number
            StartCoroutine(AnimateDamageNumber(damageNumber));
        }
        
        private System.Collections.IEnumerator AnimateDamageNumber(GameObject damageNumber)
        {
            float duration = 1f;
            float elapsed = 0f;
            Vector3 startPos = damageNumber.transform.position;
            Vector3 endPos = startPos + Vector3.up * 50f;
            
            TextMeshProUGUI text = damageNumber.GetComponent<TextMeshProUGUI>();
            Color startColor = text.color;
            Color endColor = new Color(startColor.r, startColor.g, startColor.b, 0f);
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                
                damageNumber.transform.position = Vector3.Lerp(startPos, endPos, progress);
                text.color = Color.Lerp(startColor, endColor, progress);
                
                yield return null;
            }
            
            Destroy(damageNumber);
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            gameObject.SetActive(visible);
        }
        
        public void ShowLowHealthWarning()
        {
            // Flash health bar or show warning
            StartCoroutine(FlashHealthBar());
        }
        
        private System.Collections.IEnumerator FlashHealthBar()
        {
            if (healthFillImage == null) yield break;
            
            Color originalColor = healthFillImage.color;
            
            for (int i = 0; i < 3; i++)
            {
                healthFillImage.color = Color.white;
                yield return new WaitForSeconds(0.1f);
                healthFillImage.color = originalColor;
                yield return new WaitForSeconds(0.1f);
            }
        }
        
        public void SetCrosshairVisible(bool visible)
        {
            if (crosshair != null)
                crosshair.SetActive(visible);
        }
        
        #endregion
    }
}
