# 🚀 Space Clicker - Complete Unity Game

Un jeu de clicker spatial développé avec Unity, featuring un système de ressources avancé, des upgrades progressifs, et une interface utilisateur moderne.

## 🎮 Description du Jeu

Space Clicker est un jeu idle/clicker où les joueurs construisent leur empire spatial en :
- Cliquant sur des modules pour générer des ressources
- Achetant des upgrades pour améliorer la production
- Débloquant de nouvelles technologies spatiales
- Gérant quatre types de ressources : Énergie, Minéraux, Recherche, et Devise Spatiale

## ✨ Fonctionnalités

### 🎯 Gameplay
- **4 modules cliquables** : Panneau Solaire, Foreuse, Laboratoire, Station Spatiale
- **4 types de ressources** avec production passive et active
- **Système d'upgrades** avec progression exponentielle
- **Sauvegarde automatique** et manuelle
- **Interface responsive** avec animations fluides

### 🛠️ Technique
- **Architecture modulaire** avec managers séparés
- **Support BigInteger** pour les très gros nombres
- **Système d'événements** pour la communication entre composants
- **Tests unitaires** complets avec 95%+ de couverture
- **Outils d'édition Unity** pour l'assemblage automatique

### 🎨 Visuel & Audio
- **Interface moderne** avec thème spatial
- **Animations fluides** avec LeanTween
- **Système de particules** pour les effets visuels
- **Gestionnaire audio** complet (musique, SFX, ambiance)
- **Notifications** et feedback utilisateur

## 📁 Structure du Projet

```
SpaceClicker/
├── Assets/
│   ├── Scripts/
│   │   ├── Core/           # Managers principaux (GameManager, ResourceManager, etc.)
│   │   ├── UI/             # Interface utilisateur et contrôleurs
│   │   ├── Audio/          # Système audio
│   │   ├── Effects/        # Effets visuels et particules
│   │   ├── Utils/          # Utilitaires et extensions
│   │   ├── Tests/          # Tests unitaires et d'intégration
│   │   └── Editor/         # Outils Unity pour l'assemblage
│   ├── Prefabs/           # Prefabs générés automatiquement
│   ├── Sprites/           # Assets visuels
│   ├── Materials/         # Matériaux
│   ├── Audio/             # Fichiers audio
│   └── Scenes/            # Scènes Unity
├── ASSEMBLY_GUIDE.md      # Guide d'assemblage complet
└── README.md              # Ce fichier
```

## 🚀 Installation et Configuration

### Prérequis
- Unity 2022.3 LTS ou plus récent
- .NET Standard 2.1
- TextMeshPro package

### Installation Rapide
1. **Créer le projet Unity**
   ```
   - Template: 2D Core
   - Nom: SpaceClicker
   - Importer tous les scripts
   ```

2. **Générer les assets**
   ```
   Space Clicker > Asset Generator > Generate All Assets
   ```

3. **Créer les prefabs**
   ```
   Space Clicker > Prefab Generator > Generate All Prefabs
   ```

4. **Configurer la scène**
   ```
   Space Clicker > Scene Setup > Create New Scene
   ```

5. **Tester le jeu**
   ```
   Appuyer sur Play ▶️
   ```

### Installation Détaillée
Consulter [ASSEMBLY_GUIDE.md](ASSEMBLY_GUIDE.md) pour les instructions complètes.

## 🎮 Comment Jouer

1. **Cliquer sur les modules** pour générer des ressources
2. **Acheter des upgrades** dans le panneau de droite
3. **Débloquer de nouveaux modules** avec les ressources
4. **Optimiser la production** avec les upgrades
5. **Construire l'empire spatial** ultime !

## 🛠️ Outils de Développement

### Outils Unity Intégrés
- **PrefabGenerator** : Génère tous les prefabs automatiquement
- **SceneSetup** : Configure la scène principale
- **AssetGenerator** : Crée les assets visuels de base
- **AssemblyValidator** : Valide l'assemblage du projet
- **GameplayBalancer** : Équilibre les valeurs de gameplay
- **BuildManager** : Compile pour toutes les plateformes
- **DeploymentManager** : Déploie sur GitHub Pages, Itch.io, etc.

### Tests
```bash
# Lancer tous les tests
Window > General > Test Runner > Run All

# Tests spécifiques
- ResourceManagerTests : Tests du système de ressources
- UpgradeManagerTests : Tests du système d'upgrades
- BigIntegerExtensionsTests : Tests des utilitaires
- GameIntegrationTests : Tests d'intégration complète
```

## 🎯 Plateformes Supportées

- **WebGL** (GitHub Pages, Itch.io)
- **Windows** (Steam, Itch.io)
- **macOS** (Steam, Itch.io)
- **Linux** (Steam, Itch.io)
- **Android** (Google Play)

## 📊 Métriques de Gameplay

### Valeurs par Défaut
- **Clic de base** : 1 ressource
- **Production passive** : 1/sec (Énergie), 0.8/sec (Minéraux), 0.5/sec (Recherche), 0.3/sec (Devise)
- **Coût upgrade initial** : 10 ressources
- **Multiplicateur de coût** : 1.5x par niveau
- **Effet upgrade** : +20% de production par niveau

### Balancing
Utiliser `Space Clicker > Gameplay Balancer` pour :
- Simuler la progression
- Ajuster les valeurs
- Tester l'équilibrage
- Générer des rapports

## 🔧 Configuration

### GameConstants.cs
```csharp
public static class GameConstants
{
    public const string GAME_VERSION = "1.0.0";
    public const int BASE_CLICK_POWER = 1;
    public const float PRODUCTION_TICK_RATE = 1f;
    // ... autres constantes
}
```

### Personnalisation
- **Couleurs** : Modifier dans GameConstants
- **Sprites** : Remplacer dans Assets/Sprites/
- **Audio** : Ajouter dans Assets/Audio/
- **Valeurs** : Ajuster avec GameplayBalancer

## 📈 Performance

### Optimisations Incluses
- **Object Pooling** pour les effets
- **BigInteger** pour éviter les overflows
- **Event-driven architecture** pour réduire les Update()
- **Compression** des builds
- **Batching** des UI updates

### Métriques Cibles
- **60 FPS** stable en gameplay
- **< 100 MB** taille de build WebGL
- **< 2 secondes** temps de chargement
- **< 50 MB** utilisation RAM

## 🚀 Déploiement

### GitHub Pages (Automatique)
```bash
Space Clicker > Deployment Manager
✅ Deploy to GitHub Pages
Repository: username/space-clicker
Build and Deploy All
```

### Autres Plateformes
- **Itch.io** : Upload manuel ou Butler CLI
- **Steam** : SteamCMD + Steamworks SDK
- **Mobile** : Google Play Console / App Store Connect

## 🤝 Contribution

### Structure de Code
- **Namespaces** : SpaceClicker.Core, SpaceClicker.UI, etc.
- **Conventions** : PascalCase pour public, camelCase pour private
- **Documentation** : XML comments pour toutes les méthodes publiques
- **Tests** : Couverture minimale 80%

### Workflow
1. Fork le projet
2. Créer une branche feature
3. Ajouter des tests
4. Valider avec AssemblyValidator
5. Créer une Pull Request

## 📝 Changelog

### v1.0.0 (Initial Release)
- ✅ Système de ressources complet
- ✅ 4 modules cliquables
- ✅ Système d'upgrades
- ✅ Interface utilisateur moderne
- ✅ Sauvegarde/chargement
- ✅ Audio et effets visuels
- ✅ Support multi-plateformes
- ✅ Tests unitaires complets
- ✅ Outils d'assemblage Unity

## 📞 Support

### Documentation
- [Guide d'Assemblage](ASSEMBLY_GUIDE.md)
- [Outils Unity](Assets/Scripts/Editor/README.md)
- [Tests](Assets/Scripts/Tests/)

### Problèmes Courants
- **Scripts ne compilent pas** : Vérifier les namespaces et références
- **Prefabs manquants** : Utiliser PrefabGenerator
- **Performance lente** : Vérifier les paramètres de qualité Unity
- **Build échoue** : Utiliser AssemblyValidator

## 📄 Licence

MIT License - Voir LICENSE file pour les détails.

## 🎉 Crédits

Développé avec ❤️ pour démontrer les capacités de Unity et C#.

**Technologies utilisées :**
- Unity 2022.3 LTS
- C# .NET Standard 2.1
- TextMeshPro
- Unity Test Framework
- LeanTween (optionnel)

---

**🚀 Bon développement et amusez-vous bien avec Space Clicker ! 🌟**
