using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using ChronoForge.Procedural;
using ChronoForge.Player;
using ChronoForge.Core;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur de minimap pour ChronoForge
    /// </summary>
    public class MinimapController : MonoBehaviour
    {
        [Header("Minimap Settings")]
        public RectTransform minimapContainer;
        public Image minimapBackground;
        public float minimapSize = 200f;
        public float roomIconSize = 20f;
        
        [Header("Room Icons")]
        public GameObject roomIconPrefab;
        public Sprite startRoomIcon;
        public Sprite combatRoomIcon;
        public Sprite bossRoomIcon;
        public Sprite treasureRoomIcon;
        public Sprite shopRoomIcon;
        public Sprite restRoomIcon;
        public Sprite secretRoomIcon;
        public Sprite unknownRoomIcon;
        
        [Header("Player Icon")]
        public GameObject playerIconPrefab;
        public Image playerIcon;
        public float playerIconSize = 15f;
        
        [Header("Colors")]
        public Color visitedRoomColor = Color.white;
        public Color unvisitedRoomColor = Color.gray;
        public Color currentRoomColor = Color.yellow;
        public Color clearedRoomColor = Color.green;
        public Color connectionColor = Color.white;
        
        [Header("Animation")]
        public bool animatePlayerMovement = true;
        public float playerMoveSpeed = 5f;
        public bool pulseCurrentRoom = true;
        public float pulseSpeed = 2f;
        
        // Components
        private LevelGenerator levelGenerator;
        private PlayerController player;
        private RunManager runManager;
        
        // Minimap data
        private Dictionary<Vector2Int, GameObject> roomIcons = new Dictionary<Vector2Int, GameObject>();
        private Dictionary<Vector2Int, List<LineRenderer>> roomConnections = new Dictionary<Vector2Int, List<LineRenderer>>();
        private LevelData currentLevel;
        private Vector2Int currentPlayerRoom;
        
        // State
        private bool isVisible = true;
        private bool isInitialized = false;
        
        #region Unity Lifecycle
        
        private void Update()
        {
            if (isInitialized && isVisible)
            {
                UpdateMinimap();
            }
        }
        
        #endregion
        
        #region Initialization
        
        public void Initialize()
        {
            FindComponents();
            SetupMinimap();
            SubscribeToEvents();
            
            UnityEngine.Debug.Log("🗺️ MinimapController initialized");
        }
        
        private void FindComponents()
        {
            levelGenerator = FindFirstObjectByType<LevelGenerator>();
            player = FindFirstObjectByType<PlayerController>();
            runManager = FindFirstObjectByType<RunManager>();
            
            // Setup minimap container if not assigned
            if (minimapContainer == null)
            {
                minimapContainer = GetComponent<RectTransform>();
            }
            
            // Setup background if not assigned
            if (minimapBackground == null)
            {
                minimapBackground = GetComponent<Image>();
            }
        }
        
        private void SetupMinimap()
        {
            // Create default prefabs if not assigned
            CreateDefaultPrefabs();
            
            // Setup minimap container
            if (minimapContainer != null)
            {
                minimapContainer.sizeDelta = Vector2.one * minimapSize;
            }
            
            // Setup background
            if (minimapBackground != null)
            {
                minimapBackground.color = new Color(0, 0, 0, 0.7f);
            }
            
            isInitialized = true;
        }
        
        private void CreateDefaultPrefabs()
        {
            // Create room icon prefab if not assigned
            if (roomIconPrefab == null)
            {
                roomIconPrefab = CreateDefaultRoomIcon();
            }
            
            // Create player icon prefab if not assigned
            if (playerIconPrefab == null)
            {
                playerIconPrefab = CreateDefaultPlayerIcon();
            }
            
            // Create default room icons if not assigned
            CreateDefaultRoomIcons();
        }
        
        private GameObject CreateDefaultRoomIcon()
        {
            GameObject icon = new GameObject("RoomIcon");
            
            Image image = icon.AddComponent<Image>();
            image.sprite = CreateDefaultSprite(Color.white);
            
            RectTransform rect = icon.GetComponent<RectTransform>();
            rect.sizeDelta = Vector2.one * roomIconSize;
            
            return icon;
        }
        
        private GameObject CreateDefaultPlayerIcon()
        {
            GameObject icon = new GameObject("PlayerIcon");
            
            Image image = icon.AddComponent<Image>();
            image.sprite = CreateDefaultSprite(Color.blue);
            image.color = Color.blue;
            
            RectTransform rect = icon.GetComponent<RectTransform>();
            rect.sizeDelta = Vector2.one * playerIconSize;
            
            return icon;
        }
        
        private void CreateDefaultRoomIcons()
        {
            if (startRoomIcon == null)
                startRoomIcon = CreateDefaultSprite(Color.green);
            
            if (combatRoomIcon == null)
                combatRoomIcon = CreateDefaultSprite(Color.yellow);
            
            if (bossRoomIcon == null)
                bossRoomIcon = CreateDefaultSprite(Color.red);
            
            if (treasureRoomIcon == null)
                treasureRoomIcon = CreateDefaultSprite(Color.cyan);
            
            if (shopRoomIcon == null)
                shopRoomIcon = CreateDefaultSprite(Color.magenta);
            
            if (restRoomIcon == null)
                restRoomIcon = CreateDefaultSprite(Color.blue);
            
            if (secretRoomIcon == null)
                secretRoomIcon = CreateDefaultSprite(Color.white);
            
            if (unknownRoomIcon == null)
                unknownRoomIcon = CreateDefaultSprite(Color.gray);
        }
        
        private Sprite CreateDefaultSprite(Color color)
        {
            // Create a simple colored square sprite
            Texture2D texture = new Texture2D(32, 32);
            Color[] pixels = new Color[32 * 32];
            
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = color;
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), Vector2.one * 0.5f);
        }
        
        #endregion
        
        #region Event Subscription
        
        private void SubscribeToEvents()
        {
            // Level generation events
            if (levelGenerator != null)
            {
                LevelGenerator.OnLevelGenerated += HandleLevelGenerated;
                LevelGenerator.OnRoomEntered += HandleRoomEntered;
                LevelGenerator.OnRoomCleared += HandleRoomCleared;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            LevelGenerator.OnLevelGenerated -= HandleLevelGenerated;
            LevelGenerator.OnRoomEntered -= HandleRoomEntered;
            LevelGenerator.OnRoomCleared -= HandleRoomCleared;
        }
        
        #endregion
        
        #region Minimap Generation
        
        private void HandleLevelGenerated(LevelData levelData)
        {
            currentLevel = levelData;
            GenerateMinimap();
        }
        
        private void GenerateMinimap()
        {
            if (currentLevel == null) return;
            
            // Clear existing minimap
            ClearMinimap();
            
            // Generate room icons
            GenerateRoomIcons();
            
            // Generate connections
            GenerateRoomConnections();
            
            // Create player icon
            CreatePlayerIcon();
            
            UnityEngine.Debug.Log("🗺️ Minimap generated");
        }
        
        private void ClearMinimap()
        {
            // Clear room icons
            foreach (var icon in roomIcons.Values)
            {
                if (icon != null)
                    Destroy(icon);
            }
            roomIcons.Clear();
            
            // Clear connections
            foreach (var connectionList in roomConnections.Values)
            {
                foreach (var connection in connectionList)
                {
                    if (connection != null)
                        Destroy(connection.gameObject);
                }
            }
            roomConnections.Clear();
            
            // Clear player icon
            if (playerIcon != null && playerIcon.gameObject != null)
            {
                Destroy(playerIcon.gameObject);
            }
        }
        
        private void GenerateRoomIcons()
        {
            foreach (var roomPair in currentLevel.rooms)
            {
                Vector2Int gridPos = roomPair.Key;
                RoomData roomData = roomPair.Value;
                
                CreateRoomIcon(gridPos, roomData);
            }
        }
        
        private void CreateRoomIcon(Vector2Int gridPos, RoomData roomData)
        {
            // Instantiate room icon
            GameObject iconObj = Instantiate(roomIconPrefab, minimapContainer);
            
            // Setup icon
            Image iconImage = iconObj.GetComponent<Image>();
            if (iconImage != null)
            {
                iconImage.sprite = GetRoomIcon(roomData.roomType);
                iconImage.color = GetRoomColor(roomData);
            }
            
            // Position icon
            RectTransform iconRect = iconObj.GetComponent<RectTransform>();
            Vector2 minimapPos = GridToMinimapPosition(gridPos);
            iconRect.anchoredPosition = minimapPos;
            
            // Store reference
            roomIcons[gridPos] = iconObj;
            
            // Add room icon component
            MinimapRoomIcon roomIconComponent = iconObj.GetComponent<MinimapRoomIcon>();
            if (roomIconComponent == null)
                roomIconComponent = iconObj.AddComponent<MinimapRoomIcon>();
            
            roomIconComponent.Initialize(gridPos, roomData);
        }
        
        private Sprite GetRoomIcon(RoomType roomType)
        {
            switch (roomType)
            {
                case RoomType.Start:
                    return startRoomIcon;
                case RoomType.Combat:
                    return combatRoomIcon;
                case RoomType.Boss:
                    return bossRoomIcon;
                case RoomType.Treasure:
                    return treasureRoomIcon;
                case RoomType.Shop:
                    return shopRoomIcon;
                case RoomType.Rest:
                    return restRoomIcon;
                case RoomType.Secret:
                    return secretRoomIcon;
                default:
                    return unknownRoomIcon;
            }
        }
        
        private Color GetRoomColor(RoomData roomData)
        {
            if (roomData.isCleared)
                return clearedRoomColor;
            else if (roomData.isVisited)
                return visitedRoomColor;
            else
                return unvisitedRoomColor;
        }
        
        private void GenerateRoomConnections()
        {
            foreach (var roomPair in currentLevel.rooms)
            {
                Vector2Int gridPos = roomPair.Key;
                RoomData roomData = roomPair.Value;
                
                CreateRoomConnections(gridPos, roomData);
            }
        }
        
        private void CreateRoomConnections(Vector2Int gridPos, RoomData roomData)
        {
            List<LineRenderer> connections = new List<LineRenderer>();
            
            foreach (var connection in roomData.connections)
            {
                Vector2Int connectedRoomPos = gridPos + connection;
                
                if (currentLevel.rooms.ContainsKey(connectedRoomPos))
                {
                    LineRenderer line = CreateConnectionLine(gridPos, connectedRoomPos);
                    connections.Add(line);
                }
            }
            
            roomConnections[gridPos] = connections;
        }
        
        private LineRenderer CreateConnectionLine(Vector2Int from, Vector2Int to)
        {
            GameObject lineObj = new GameObject($"Connection_{from}_{to}");
            lineObj.transform.SetParent(minimapContainer);
            
            LineRenderer line = lineObj.AddComponent<LineRenderer>();
            line.material = new Material(Shader.Find("Sprites/Default"));
            line.material.color = connectionColor;
            line.startWidth = 2f;
            line.endWidth = 2f;
            line.positionCount = 2;
            line.useWorldSpace = false;
            
            Vector2 fromPos = GridToMinimapPosition(from);
            Vector2 toPos = GridToMinimapPosition(to);
            
            line.SetPosition(0, fromPos);
            line.SetPosition(1, toPos);
            
            return line;
        }
        
        private void CreatePlayerIcon()
        {
            if (playerIconPrefab == null) return;
            
            GameObject playerIconObj = Instantiate(playerIconPrefab, minimapContainer);
            playerIcon = playerIconObj.GetComponent<Image>();
            
            // Position at current room
            if (levelGenerator != null)
            {
                RoomData currentRoom = levelGenerator.GetCurrentRoom();
                if (currentRoom != null)
                {
                    currentPlayerRoom = currentRoom.gridPosition;
                    UpdatePlayerIconPosition();
                }
            }
        }
        
        #endregion
        
        #region Update Methods
        
        public void UpdateMinimap()
        {
            UpdateRoomStates();
            UpdatePlayerPosition();
            UpdateAnimations();
        }
        
        private void UpdateRoomStates()
        {
            if (currentLevel == null) return;
            
            foreach (var roomPair in currentLevel.rooms)
            {
                Vector2Int gridPos = roomPair.Key;
                RoomData roomData = roomPair.Value;
                
                if (roomIcons.ContainsKey(gridPos))
                {
                    GameObject iconObj = roomIcons[gridPos];
                    Image iconImage = iconObj.GetComponent<Image>();
                    
                    if (iconImage != null)
                    {
                        iconImage.color = GetRoomColor(roomData);
                    }
                }
            }
        }
        
        private void UpdatePlayerPosition()
        {
            if (levelGenerator == null || playerIcon == null) return;
            
            RoomData currentRoom = levelGenerator.GetCurrentRoom();
            if (currentRoom != null && currentRoom.gridPosition != currentPlayerRoom)
            {
                currentPlayerRoom = currentRoom.gridPosition;
                UpdatePlayerIconPosition();
            }
        }
        
        private void UpdatePlayerIconPosition()
        {
            if (playerIcon == null) return;
            
            Vector2 targetPosition = GridToMinimapPosition(currentPlayerRoom);
            
            if (animatePlayerMovement)
            {
                // Smooth movement
                RectTransform playerRect = playerIcon.GetComponent<RectTransform>();
                Vector2 currentPos = playerRect.anchoredPosition;
                Vector2 newPos = Vector2.MoveTowards(currentPos, targetPosition, playerMoveSpeed * Time.deltaTime);
                playerRect.anchoredPosition = newPos;
            }
            else
            {
                // Instant movement
                RectTransform playerRect = playerIcon.GetComponent<RectTransform>();
                playerRect.anchoredPosition = targetPosition;
            }
        }
        
        private void UpdateAnimations()
        {
            // Pulse current room
            if (pulseCurrentRoom && roomIcons.ContainsKey(currentPlayerRoom))
            {
                GameObject currentRoomIcon = roomIcons[currentPlayerRoom];
                if (currentRoomIcon != null)
                {
                    float pulse = 1f + 0.2f * Mathf.Sin(Time.time * pulseSpeed);
                    currentRoomIcon.transform.localScale = Vector3.one * pulse;
                }
            }
        }
        
        #endregion
        
        #region Event Handlers
        
        private void HandleRoomEntered(RoomData roomData)
        {
            currentPlayerRoom = roomData.gridPosition;
            UpdatePlayerIconPosition();
        }
        
        private void HandleRoomCleared(RoomData roomData)
        {
            // Room color will be updated in UpdateRoomStates
        }
        
        #endregion
        
        #region Utility Methods
        
        private Vector2 GridToMinimapPosition(Vector2Int gridPos)
        {
            if (currentLevel == null) return Vector2.zero;
            
            // Convert grid position to minimap position
            Vector2 normalizedPos = new Vector2(
                (float)gridPos.x / (currentLevel.gridSize.x - 1),
                (float)gridPos.y / (currentLevel.gridSize.y - 1)
            );
            
            // Center around origin
            normalizedPos -= Vector2.one * 0.5f;
            
            // Scale to minimap size
            return normalizedPos * (minimapSize - roomIconSize);
        }
        
        #endregion
        
        #region Public Methods
        
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            gameObject.SetActive(visible);
        }
        
        public void RefreshMinimap()
        {
            if (currentLevel != null)
            {
                GenerateMinimap();
            }
        }
        
        public void SetMinimapSize(float size)
        {
            minimapSize = size;
            if (minimapContainer != null)
            {
                minimapContainer.sizeDelta = Vector2.one * minimapSize;
            }
            RefreshMinimap();
        }
        
        #endregion
    }
    
    /// <summary>
    /// Composant pour les icônes de salle sur la minimap
    /// </summary>
    public class MinimapRoomIcon : MonoBehaviour
    {
        public Vector2Int gridPosition;
        public RoomData roomData;
        
        public void Initialize(Vector2Int pos, RoomData data)
        {
            gridPosition = pos;
            roomData = data;
        }
        
        // Could add click functionality here for room selection
        public void OnRoomClicked()
        {
            UnityEngine.Debug.Log($"Clicked room at {gridPosition}: {roomData.roomType}");
        }
    }
}
