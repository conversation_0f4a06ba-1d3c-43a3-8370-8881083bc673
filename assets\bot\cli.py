#!/usr/bin/env python3
"""
Interface en ligne de commande pour le bot de publication automatisé.
"""

import click
import sys
import json
from datetime import datetime
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import ConfigManager, setup_logging, validate_environment
from src.content_manager import ContentManager
from src.publisher import Publisher
from src.scheduler import BotScheduler

@click.group()
@click.option('--config', default='config.yaml', help='Fichier de configuration')
@click.option('--verbose', '-v', is_flag=True, help='Mode verbeux')
@click.pass_context
def cli(ctx, config, verbose):
    """Bot de publication automatisé pour réseaux sociaux."""
    ctx.ensure_object(dict)
    
    # Charger la configuration
    config_manager = ConfigManager(config)
    ctx.obj['config'] = config_manager
    
    # Configurer le logging
    if verbose:
        import os
        os.environ['LOG_LEVEL'] = 'DEBUG'
    
    setup_logging(config_manager)
    
    # Valider l'environnement
    if not validate_environment():
        click.echo("❌ Erreur de configuration de l'environnement", err=True)
        sys.exit(1)

@cli.command()
@click.pass_context
def status(ctx):
    """Affiche le statut du bot et des plateformes."""
    config = ctx.obj['config']
    
    click.echo("🤖 Statut du Bot de Publication")
    click.echo("=" * 40)
    
    # Statut de la configuration
    click.echo(f"📁 Fichier de config: {config.config_file}")
    click.echo(f"📁 Répertoire posts: {config.get('general.posts_directory', 'posts')}")
    
    # Validation des clés API
    click.echo("\n🔑 Validation des clés API:")
    api_validation = config.validate_api_keys()
    for platform, valid in api_validation.items():
        status_icon = "✅" if valid else "❌"
        enabled = "activé" if config.is_platform_enabled(platform) else "désactivé"
        click.echo(f"  {status_icon} {platform.capitalize()}: {enabled}")
    
    # Test des connexions
    click.echo("\n🌐 Test des connexions:")
    publisher = Publisher(config.config)
    test_results = publisher.test_all_connections()
    
    for platform, result in test_results.items():
        if result['available']:
            auth_status = "✅ connecté" if result['authenticated'] else "❌ non connecté"
            click.echo(f"  {platform.capitalize()}: {auth_status}")
        else:
            click.echo(f"  {platform.capitalize()}: ❌ erreur - {result['error']}")

@cli.command()
@click.pass_context
def list_posts(ctx):
    """Liste tous les posts disponibles."""
    config = ctx.obj['config']
    
    content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
    posts = content_manager.load_posts()
    
    if not posts:
        click.echo("📭 Aucun post trouvé")
        return
    
    click.echo(f"📝 {len(posts)} post(s) trouvé(s):")
    click.echo("=" * 50)
    
    for post in posts:
        platforms_str = ", ".join(post.platforms)
        click.echo(f"🆔 {post.id}")
        click.echo(f"📰 {post.title}")
        click.echo(f"🎯 Plateformes: {platforms_str}")
        click.echo(f"⏰ Programmé: {post.schedule}")
        if post.tags:
            click.echo(f"🏷️  Tags: {', '.join(post.tags)}")
        click.echo("-" * 30)

@cli.command()
@click.argument('post_id')
@click.option('--platform', help='Publier sur une plateforme spécifique')
@click.option('--dry-run', is_flag=True, help='Simulation sans publication réelle')
@click.pass_context
def publish(ctx, post_id, platform, dry_run):
    """Publie un post spécifique."""
    config = ctx.obj['config']
    
    # Charger les posts
    content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
    content_manager.load_posts()
    
    # Trouver le post
    post = content_manager.get_post_by_id(post_id)
    if not post:
        click.echo(f"❌ Post '{post_id}' non trouvé", err=True)
        return
    
    click.echo(f"🚀 Publication du post: {post.title}")
    
    if dry_run:
        click.echo("🧪 Mode simulation activé")
        platforms_to_publish = [platform] if platform else post.platforms
        for p in platforms_to_publish:
            click.echo(f"  📤 Simulerait publication sur {p}")
        return
    
    # Publier
    publisher = Publisher(config.config)
    
    if platform:
        # Publication sur une plateforme spécifique
        if platform not in post.platforms:
            click.echo(f"⚠️  Plateforme {platform} non configurée pour ce post", err=True)
            return
        
        result = publisher.publish_to_platform(post, platform)
        if result.success:
            click.echo(f"✅ Publié avec succès sur {platform}")
        else:
            click.echo(f"❌ Échec sur {platform}: {result.error}", err=True)
    else:
        # Publication sur toutes les plateformes
        results = publisher.publish_to_all_platforms(post)
        
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        click.echo(f"📊 Résultat: {success_count}/{total_count} plateformes")
        
        for result in results:
            status_icon = "✅" if result.success else "❌"
            click.echo(f"  {status_icon} {result.platform}: {result.error or 'Succès'}")

@cli.command()
@click.option('--blocking', is_flag=True, help='Mode bloquant (premier plan)')
@click.pass_context
def start(ctx, blocking):
    """Démarre le planificateur automatique."""
    config = ctx.obj['config']
    
    if not config.get('scheduler.enabled', True):
        click.echo("❌ Planificateur désactivé dans la configuration", err=True)
        return
    
    click.echo("🚀 Démarrage du planificateur...")
    
    # Initialiser les composants
    content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
    publisher = Publisher(config.config)
    scheduler = BotScheduler(config.config, content_manager, publisher)
    
    # Authentifier toutes les plateformes
    click.echo("🔐 Authentification des plateformes...")
    auth_results = publisher.authenticate_all()
    
    failed_auths = [p for p, success in auth_results.items() if not success]
    if failed_auths:
        click.echo(f"⚠️  Échec d'authentification: {', '.join(failed_auths)}")
    
    # Démarrer le planificateur
    try:
        if blocking:
            click.echo("⏰ Planificateur démarré en mode bloquant (Ctrl+C pour arrêter)")
            scheduler.start(blocking=True)
        else:
            click.echo("⏰ Planificateur démarré en arrière-plan")
            scheduler.start(blocking=False)
            click.echo("✅ Planificateur actif")
    except KeyboardInterrupt:
        click.echo("\n⏹️  Arrêt du planificateur...")
        scheduler.stop()
        click.echo("👋 Au revoir!")

@cli.command()
@click.pass_context
def test(ctx):
    """Teste la publication avec un post de test."""
    config = ctx.obj['config']
    
    click.echo("🧪 Test de publication...")
    
    publisher = Publisher(config.config)
    
    # Authentifier
    click.echo("🔐 Authentification...")
    auth_results = publisher.authenticate_all()
    
    failed_auths = [p for p, success in auth_results.items() if not success]
    if failed_auths:
        click.echo(f"❌ Échec d'authentification: {', '.join(failed_auths)}")
        return
    
    # Publier un post de test
    click.echo("📤 Publication du post de test...")
    results = publisher.publish_test_post()
    
    success_count = sum(1 for r in results if r.success)
    total_count = len(results)
    
    click.echo(f"📊 Résultat: {success_count}/{total_count} plateformes")
    
    for result in results:
        status_icon = "✅" if result.success else "❌"
        click.echo(f"  {status_icon} {result.platform}: {result.error or 'Succès'}")

@cli.command()
@click.pass_context
def jobs(ctx):
    """Liste les tâches programmées."""
    config = ctx.obj['config']
    
    content_manager = ContentManager(config.get('general.posts_directory', 'posts'))
    publisher = Publisher(config.config)
    scheduler = BotScheduler(config.config, content_manager, publisher)
    
    # Note: Pour lister les jobs, le scheduler doit être démarré
    click.echo("⚠️  Pour voir les tâches actives, le planificateur doit être démarré")
    click.echo("💡 Utilisez 'python cli.py start' pour démarrer le planificateur")

@cli.command()
@click.argument('title')
@click.argument('body')
@click.option('--platforms', default='twitter,reddit', help='Plateformes (séparées par des virgules)')
@click.option('--tags', help='Tags (séparés par des virgules)')
@click.option('--schedule', help='Date de publication (YYYY-MM-DD HH:MM)')
@click.pass_context
def add_post(ctx, title, body, platforms, tags, schedule):
    """Ajoute un nouveau post."""
    config = ctx.obj['config']
    
    # Générer un ID unique
    post_id = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Préparer les données du post
    post_data = {
        'id': post_id,
        'title': title,
        'body': body,
        'platforms': platforms.split(','),
        'schedule': schedule or 'manual',
        'tags': tags.split(',') if tags else []
    }
    
    # Sauvegarder dans un fichier
    posts_dir = Path(config.get('general.posts_directory', 'posts'))
    posts_dir.mkdir(exist_ok=True)
    
    manual_file = posts_dir / 'manual_posts.yaml'
    
    # Charger les posts existants ou créer une nouvelle structure
    if manual_file.exists():
        import yaml
        with open(manual_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f) or {'posts': []}
    else:
        data = {'posts': []}
    
    # Ajouter le nouveau post
    data['posts'].append(post_data)
    
    # Sauvegarder
    import yaml
    with open(manual_file, 'w', encoding='utf-8') as f:
        yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
    
    click.echo(f"✅ Post '{post_id}' ajouté avec succès")
    click.echo(f"📁 Sauvegardé dans: {manual_file}")

if __name__ == '__main__':
    cli()
