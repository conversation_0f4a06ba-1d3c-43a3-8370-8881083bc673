# Exemple de service systemd pour déploiement sur VPS
# Placez ce fichier dans /etc/systemd/system/bot-publication.service

[Unit]
Description=Bot de Publication Automatisé
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/project/assets/bot
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PYTHONPATH=/path/to/your/project/assets/bot/src
ExecStart=/usr/bin/python3 /path/to/your/project/assets/bot/cli.py start --blocking
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=bot-publication

# Variables d'environnement (optionnel, préférez le fichier .env)
# Environment=LOG_LEVEL=INFO

[Install]
WantedBy=multi-user.target

# Instructions d'installation :
# 1. Copiez ce fichier vers /etc/systemd/system/bot-publication.service
# 2. Modifiez les chemins selon votre installation
# 3. Rechargez systemd : sudo systemctl daemon-reload
# 4. Activez le service : sudo systemctl enable bot-publication
# 5. Démarrez le service : sudo systemctl start bot-publication
# 6. Vérifiez le statut : sudo systemctl status bot-publication
# 7. Consultez les logs : sudo journalctl -u bot-publication -f

# Commandes utiles :
# sudo systemctl start bot-publication     # Démarrer
# sudo systemctl stop bot-publication      # Arrêter
# sudo systemctl restart bot-publication   # Redémarrer
# sudo systemctl status bot-publication    # Statut
# sudo journalctl -u bot-publication -f    # Logs en temps réel
