#!/usr/bin/env python3
"""
Script pour corriger massivement toutes les erreurs restantes dans ChronoForge
"""

import os
import re

def fix_file(file_path):
    """Corrige un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix .material.material chains
        content = re.sub(r'\.material\.material\.color', '.color', content)
        content = re.sub(r'\.material\.material', '.material', content)
        
        # Fix Gizmos.material (should be Gizmos.color)
        content = re.sub(r'Gizmos\.material\.color', 'Gizmos.color', content)
        content = re.sub(r'Gizmos\.material', 'Gizmos.color', content)
        
        # Fix spriteRenderer.material.material
        content = re.sub(r'spriteRenderer\.material\.material\.color', 'spriteRenderer.material.color', content)
        
        # Fix UI components .material.material
        content = re.sub(r'(\w+Image)\.material\.material\.color', r'\1.color', content)
        content = re.sub(r'(\w+Image)\.material\.color', r'\1.color', content)
        content = re.sub(r'(\w+Text)\.material\.material\.color', r'\1.color', content)
        content = re.sub(r'(\w+Text)\.material\.color', r'\1.color', content)
        
        # Fix specific UI components
        content = re.sub(r'iconImage\.material\.material\.color', 'iconImage.color', content)
        content = re.sub(r'iconImage\.material\.color', 'iconImage.color', content)
        content = re.sub(r'backgroundImage\.material\.material\.color', 'backgroundImage.color', content)
        content = re.sub(r'backgroundImage\.material\.color', 'backgroundImage.color', content)
        content = re.sub(r'notificationImage\.material\.material\.color', 'notificationImage.color', content)
        content = re.sub(r'notificationImage\.material\.color', 'notificationImage.color', content)
        
        # Fix Debug.log issues
        content = re.sub(r'ChronoForge\.Debug\.log\(', 'UnityEngine.Debug.Log(', content)
        
        # Fix FindObjectsOfType warnings
        content = re.sub(r'FindObjectsOfType<([^>]+)>\(\)', r'FindObjectsByType<\1>(FindObjectsSortMode.None)', content)
        
        # Fix Resolution.refreshRate warning
        content = re.sub(r'\.refreshRate', '.refreshRateRatio.value', content)
        
        # Fix GetTotalAllocatedMemory
        content = re.sub(r'GetTotalAllocatedMemory\(true\)', 'GetTotalAllocatedMemory(false)', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
    
    return False

def main():
    """Fonction principale"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    assets_dir = os.path.join(script_dir, "Assets", "Scripts")
    
    if not os.path.exists(assets_dir):
        print(f"❌ Directory not found: {assets_dir}")
        return
    
    fixed_count = 0
    
    # Parcourir tous les fichiers .cs
    for root, dirs, files in os.walk(assets_dir):
        for file in files:
            if file.endswith('.cs'):
                file_path = os.path.join(root, file)
                if fix_file(file_path):
                    fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} files!")

if __name__ == "__main__":
    main()
