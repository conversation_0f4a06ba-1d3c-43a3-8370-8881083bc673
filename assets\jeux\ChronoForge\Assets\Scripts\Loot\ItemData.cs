using UnityEngine;

namespace ChronoForge.Loot
{
    /// <summary>
    /// Types d'items dans ChronoForge
    /// </summary>
    public enum ItemType
    {
        Weapon,
        Armor,
        Artifact,
        Consumable,
        Material,
        Currency,
        Key,
        Special
    }
    
    /// <summary>
    /// Rareté des items
    /// </summary>
    public enum ItemRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary,
        Mythic
    }
    
    /// <summary>
    /// Données d'un item dans ChronoForge
    /// </summary>
    [CreateAssetMenu(fileName = "NewItem", menuName = "ChronoForge/Item Data")]
    public class ItemData : ScriptableObject
    {
        [Header("Basic Info")]
        public string itemId;
        public string itemName;
        public string description;
        public ItemType itemType;
        public ItemRarity rarity;
        
        [Header("Visual")]
        public Sprite icon;
        public GameObject worldPrefab;
        public Color rarityColor = Color.white;
        
        [Header("Properties")]
        public int maxStackSize = 1;
        public bool isConsumable = false;
        public bool isDroppable = true;
        public bool isTradeable = true;
        
        [Header("Stats")]
        public float damageBonus = 0f;
        public float healthBonus = 0f;
        public float speedBonus = 0f;
        public float criticalChanceBonus = 0f;
        public float criticalDamageBonus = 0f;
        
        [Header("Special Effects")]
        public string[] specialEffects = new string[0];
        public float[] effectValues = new float[0];
        
        [Header("Economy")]
        public int baseValue = 10;
        public bool canBeSold = true;
        public bool canBeBought = false;
        
        /// <summary>
        /// Obtient la couleur basée sur la rareté
        /// </summary>
        public Color GetRarityColor()
        {
            switch (rarity)
            {
                case ItemRarity.Common:
                    return Color.white;
                case ItemRarity.Uncommon:
                    return Color.green;
                case ItemRarity.Rare:
                    return Color.blue;
                case ItemRarity.Epic:
                    return Color.magenta;
                case ItemRarity.Legendary:
                    return Color.yellow;
                case ItemRarity.Mythic:
                    return Color.red;
                default:
                    return Color.gray;
            }
        }
        
        /// <summary>
        /// Obtient la valeur de vente
        /// </summary>
        public int GetSellValue()
        {
            if (!canBeSold) return 0;
            
            float multiplier = GetRarityMultiplier();
            return Mathf.RoundToInt(baseValue * multiplier);
        }
        
        /// <summary>
        /// Obtient la valeur d'achat
        /// </summary>
        public int GetBuyValue()
        {
            if (!canBeBought) return 0;
            
            return GetSellValue() * 2; // Buy price is double sell price
        }
        
        /// <summary>
        /// Obtient le multiplicateur de rareté
        /// </summary>
        private float GetRarityMultiplier()
        {
            switch (rarity)
            {
                case ItemRarity.Common:
                    return 1f;
                case ItemRarity.Uncommon:
                    return 2f;
                case ItemRarity.Rare:
                    return 5f;
                case ItemRarity.Epic:
                    return 10f;
                case ItemRarity.Legendary:
                    return 25f;
                case ItemRarity.Mythic:
                    return 50f;
                default:
                    return 1f;
            }
        }
        
        /// <summary>
        /// Vérifie si l'item a un effet spécial
        /// </summary>
        public bool HasSpecialEffect(string effectName)
        {
            return System.Array.IndexOf(specialEffects, effectName) >= 0;
        }
        
        /// <summary>
        /// Obtient la valeur d'un effet spécial
        /// </summary>
        public float GetSpecialEffectValue(string effectName)
        {
            int index = System.Array.IndexOf(specialEffects, effectName);
            if (index >= 0 && index < effectValues.Length)
            {
                return effectValues[index];
            }
            return 0f;
        }
        
        /// <summary>
        /// Obtient la description complète avec stats
        /// </summary>
        public string GetFullDescription()
        {
            string fullDesc = description;
            
            // Add stat bonuses
            if (damageBonus != 0)
                fullDesc += $"\n+{damageBonus} Damage";
            
            if (healthBonus != 0)
                fullDesc += $"\n+{healthBonus} Health";
            
            if (speedBonus != 0)
                fullDesc += $"\n+{speedBonus * 100:F0}% Speed";
            
            if (criticalChanceBonus != 0)
                fullDesc += $"\n+{criticalChanceBonus * 100:F0}% Critical Chance";
            
            if (criticalDamageBonus != 0)
                fullDesc += $"\n+{criticalDamageBonus * 100:F0}% Critical Damage";
            
            // Add special effects
            for (int i = 0; i < specialEffects.Length && i < effectValues.Length; i++)
            {
                fullDesc += $"\n{specialEffects[i]}: {effectValues[i]}";
            }
            
            return fullDesc;
        }
    }
    
    /// <summary>
    /// Instance d'un item dans l'inventaire
    /// </summary>
    [System.Serializable]
    public class ItemInstance
    {
        public ItemData itemData;
        public int quantity = 1;
        public string uniqueId;
        public float durability = 1f;
        public bool isEquipped = false;
        
        // Custom properties for this instance
        public float[] customStats = new float[0];
        public string[] customEffects = new string[0];
        
        public ItemInstance(ItemData data, int qty = 1)
        {
            itemData = data;
            quantity = qty;
            uniqueId = System.Guid.NewGuid().ToString();
            durability = 1f;
        }
        
        /// <summary>
        /// Vérifie si l'item est valide
        /// </summary>
        public bool IsValid()
        {
            return itemData != null && quantity > 0;
        }
        
        /// <summary>
        /// Obtient la valeur totale de la stack
        /// </summary>
        public int GetTotalValue()
        {
            return itemData != null ? itemData.GetSellValue() * quantity : 0;
        }
        
        /// <summary>
        /// Peut être stacké avec un autre item
        /// </summary>
        public bool CanStackWith(ItemInstance other)
        {
            return other != null && 
                   itemData == other.itemData && 
                   quantity + other.quantity <= itemData.maxStackSize;
        }
        
        /// <summary>
        /// Stack avec un autre item
        /// </summary>
        public bool StackWith(ItemInstance other)
        {
            if (!CanStackWith(other)) return false;
            
            quantity += other.quantity;
            return true;
        }
        
        /// <summary>
        /// Divise la stack
        /// </summary>
        public ItemInstance Split(int amount)
        {
            if (amount >= quantity) return null;
            
            quantity -= amount;
            return new ItemInstance(itemData, amount);
        }
    }
}
