using UnityEngine;
using UnityEngine.UI;
using TMPro;
using ChronoForge.Core;
using ChronoForge.Audio;

namespace ChronoForge.UI
{
    /// <summary>
    /// Contrôleur de paramètres pour ChronoForge
    /// </summary>
    public class SettingsController : MonoBehaviour
    {
        [Header("Audio Settings")]
        public Slider masterVolumeSlider;
        public Slider musicVolumeSlider;
        public Slider sfxVolumeSlider;
        public Slider ambientVolumeSlider;
        public Slider uiVolumeSlider;
        
        [Header("Graphics Settings")]
        public TMP_Dropdown qualityDropdown;
        public TMP_Dropdown resolutionDropdown;
        public Toggle fullscreenToggle;
        public Toggle vsyncToggle;
        public Slider fpsLimitSlider;
        public TextMeshProUGUI fpsLimitText;
        
        [Header("Gameplay Settings")]
        public Toggle showDamageNumbersToggle;
        public Toggle screenShakeToggle;
        public Toggle autoAimToggle;
        public Slider mouseSensitivitySlider;
        public Toggle pauseOnFocusLossToggle;
        
        [Header("UI Settings")]
        public Toggle showMinimapToggle;
        public Toggle showHealthBarToggle;
        public Toggle showCrosshairToggle;
        public Slider uiScaleSlider;
        public Toggle showFPSToggle;
        
        [Header("Accessibility")]
        public Toggle colorBlindModeToggle;
        public Toggle highContrastModeToggle;
        public Slider textSizeSlider;
        public Toggle subtitlesToggle;
        
        [Header("Language")]
        public TMP_Dropdown languageDropdown;
        
        [Header("Buttons")]
        public Button applyButton;
        public Button resetButton;
        public Button backButton;
        
        // Components
        private GameSettings gameSettings;
        private AudioManager audioManager;

        // Resolution options
        private Resolution[] resolutions;

        // Audio settings cache
        private float masterVolume = 1f;
        private float musicVolume = 1f;
        private float sfxVolume = 1f;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeSettings();
        }
        
        private void Start()
        {
            FindComponents();
            SetupUI();
            LoadCurrentSettings();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeSettings()
        {
            // Get available resolutions
            resolutions = Screen.resolutions;
            
            UnityEngine.Debug.Log("⚙️ SettingsController initialized");
        }
        
        private void FindComponents()
        {
            gameSettings = GameSettings.Instance;
            audioManager = AudioManager.Instance;
        }
        
        private void SetupUI()
        {
            SetupAudioSliders();
            SetupGraphicsControls();
            SetupGameplayToggles();
            SetupUIControls();
            SetupAccessibilityControls();
            SetupLanguageDropdown();
            SetupButtons();
        }
        
        private void SetupAudioSliders()
        {
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
            
            if (ambientVolumeSlider != null)
                ambientVolumeSlider.onValueChanged.AddListener(OnAmbientVolumeChanged);
            
            if (uiVolumeSlider != null)
                uiVolumeSlider.onValueChanged.AddListener(OnUIVolumeChanged);
        }
        
        private void SetupGraphicsControls()
        {
            // Quality dropdown
            if (qualityDropdown != null)
            {
                qualityDropdown.ClearOptions();
                qualityDropdown.AddOptions(new System.Collections.Generic.List<string>(QualitySettings.names));
                qualityDropdown.onValueChanged.AddListener(OnQualityChanged);
            }
            
            // Resolution dropdown
            if (resolutionDropdown != null)
            {
                resolutionDropdown.ClearOptions();
                var resolutionOptions = new System.Collections.Generic.List<string>();
                
                foreach (Resolution res in resolutions)
                {
                    resolutionOptions.Add($"{res.width} x {res.height} @ {res.refreshRateRatio.value}Hz");
                }
                
                resolutionDropdown.AddOptions(resolutionOptions);
                resolutionDropdown.onValueChanged.AddListener(OnResolutionChanged);
            }
            
            // Graphics toggles
            if (fullscreenToggle != null)
                fullscreenToggle.onValueChanged.AddListener(OnFullscreenChanged);
            
            if (vsyncToggle != null)
                vsyncToggle.onValueChanged.AddListener(OnVSyncChanged);
            
            // FPS limit
            if (fpsLimitSlider != null)
            {
                fpsLimitSlider.onValueChanged.AddListener(OnFPSLimitChanged);
                fpsLimitSlider.minValue = 30;
                fpsLimitSlider.maxValue = 144;
                fpsLimitSlider.wholeNumbers = true;
            }
        }
        
        private void SetupGameplayToggles()
        {
            if (showDamageNumbersToggle != null)
                showDamageNumbersToggle.onValueChanged.AddListener(OnShowDamageNumbersChanged);
            
            if (screenShakeToggle != null)
                screenShakeToggle.onValueChanged.AddListener(OnScreenShakeChanged);
            
            if (autoAimToggle != null)
                autoAimToggle.onValueChanged.AddListener(OnAutoAimChanged);
            
            if (mouseSensitivitySlider != null)
                mouseSensitivitySlider.onValueChanged.AddListener(OnMouseSensitivityChanged);
            
            if (pauseOnFocusLossToggle != null)
                pauseOnFocusLossToggle.onValueChanged.AddListener(OnPauseOnFocusLossChanged);
        }
        
        private void SetupUIControls()
        {
            if (showMinimapToggle != null)
                showMinimapToggle.onValueChanged.AddListener(OnShowMinimapChanged);
            
            if (showHealthBarToggle != null)
                showHealthBarToggle.onValueChanged.AddListener(OnShowHealthBarChanged);
            
            if (showCrosshairToggle != null)
                showCrosshairToggle.onValueChanged.AddListener(OnShowCrosshairChanged);
            
            if (uiScaleSlider != null)
                uiScaleSlider.onValueChanged.AddListener(OnUIScaleChanged);
            
            if (showFPSToggle != null)
                showFPSToggle.onValueChanged.AddListener(OnShowFPSChanged);
        }
        
        private void SetupAccessibilityControls()
        {
            if (colorBlindModeToggle != null)
                colorBlindModeToggle.onValueChanged.AddListener(OnColorBlindModeChanged);
            
            if (highContrastModeToggle != null)
                highContrastModeToggle.onValueChanged.AddListener(OnHighContrastModeChanged);
            
            if (textSizeSlider != null)
                textSizeSlider.onValueChanged.AddListener(OnTextSizeChanged);
            
            if (subtitlesToggle != null)
                subtitlesToggle.onValueChanged.AddListener(OnSubtitlesChanged);
        }
        
        private void SetupLanguageDropdown()
        {
            if (languageDropdown != null)
            {
                languageDropdown.ClearOptions();
                var languageOptions = new System.Collections.Generic.List<string>
                {
                    "English", "Français", "Español", "Deutsch", "日本語"
                };
                languageDropdown.AddOptions(languageOptions);
                languageDropdown.onValueChanged.AddListener(OnLanguageChanged);
            }
        }
        
        private void SetupButtons()
        {
            if (applyButton != null)
                applyButton.onClick.AddListener(ApplySettings);
            
            if (resetButton != null)
                resetButton.onClick.AddListener(ResetToDefaults);
            
            if (backButton != null)
                backButton.onClick.AddListener(CloseSettings);
        }
        
        #endregion
        
        #region Load Settings
        
        private void LoadCurrentSettings()
        {
            if (gameSettings == null) return;
            
            // Audio settings
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = gameSettings.masterVolume;
            
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = gameSettings.musicVolume;
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = gameSettings.sfxVolume;
            
            if (ambientVolumeSlider != null)
                ambientVolumeSlider.value = gameSettings.ambientVolume;
            
            if (uiVolumeSlider != null)
                uiVolumeSlider.value = gameSettings.uiVolume;
            
            // Graphics settings
            if (qualityDropdown != null)
                qualityDropdown.value = gameSettings.qualityLevel;
            
            if (fullscreenToggle != null)
                fullscreenToggle.isOn = gameSettings.fullscreen;
            
            if (vsyncToggle != null)
                vsyncToggle.isOn = gameSettings.enableVSync;
            
            if (fpsLimitSlider != null)
            {
                fpsLimitSlider.value = gameSettings.targetFrameRate;
                UpdateFPSLimitText(gameSettings.targetFrameRate);
            }
            
            // Gameplay settings
            if (showDamageNumbersToggle != null)
                showDamageNumbersToggle.isOn = gameSettings.showDamageNumbers;
            
            if (screenShakeToggle != null)
                screenShakeToggle.isOn = gameSettings.screenShakeEnabled;
            
            if (autoAimToggle != null)
                autoAimToggle.isOn = gameSettings.autoAim;
            
            if (mouseSensitivitySlider != null)
                mouseSensitivitySlider.value = gameSettings.mouseSensitivity;
            
            if (pauseOnFocusLossToggle != null)
                pauseOnFocusLossToggle.isOn = gameSettings.pauseOnFocusLoss;
            
            // UI settings
            if (showMinimapToggle != null)
                showMinimapToggle.isOn = gameSettings.showMinimap;
            
            if (showHealthBarToggle != null)
                showHealthBarToggle.isOn = gameSettings.showHealthBar;
            
            if (showCrosshairToggle != null)
                showCrosshairToggle.isOn = gameSettings.showCrosshair;
            
            if (uiScaleSlider != null)
                uiScaleSlider.value = gameSettings.uiScale;
            
            if (showFPSToggle != null)
                showFPSToggle.isOn = gameSettings.showFPS;
            
            // Accessibility settings
            if (colorBlindModeToggle != null)
                colorBlindModeToggle.isOn = gameSettings.colorBlindMode;
            
            if (highContrastModeToggle != null)
                highContrastModeToggle.isOn = gameSettings.highContrastMode;
            
            if (textSizeSlider != null)
                textSizeSlider.value = gameSettings.textSize;
            
            if (subtitlesToggle != null)
                subtitlesToggle.isOn = gameSettings.subtitlesEnabled;
        }
        
        #endregion
        
        #region Event Handlers
        
        // Audio handlers
        private void OnMasterVolumeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.SetMasterVolume(value);
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.SetMusicVolume(value);
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.SetSFXVolume(value);
        }
        
        private void OnAmbientVolumeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.ambientVolume = value;
        }
        
        private void OnUIVolumeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.uiVolume = value;
        }
        
        // Graphics handlers
        private void OnQualityChanged(int value)
        {
            if (gameSettings != null)
                gameSettings.SetQualityLevel(value);
        }
        
        private void OnResolutionChanged(int value)
        {
            if (value >= 0 && value < resolutions.Length)
            {
                Resolution resolution = resolutions[value];
                if (gameSettings != null)
                    gameSettings.SetResolution(resolution.width, resolution.height, gameSettings.fullscreen);
            }
        }
        
        private void OnFullscreenChanged(bool value)
        {
            if (gameSettings != null)
            {
                gameSettings.fullscreen = value;
                Screen.fullScreen = value;
            }
        }
        
        private void OnVSyncChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.SetVSync(value);
        }
        
        private void OnFPSLimitChanged(float value)
        {
            int fps = Mathf.RoundToInt(value);
            if (gameSettings != null)
                gameSettings.SetFrameRate(fps);
            
            UpdateFPSLimitText(fps);
        }
        
        // Gameplay handlers
        private void OnShowDamageNumbersChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.showDamageNumbers = value;
        }
        
        private void OnScreenShakeChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.screenShakeEnabled = value;
        }
        
        private void OnAutoAimChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.SetAutoAim(value);
        }
        
        private void OnMouseSensitivityChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.SetMouseSensitivity(value);
        }
        
        private void OnPauseOnFocusLossChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.pauseOnFocusLoss = value;
        }
        
        // UI handlers
        private void OnShowMinimapChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.showMinimap = value;
        }
        
        private void OnShowHealthBarChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.showHealthBar = value;
        }
        
        private void OnShowCrosshairChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.showCrosshair = value;
        }
        
        private void OnUIScaleChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.SetUIScale(value);
        }
        
        private void OnShowFPSChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.showFPS = value;
        }
        
        // Accessibility handlers
        private void OnColorBlindModeChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.SetColorBlindMode(value);
        }
        
        private void OnHighContrastModeChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.SetHighContrastMode(value);
        }
        
        private void OnTextSizeChanged(float value)
        {
            if (gameSettings != null)
                gameSettings.textSize = value;
        }
        
        private void OnSubtitlesChanged(bool value)
        {
            if (gameSettings != null)
                gameSettings.subtitlesEnabled = value;
        }
        
        private void OnLanguageChanged(int value)
        {
            // This would change the language
            UnityEngine.Debug.Log($"Language changed to index: {value}");
        }
        
        #endregion
        
        #region Button Actions
        
        private void ApplySettings()
        {
            if (gameSettings != null)
            {
                gameSettings.ApplyAllSettings();
                gameSettings.SaveSettings();
            }
            
            UnityEngine.Debug.Log("⚙️ Settings applied and saved");
        }
        
        private void ResetToDefaults()
        {
            if (gameSettings != null)
            {
                gameSettings.ResetToDefaults();
                LoadCurrentSettings();
            }
            
            UnityEngine.Debug.Log("🔄 Settings reset to defaults");
        }
        
        private void CloseSettings()
        {
            gameObject.SetActive(false);
        }
        
        #endregion
        
        #region Utility
        
        private void UpdateFPSLimitText(int fps)
        {
            if (fpsLimitText != null)
            {
                fpsLimitText.text = fps.ToString();
            }
        }
        
        #endregion
        
        #region Public Methods
        
        public void ShowSettings()
        {
            gameObject.SetActive(true);
            LoadCurrentSettings();
        }
        
        public void HideSettings()
        {
            gameObject.SetActive(false);
        }
        
        public void RefreshSettings()
        {
            LoadSettingsFromPrefs();
            UpdateUIElements();
        }

        private void LoadSettingsFromPrefs()
        {
            // Load settings from PlayerPrefs
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 1f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 1f);

            // Apply loaded settings
            ApplyAudioSettings();
        }

        private void UpdateUIElements()
        {
            // Update UI sliders to reflect current settings
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = masterVolume;
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = musicVolume;
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = sfxVolume;
        }

        private void ApplyAudioSettings()
        {
            if (gameSettings != null)
            {
                gameSettings.SetMasterVolume(masterVolume);
                gameSettings.SetMusicVolume(musicVolume);
                gameSettings.SetSFXVolume(sfxVolume);
            }
        }
        
        #endregion
    }
}
