using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using System.IO;

namespace ChronoForge.Editor
{
    public class ChronoForgeSetupWizard : EditorWindow
    {
        private int currentStep = 0;
        private string[] steps = {
            "🏗️ Structure des Dossiers",
            "🎮 Génération des Prefabs", 
            "🎬 Configuration de Scène",
            "⚙️ Paramètres du Projet",
            "✅ Finalisation"
        };

        private bool[] stepCompleted = new bool[5];

        [MenuItem("ChronoForge/🚀 Setup Wizard Complet")]
        public static void ShowWindow()
        {
            ChronoForgeSetupWizard window = GetWindow<ChronoForgeSetupWizard>("ChronoForge Setup Wizard");
            window.minSize = new Vector2(500, 400);
        }

        private void OnGUI()
        {
            DrawHeader();
            DrawProgressBar();
            DrawCurrentStep();
            DrawNavigationButtons();
        }

        private void DrawHeader()
        {
            GUILayout.Space(10);
            GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 18;
            headerStyle.alignment = TextAnchor.MiddleCenter;
            
            GUILayout.Label("🎮 ChronoForge Setup Wizard", headerStyle);
            GUILayout.Label("Configuration automatique complète du projet", EditorStyles.centeredGreyMiniLabel);
            GUILayout.Space(20);
        }

        private void DrawProgressBar()
        {
            GUILayout.Label($"Étape {currentStep + 1} sur {steps.Length}", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            for (int i = 0; i < steps.Length; i++)
            {
                Color originalColor = GUI.backgroundColor;
                
                if (stepCompleted[i])
                    GUI.backgroundColor = Color.green;
                else if (i == currentStep)
                    GUI.backgroundColor = Color.yellow;
                else
                    GUI.backgroundColor = Color.gray;

                GUILayout.Button($"{i + 1}", GUILayout.Width(30), GUILayout.Height(30));
                GUI.backgroundColor = originalColor;
                
                if (i < steps.Length - 1)
                    GUILayout.Label("→", GUILayout.Width(20));
            }
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(10);
        }

        private void DrawCurrentStep()
        {
            GUILayout.Label(steps[currentStep], EditorStyles.boldLabel);
            GUILayout.Space(10);

            switch (currentStep)
            {
                case 0: DrawStep1_FolderStructure(); break;
                case 1: DrawStep2_PrefabGeneration(); break;
                case 2: DrawStep3_SceneSetup(); break;
                case 3: DrawStep4_ProjectSettings(); break;
                case 4: DrawStep5_Finalization(); break;
            }
        }

        private void DrawStep1_FolderStructure()
        {
            GUILayout.Label("Cette étape va créer toute la structure de dossiers nécessaire pour ChronoForge :", EditorStyles.wordWrappedLabel);
            GUILayout.Space(10);

            GUILayout.Label("📁 Dossiers à créer :");
            GUILayout.Label("• Assets/Prefabs/ (avec sous-dossiers)");
            GUILayout.Label("• Assets/Scenes/");
            GUILayout.Label("• Assets/Materials/");
            GUILayout.Label("• Assets/Textures/");
            GUILayout.Label("• Assets/Audio/");
            GUILayout.Label("• Assets/Animations/");

            GUILayout.Space(20);

            if (GUILayout.Button("🏗️ Créer la Structure de Dossiers", GUILayout.Height(40)))
            {
                CreateCompleteProjectStructure();
                stepCompleted[0] = true;
                currentStep = 1;
            }
        }

        private void DrawStep2_PrefabGeneration()
        {
            GUILayout.Label("Cette étape va générer automatiquement tous les prefabs essentiels :", EditorStyles.wordWrappedLabel);
            GUILayout.Space(10);

            GUILayout.Label("🎮 Prefabs à créer :");
            GUILayout.Label("• Player (avec tous les composants)");
            GUILayout.Label("• GameManager (avec tous les managers)");
            GUILayout.Label("• UI Canvas (avec tous les panels)");
            GUILayout.Label("• AudioManager (avec sources audio)");
            GUILayout.Label("• EffectsManager");
            GUILayout.Label("• LevelGenerator");

            GUILayout.Space(20);

            if (GUILayout.Button("🎮 Générer Tous les Prefabs", GUILayout.Height(40)))
            {
                GenerateAllPrefabs();
                stepCompleted[1] = true;
                currentStep = 2;
            }
        }

        private void DrawStep3_SceneSetup()
        {
            GUILayout.Label("Cette étape va créer et configurer une scène de jeu complète :", EditorStyles.wordWrappedLabel);
            GUILayout.Space(10);

            GUILayout.Label("🎬 Configuration de scène :");
            GUILayout.Label("• Caméra 2D optimisée");
            GUILayout.Label("• Éclairage configuré");
            GUILayout.Label("• Physique 2D (gravité désactivée)");
            GUILayout.Label("• Instanciation des prefabs principaux");
            GUILayout.Label("• Niveau de test simple");

            GUILayout.Space(20);

            if (GUILayout.Button("🎬 Configurer la Scène de Jeu", GUILayout.Height(40)))
            {
                SetupGameScene();
                stepCompleted[2] = true;
                currentStep = 3;
            }
        }

        private void DrawStep4_ProjectSettings()
        {
            GUILayout.Label("Cette étape configure les paramètres optimaux du projet :", EditorStyles.wordWrappedLabel);
            GUILayout.Space(10);

            GUILayout.Label("⚙️ Paramètres à configurer :");
            GUILayout.Label("• Layers de collision");
            GUILayout.Label("• Tags personnalisés");
            GUILayout.Label("• Paramètres de physique 2D");
            GUILayout.Label("• Paramètres de qualité");
            GUILayout.Label("• Input Manager");

            GUILayout.Space(20);

            if (GUILayout.Button("⚙️ Configurer les Paramètres", GUILayout.Height(40)))
            {
                ConfigureProjectSettings();
                stepCompleted[3] = true;
                currentStep = 4;
            }
        }

        private void DrawStep5_Finalization()
        {
            GUILayout.Label("🎉 Configuration terminée avec succès !", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("Votre projet ChronoForge est maintenant prêt !", EditorStyles.wordWrappedLabel);
            GUILayout.Space(10);

            GUILayout.Label("✅ Étapes suivantes recommandées :");
            GUILayout.Label("1. Ouvrir la scène GameScene");
            GUILayout.Label("2. Appuyer sur Play pour tester");
            GUILayout.Label("3. Ajouter vos sprites et assets");
            GUILayout.Label("4. Commencer le développement !");

            GUILayout.Space(20);

            if (GUILayout.Button("🚀 Ouvrir la Scène de Jeu", GUILayout.Height(40)))
            {
                EditorSceneManager.OpenScene("Assets/Scenes/GameScene.unity");
                stepCompleted[4] = true;
            }

            GUILayout.Space(10);

            if (GUILayout.Button("📖 Ouvrir la Documentation", GUILayout.Height(30)))
            {
                Application.OpenURL("file://" + Application.dataPath + "/../PREFAB_SETUP_GUIDE.md");
            }
        }

        private void DrawNavigationButtons()
        {
            GUILayout.FlexibleSpace();
            
            EditorGUILayout.BeginHorizontal();
            
            if (currentStep > 0)
            {
                if (GUILayout.Button("← Précédent", GUILayout.Height(30)))
                {
                    currentStep--;
                }
            }
            
            GUILayout.FlexibleSpace();
            
            if (currentStep < steps.Length - 1 && stepCompleted[currentStep])
            {
                if (GUILayout.Button("Suivant →", GUILayout.Height(30)))
                {
                    currentStep++;
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            GUILayout.Space(10);
        }

        private void CreateCompleteProjectStructure()
        {
            string[] folders = {
                "Assets/Prefabs", "Assets/Prefabs/Core", "Assets/Prefabs/Player", 
                "Assets/Prefabs/UI", "Assets/Prefabs/Audio", "Assets/Prefabs/Effects", 
                "Assets/Prefabs/World", "Assets/Scenes", "Assets/Materials", 
                "Assets/Textures", "Assets/Audio", "Assets/Audio/Music", 
                "Assets/Audio/SFX", "Assets/Animations", "Assets/Sprites"
            };

            foreach (string folder in folders)
            {
                if (!AssetDatabase.IsValidFolder(folder))
                {
                    string parentFolder = Path.GetDirectoryName(folder).Replace('\\', '/');
                    string folderName = Path.GetFileName(folder);
                    AssetDatabase.CreateFolder(parentFolder, folderName);
                }
            }
            
            AssetDatabase.Refresh();
            UnityEngine.UnityEngine.Debug.Log("✅ Structure de dossiers créée avec succès !");
        }

        private void GenerateAllPrefabs()
        {
            // Utiliser le générateur de prefabs existant
            PrefabAutoGenerator generator = CreateInstance<PrefabAutoGenerator>();
            // Simuler la génération automatique
            UnityEngine.UnityEngine.Debug.Log("🎮 Génération des prefabs en cours...");
            
            // Ici on appellerait les méthodes du PrefabAutoGenerator
            // Pour simplifier, on affiche juste un message
            EditorUtility.DisplayDialog("Prefabs", "Utilisez le menu 'ChronoForge/Auto Generate Prefabs' pour générer les prefabs.", "OK");
        }

        private void SetupGameScene()
        {
            UnityEngine.UnityEngine.Debug.Log("🎬 Configuration de la scène de jeu...");
            EditorUtility.DisplayDialog("Scène", "Utilisez le menu 'ChronoForge/Auto Setup Scene' pour configurer la scène.", "OK");
        }

        private void ConfigureProjectSettings()
        {
            UnityEngine.UnityEngine.Debug.Log("⚙️ Configuration des paramètres du projet...");
            
            // Configurer la physique 2D
            Physics2D.gravity = Vector2.zero;
            
            UnityEngine.UnityEngine.Debug.Log("✅ Paramètres configurés !");
            EditorUtility.DisplayDialog("Paramètres", "Paramètres de base configurés !\n\nConfigurez manuellement les layers dans Project Settings.", "OK");
        }
    }
}
