using System;
using System.IO;
using UnityEngine;
using System.Security.Cryptography;
using System.Text;

namespace SpaceClicker.Core
{
    /// <summary>
    /// Structure principale de sauvegarde du jeu
    /// </summary>
    [System.Serializable]
    public class GameSaveData
    {
        public string version = "1.0";
        public string lastSaveTime;
        public ResourceSaveData resources;
        public UpgradeSaveData upgrades;
        public PlayerSaveData player;
        
        public GameSaveData()
        {
            lastSaveTime = DateTime.Now.ToBinary().ToString();
            resources = new ResourceSaveData();
            upgrades = new UpgradeSaveData();
            player = new PlayerSaveData();
        }
    }
    
    /// <summary>
    /// Données du joueur
    /// </summary>
    [System.Serializable]
    public class PlayerSaveData
    {
        public int level = 1;
        public int prestigeLevel = 0;
        public float totalPlayTime = 0f;
        public string playerName = "Commander";
    }
    
    /// <summary>
    /// Système de sauvegarde du jeu
    /// </summary>
    public class SaveSystem : MonoBehaviour
    {
        [Header("Save Settings")]
        public bool useEncryption = true;
        public bool useCloudSave = false;
        public string saveFileName = "spaceclicker_save.dat";
        
        [Header("Debug")]
        public bool showDebugInfo = false;
        
        // Clé de chiffrement (à changer en production)
        private const string ENCRYPTION_KEY = "SpaceClicker2025SecretKey";
        
        // Chemin de sauvegarde
        private string savePath;
        
        // Références
        private ResourceManager resourceManager;
        private UpgradeManager upgradeManager;
        
        // Données actuelles
        private GameSaveData currentSaveData;
        
        #region Initialization
        
        public void Initialize()
        {
            Debug.Log("💾 Initializing SaveSystem...");
            
            // Définir le chemin de sauvegarde
            savePath = Path.Combine(Application.persistentDataPath, saveFileName);
            
            // Obtenir les références
            resourceManager = FindObjectOfType<ResourceManager>();
            upgradeManager = FindObjectOfType<UpgradeManager>();
            
            Debug.Log($"✅ SaveSystem initialized. Save path: {savePath}");
        }
        
        #endregion
        
        #region Save Operations
        
        /// <summary>
        /// Sauvegarde le jeu
        /// </summary>
        public void SaveGame()
        {
            try
            {
                // Créer les données de sauvegarde
                GameSaveData saveData = CreateSaveData();
                
                // Convertir en JSON
                string jsonData = JsonUtility.ToJson(saveData, true);
                
                // Chiffrer si nécessaire
                if (useEncryption)
                {
                    jsonData = EncryptString(jsonData);
                }
                
                // Écrire le fichier
                File.WriteAllText(savePath, jsonData);
                
                currentSaveData = saveData;
                
                if (showDebugInfo)
                {
                    Debug.Log($"💾 Game saved successfully to {savePath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ Failed to save game: {e.Message}");
            }
        }
        
        /// <summary>
        /// Charge le jeu
        /// </summary>
        public void LoadGame()
        {
            try
            {
                if (!File.Exists(savePath))
                {
                    Debug.Log("📁 No save file found, starting new game");
                    CreateNewGame();
                    return;
                }
                
                // Lire le fichier
                string jsonData = File.ReadAllText(savePath);
                
                // Déchiffrer si nécessaire
                if (useEncryption)
                {
                    jsonData = DecryptString(jsonData);
                }
                
                // Convertir depuis JSON
                GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(jsonData);
                
                if (saveData != null)
                {
                    LoadSaveData(saveData);
                    currentSaveData = saveData;
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"📁 Game loaded successfully from {savePath}");
                    }
                }
                else
                {
                    Debug.LogError("❌ Failed to parse save data");
                    CreateNewGame();
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ Failed to load game: {e.Message}");
                CreateNewGame();
            }
        }
        
        /// <summary>
        /// Supprime la sauvegarde
        /// </summary>
        public void DeleteSave()
        {
            try
            {
                if (File.Exists(savePath))
                {
                    File.Delete(savePath);
                    Debug.Log("🗑️ Save file deleted");
                }
                
                CreateNewGame();
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ Failed to delete save: {e.Message}");
            }
        }
        
        #endregion
        
        #region Save Data Management
        
        /// <summary>
        /// Crée les données de sauvegarde actuelles
        /// </summary>
        private GameSaveData CreateSaveData()
        {
            GameSaveData saveData = new GameSaveData();
            
            // Sauvegarder les ressources
            if (resourceManager != null)
            {
                saveData.resources = resourceManager.GetSaveData();
            }
            
            // Sauvegarder les upgrades
            if (upgradeManager != null)
            {
                saveData.upgrades = upgradeManager.GetSaveData();
            }
            
            // Sauvegarder les données du joueur
            saveData.player = new PlayerSaveData
            {
                level = GetPlayerLevel(),
                prestigeLevel = GetPrestigeLevel(),
                totalPlayTime = GetTotalPlayTime(),
                playerName = GetPlayerName()
            };
            
            return saveData;
        }
        
        /// <summary>
        /// Charge les données de sauvegarde
        /// </summary>
        private void LoadSaveData(GameSaveData saveData)
        {
            // Charger les ressources
            if (resourceManager != null && saveData.resources != null)
            {
                resourceManager.LoadSaveData(saveData.resources);
            }
            
            // Charger les upgrades
            if (upgradeManager != null && saveData.upgrades != null)
            {
                upgradeManager.LoadSaveData(saveData.upgrades);
            }
            
            // Charger les données du joueur
            if (saveData.player != null)
            {
                LoadPlayerData(saveData.player);
            }
        }
        
        /// <summary>
        /// Crée une nouvelle partie
        /// </summary>
        private void CreateNewGame()
        {
            currentSaveData = new GameSaveData();
            
            // Initialiser avec les valeurs par défaut
            if (resourceManager != null)
            {
                // Les ressources sont déjà initialisées à zéro
            }
            
            if (upgradeManager != null)
            {
                // Les upgrades sont déjà initialisés
            }
            
            Debug.Log("🆕 New game created");
        }
        
        #endregion
        
        #region Player Data
        
        private int GetPlayerLevel()
        {
            // Calculer le niveau basé sur les ressources totales ou autres métriques
            if (resourceManager != null)
            {
                var totalResources = resourceManager.GetResource(ResourceType.Energy) + 
                                   resourceManager.GetResource(ResourceType.Minerals) + 
                                   resourceManager.GetResource(ResourceType.ResearchData);
                
                // Formule simple pour le niveau
                return Mathf.Max(1, (int)(Math.Log10((double)totalResources + 1) * 10));
            }
            
            return 1;
        }
        
        private int GetPrestigeLevel()
        {
            // TODO: Implémenter le système de prestige
            return currentSaveData?.player?.prestigeLevel ?? 0;
        }
        
        private float GetTotalPlayTime()
        {
            // TODO: Tracker le temps de jeu
            return currentSaveData?.player?.totalPlayTime ?? 0f;
        }
        
        private string GetPlayerName()
        {
            return currentSaveData?.player?.playerName ?? "Commander";
        }
        
        private void LoadPlayerData(PlayerSaveData playerData)
        {
            // TODO: Appliquer les données du joueur
        }
        
        #endregion
        
        #region Encryption
        
        /// <summary>
        /// Chiffre une chaîne de caractères
        /// </summary>
        private string EncryptString(string plainText)
        {
            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                using (Aes aes = Aes.Create())
                {
                    aes.Key = ResizeKey(keyBytes, 32); // AES-256
                    aes.IV = new byte[16]; // IV de zéros pour simplicité
                    
                    using (var encryptor = aes.CreateEncryptor())
                    {
                        byte[] encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                        return Convert.ToBase64String(encryptedBytes);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ Encryption failed: {e.Message}");
                return plainText; // Retourner le texte non chiffré en cas d'erreur
            }
        }
        
        /// <summary>
        /// Déchiffre une chaîne de caractères
        /// </summary>
        private string DecryptString(string cipherText)
        {
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(cipherText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                using (Aes aes = Aes.Create())
                {
                    aes.Key = ResizeKey(keyBytes, 32); // AES-256
                    aes.IV = new byte[16]; // IV de zéros pour simplicité
                    
                    using (var decryptor = aes.CreateDecryptor())
                    {
                        byte[] decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ Decryption failed: {e.Message}");
                return cipherText; // Retourner le texte chiffré en cas d'erreur
            }
        }
        
        /// <summary>
        /// Redimensionne la clé à la taille requise
        /// </summary>
        private byte[] ResizeKey(byte[] key, int size)
        {
            byte[] resizedKey = new byte[size];
            
            if (key.Length >= size)
            {
                Array.Copy(key, resizedKey, size);
            }
            else
            {
                Array.Copy(key, resizedKey, key.Length);
                // Remplir le reste avec des zéros (déjà fait par défaut)
            }
            
            return resizedKey;
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Obtient la date de dernière sauvegarde
        /// </summary>
        public DateTime GetLastSaveTime()
        {
            if (currentSaveData != null && !string.IsNullOrEmpty(currentSaveData.lastSaveTime))
            {
                if (long.TryParse(currentSaveData.lastSaveTime, out long binary))
                {
                    return DateTime.FromBinary(binary);
                }
            }
            
            return default(DateTime);
        }
        
        /// <summary>
        /// Vérifie si une sauvegarde existe
        /// </summary>
        public bool SaveExists()
        {
            return File.Exists(savePath);
        }
        
        /// <summary>
        /// Obtient les informations de la sauvegarde
        /// </summary>
        public string GetSaveInfo()
        {
            if (!SaveExists()) return "Aucune sauvegarde";
            
            try
            {
                FileInfo fileInfo = new FileInfo(savePath);
                return $"Dernière sauvegarde: {fileInfo.LastWriteTime:dd/MM/yyyy HH:mm}";
            }
            catch
            {
                return "Informations indisponibles";
            }
        }
        
        #endregion
    }
}
