#!/usr/bin/env python3
"""
Script de test pour diagnostiquer les problèmes d'authentification Twitter.
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import ConfigManager, setup_logging
from src.platforms.twitter import TwitterConnector
import tweepy

def test_twitter_auth():
    """Test détaillé de l'authentification Twitter."""
    
    # Configuration
    config = ConfigManager()
    setup_logging(config)
    
    print("🔍 Diagnostic Twitter API")
    print("=" * 40)
    
    # Vérifier les variables d'environnement
    print("\n📋 Variables d'environnement:")
    api_key = os.getenv('TWITTER_API_KEY')
    api_secret = os.getenv('TWITTER_API_SECRET')
    access_token = os.getenv('TWITTER_ACCESS_TOKEN')
    access_token_secret = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
    bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
    
    print(f"API_KEY: {'✅' if api_key else '❌'} ({api_key[:10] + '...' if api_key else 'MANQUANT'})")
    print(f"API_SECRET: {'✅' if api_secret else '❌'} ({api_secret[:10] + '...' if api_secret else 'MANQUANT'})")
    print(f"ACCESS_TOKEN: {'✅' if access_token else '❌'} ({access_token[:10] + '...' if access_token else 'MANQUANT'})")
    print(f"ACCESS_TOKEN_SECRET: {'✅' if access_token_secret else '❌'} ({access_token_secret[:10] + '...' if access_token_secret else 'MANQUANT'})")
    print(f"BEARER_TOKEN: {'✅' if bearer_token else '❌'} ({bearer_token[:20] + '...' if bearer_token else 'MANQUANT'})")
    
    if not all([api_key, api_secret, access_token, access_token_secret, bearer_token]):
        print("\n❌ Certaines clés API sont manquantes!")
        return False
    
    # Test 1: Client API v2
    print("\n🧪 Test 1: Twitter API v2 Client")
    try:
        client = tweepy.Client(
            bearer_token=bearer_token,
            consumer_key=api_key,
            consumer_secret=api_secret,
            access_token=access_token,
            access_token_secret=access_token_secret,
            wait_on_rate_limit=True
        )
        
        user = client.get_me()
        if user.data:
            print(f"✅ API v2 OK - Connecté en tant que @{user.data.username}")
            print(f"   ID: {user.data.id}")
            print(f"   Nom: {user.data.name}")
        else:
            print("❌ API v2 - Réponse vide")
            return False
            
    except Exception as e:
        print(f"❌ API v2 - Erreur: {e}")
        return False
    
    # Test 2: API v1.1 pour les médias
    print("\n🧪 Test 2: Twitter API v1.1")
    try:
        auth = tweepy.OAuth1UserHandler(
            api_key,
            api_secret,
            access_token,
            access_token_secret
        )
        api = tweepy.API(auth, wait_on_rate_limit=True)
        
        user = api.verify_credentials()
        if user:
            print(f"✅ API v1.1 OK - Connecté en tant que @{user.screen_name}")
        else:
            print("❌ API v1.1 - Échec de vérification")
            return False
            
    except Exception as e:
        print(f"❌ API v1.1 - Erreur: {e}")
        return False
    
    # Test 3: Permissions
    print("\n🧪 Test 3: Vérification des permissions")
    try:
        # Vérifier les permissions de l'app
        app = client.get_me(user_fields=['public_metrics'])
        print("✅ Permissions de lecture OK")
        
        # Note: Pour tester l'écriture, il faudrait faire un vrai tweet
        print("⚠️  Permissions d'écriture non testées (éviter le spam)")
        
    except Exception as e:
        print(f"❌ Permissions - Erreur: {e}")
        return False
    
    print("\n✅ Tous les tests Twitter sont passés!")
    return True

def suggest_fixes():
    """Suggère des corrections pour les problèmes courants."""
    print("\n🔧 Solutions possibles:")
    print("1. Vérifiez que votre app Twitter a les permissions 'Read and Write'")
    print("2. Régénérez vos tokens d'accès si nécessaire")
    print("3. Vérifiez que votre app n'est pas en mode 'Restricted'")
    print("4. Assurez-vous d'avoir un plan API valide (Free, Basic, Pro)")
    print("5. Vérifiez les URLs de callback dans votre app")
    
    print("\n📱 Liens utiles:")
    print("- Dashboard Twitter: https://developer.twitter.com/en/portal/dashboard")
    print("- Gestion des apps: https://developer.twitter.com/en/portal/projects-and-apps")
    print("- Documentation API: https://developer.twitter.com/en/docs/twitter-api")

if __name__ == "__main__":
    success = test_twitter_auth()
    
    if not success:
        suggest_fixes()
        sys.exit(1)
    else:
        print("\n🎉 Twitter est prêt à être utilisé!")
        sys.exit(0)
