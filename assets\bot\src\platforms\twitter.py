"""
Connecteur Twitter/X utilisant l'API v2 avec tweepy.
"""

import tweepy
import os
from typing import Dict, Any, Optional
from .base import PlatformConnector, PublicationResult
import logging

logger = logging.getLogger(__name__)

class TwitterConnector(PlatformConnector):
    """Connecteur pour Twitter/X."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client = None
        self.api = None
        self.max_length = config.get('max_length', 280)
        
    def authenticate(self) -> bool:
        """Authentifie avec l'API Twitter."""
        try:
            # API v2 Client pour les tweets
            self.client = tweepy.Client(
                bearer_token=os.getenv('TWITTER_BEARER_TOKEN'),
                consumer_key=os.getenv('TWITTER_API_KEY'),
                consumer_secret=os.getenv('TWITTER_API_SECRET'),
                access_token=os.getenv('TWITTER_ACCESS_TOKEN'),
                access_token_secret=os.getenv('TWITTER_ACCESS_TOKEN_SECRET'),
                wait_on_rate_limit=True
            )
            
            # API v1.1 pour l'upload d'images
            auth = tweepy.OAuth1UserHandler(
                os.getenv('TWITTER_API_KEY'),
                os.getenv('TWITTER_API_SECRET'),
                os.getenv('TWITTER_ACCESS_TOKEN'),
                os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
            )
            self.api = tweepy.API(auth, wait_on_rate_limit=True)
            
            # Test de connexion
            user = self.client.get_me()
            if user.data:
                logger.info(f"Authentifié sur Twitter en tant que @{user.data.username}")
                self.authenticated = True
                return True
            else:
                logger.error("Échec de l'authentification Twitter")
                return False
                
        except Exception as e:
            logger.error(f"Erreur d'authentification Twitter: {e}")
            return False
    
    def validate_post(self, title: str, body: str, **kwargs) -> bool:
        """Valide qu'un post respecte les contraintes Twitter."""
        full_text = self._prepare_tweet_text(title, body, kwargs.get('tags', []))
        
        if len(full_text) > self.max_length:
            logger.warning(f"Tweet trop long: {len(full_text)} caractères (max: {self.max_length})")
            return False
            
        return True
    
    def publish_post(self, title: str, body: str, **kwargs) -> PublicationResult:
        """Publie un tweet."""
        if not self.authenticated:
            return PublicationResult(
                success=False,
                platform="twitter",
                error="Non authentifié"
            )
        
        try:
            # Préparer le texte du tweet
            tweet_text = self._prepare_tweet_text(title, body, kwargs.get('tags', []))
            
            # Tronquer si nécessaire
            if len(tweet_text) > self.max_length:
                tweet_text = self.truncate_text(tweet_text, self.max_length)
            
            # Upload d'image si fournie
            media_ids = []
            if kwargs.get('image'):
                try:
                    media = self.api.media_upload(kwargs['image'])
                    media_ids = [media.media_id]
                except Exception as e:
                    logger.warning(f"Erreur upload image: {e}")
            
            # Publier le tweet
            response = self.client.create_tweet(
                text=tweet_text,
                media_ids=media_ids if media_ids else None
            )
            
            if response.data:
                self.log_success(kwargs.get('post_id', 'unknown'), response.data)
                return PublicationResult(
                    success=True,
                    platform="twitter",
                    post_id=str(response.data['id']),
                    response_data=response.data
                )
            else:
                return PublicationResult(
                    success=False,
                    platform="twitter",
                    error="Réponse vide de l'API"
                )
                
        except Exception as e:
            self.log_error(kwargs.get('post_id', 'unknown'), e)
            return PublicationResult(
                success=False,
                platform="twitter",
                error=str(e)
            )
    
    def _prepare_tweet_text(self, title: str, body: str, tags: list = None) -> str:
        """Prépare le texte du tweet en combinant titre, corps et hashtags."""
        # Si le body contient déjà le titre, utiliser seulement le body
        if title.lower() in body.lower():
            tweet_text = body
        else:
            tweet_text = f"{title}\n\n{body}"
        
        # Ajouter les hashtags de la config si pas déjà présents
        config_hashtags = self.config.get('hashtags', [])
        if config_hashtags and not any(tag in tweet_text for tag in config_hashtags):
            hashtag_text = self.format_hashtags(config_hashtags[:3])  # Max 3 hashtags
            tweet_text += hashtag_text
        
        # Ajouter les hashtags du post
        if tags:
            post_hashtags = [tag for tag in tags if f"#{tag}" not in tweet_text]
            if post_hashtags:
                hashtag_text = self.format_hashtags(post_hashtags[:2])  # Max 2 hashtags supplémentaires
                tweet_text += hashtag_text
        
        return tweet_text.strip()
    
    def get_tweet_url(self, tweet_id: str, username: str = None) -> str:
        """Génère l'URL d'un tweet."""
        if not username:
            try:
                user = self.client.get_me()
                username = user.data.username
            except:
                username = "unknown"
        
        return f"https://twitter.com/{username}/status/{tweet_id}"
