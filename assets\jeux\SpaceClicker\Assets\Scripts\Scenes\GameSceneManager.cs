using UnityEngine;
using UnityEngine.SceneManagement;
using SpaceClicker.Core;
using SpaceClicker.UI;
using SpaceClicker.Audio;
using SpaceClicker.Effects;

namespace SpaceClicker.Scenes
{
    /// <summary>
    /// Gestionnaire de la scène principale du jeu
    /// </summary>
    public class GameSceneManager : MonoBehaviour
    {
        [Header("Scene References")]
        public string mainMenuSceneName = "MainMenu";
        public string gameSceneName = "GameScene";
        public string settingsSceneName = "Settings";
        
        [Header("Game Managers")]
        public GameManager gameManager;
        public ResourceManager resourceManager;
        public UpgradeManager upgradeManager;
        
        [Header("UI Controllers")]
        public MainUIController mainUIController;
        public UIAnimationController uiAnimationController;
        
        [Header("Effect Managers")]
        public ClickEffectManager clickEffectManager;
        
        [Header("Audio")]
        public AudioManager audioManager;
        
        [Header("Loading")]
        public GameObject loadingPanel;
        public UnityEngine.UI.Slider loadingProgressBar;
        public TMPro.TextMeshProUGUI loadingText;
        
        // Private fields
        private bool isInitialized = false;
        private bool isPaused = false;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeScene();
        }
        
        private void Start()
        {
            StartGame();
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }
        
        #endregion
        
        #region Scene Initialization
        
        private void InitializeScene()
        {
            // Find managers if not assigned
            FindManagers();
            
            // Initialize loading UI
            if (loadingPanel != null)
            {
                loadingPanel.SetActive(true);
                UpdateLoadingProgress(0f, "Initializing...");
            }
            
            // Initialize managers in order
            InitializeManagers();
            
            isInitialized = true;
        }
        
        private void FindManagers()
        {
            // Find GameManager
            if (gameManager == null)
                gameManager = FindObjectOfType<GameManager>();
            
            // Find ResourceManager
            if (resourceManager == null)
                resourceManager = FindObjectOfType<ResourceManager>();
            
            // Find UpgradeManager
            if (upgradeManager == null)
                upgradeManager = FindObjectOfType<UpgradeManager>();
            
            // Find UI Controllers
            if (mainUIController == null)
                mainUIController = FindObjectOfType<MainUIController>();
            
            if (uiAnimationController == null)
                uiAnimationController = FindObjectOfType<UIAnimationController>();
            
            // Find Effect Managers
            if (clickEffectManager == null)
                clickEffectManager = FindObjectOfType<ClickEffectManager>();
            
            // Find Audio Manager
            if (audioManager == null)
                audioManager = FindObjectOfType<AudioManager>();
        }
        
        private void InitializeManagers()
        {
            UpdateLoadingProgress(0.2f, "Loading game data...");
            
            // Initialize core managers first
            if (gameManager != null)
            {
                // GameManager initializes itself
                UpdateLoadingProgress(0.4f, "Initializing resources...");
            }
            
            if (resourceManager != null)
            {
                // ResourceManager initializes itself
                UpdateLoadingProgress(0.6f, "Loading upgrades...");
            }
            
            if (upgradeManager != null)
            {
                // UpgradeManager initializes itself
                UpdateLoadingProgress(0.8f, "Setting up UI...");
            }
            
            // Initialize UI and effects
            if (mainUIController != null)
            {
                // MainUIController initializes itself
            }
            
            if (audioManager != null)
            {
                audioManager.PlayMusic("gameplay");
            }
            
            UpdateLoadingProgress(1f, "Ready!");
        }
        
        private void UpdateLoadingProgress(float progress, string message)
        {
            if (loadingProgressBar != null)
                loadingProgressBar.value = progress;
            
            if (loadingText != null)
                loadingText.text = message;
        }
        
        #endregion
        
        #region Game Control
        
        private void StartGame()
        {
            if (!isInitialized) return;
            
            // Hide loading panel
            if (loadingPanel != null)
            {
                loadingPanel.SetActive(false);
            }
            
            // Start game systems
            if (gameManager != null)
            {
                gameManager.StartGame();
            }
            
            // Show welcome message
            if (uiAnimationController != null)
            {
                uiAnimationController.ShowInfoNotification("Welcome to Space Clicker!");
            }
            
            Debug.Log("Game started successfully!");
        }
        
        public void PauseGame()
        {
            if (isPaused) return;
            
            isPaused = true;
            Time.timeScale = 0f;
            
            if (audioManager != null)
            {
                audioManager.PauseMusic();
            }
            
            // Show pause UI
            if (mainUIController != null)
            {
                mainUIController.ShowMessage("Game Paused");
            }
        }
        
        public void ResumeGame()
        {
            if (!isPaused) return;
            
            isPaused = false;
            Time.timeScale = 1f;
            
            if (audioManager != null)
            {
                audioManager.ResumeMusic();
            }
        }
        
        public void RestartGame()
        {
            // Save current progress
            if (gameManager != null)
            {
                gameManager.SaveGame();
            }
            
            // Reset game state
            if (resourceManager != null)
            {
                resourceManager.ResetResources();
            }
            
            if (upgradeManager != null)
            {
                upgradeManager.ResetUpgrades();
            }
            
            // Reload scene
            SceneManager.LoadScene(gameSceneName);
        }
        
        #endregion
        
        #region Input Handling
        
        private void HandleInput()
        {
            // Pause/Resume with Escape key
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (isPaused)
                    ResumeGame();
                else
                    PauseGame();
            }
            
            // Quick save with F5
            if (Input.GetKeyDown(KeyCode.F5))
            {
                QuickSave();
            }
            
            // Quick load with F9
            if (Input.GetKeyDown(KeyCode.F9))
            {
                QuickLoad();
            }
            
            // Toggle mute with M
            if (Input.GetKeyDown(KeyCode.M))
            {
                if (audioManager != null)
                {
                    audioManager.ToggleMute();
                }
            }
        }
        
        #endregion
        
        #region Save/Load
        
        public void QuickSave()
        {
            if (gameManager != null)
            {
                gameManager.SaveGame();
                
                if (uiAnimationController != null)
                {
                    uiAnimationController.ShowSuccessNotification("Game Saved!");
                }
                
                if (audioManager != null)
                {
                    audioManager.PlaySuccessSound();
                }
            }
        }
        
        public void QuickLoad()
        {
            if (gameManager != null)
            {
                bool loaded = gameManager.LoadGame();
                
                if (loaded)
                {
                    if (uiAnimationController != null)
                    {
                        uiAnimationController.ShowSuccessNotification("Game Loaded!");
                    }
                    
                    if (audioManager != null)
                    {
                        audioManager.PlaySuccessSound();
                    }
                    
                    // Refresh UI
                    if (mainUIController != null)
                    {
                        mainUIController.RefreshUI();
                    }
                }
                else
                {
                    if (uiAnimationController != null)
                    {
                        uiAnimationController.ShowErrorNotification("No save file found!");
                    }
                    
                    if (audioManager != null)
                    {
                        audioManager.PlayErrorSound();
                    }
                }
            }
        }
        
        #endregion
        
        #region Scene Transitions
        
        public void GoToMainMenu()
        {
            // Save before leaving
            if (gameManager != null)
            {
                gameManager.SaveGame();
            }
            
            // Transition to main menu
            if (uiAnimationController != null)
            {
                uiAnimationController.TransitionToScene(mainMenuSceneName);
            }
            else
            {
                SceneManager.LoadScene(mainMenuSceneName);
            }
        }
        
        public void GoToSettings()
        {
            // Transition to settings
            if (uiAnimationController != null)
            {
                uiAnimationController.TransitionToScene(settingsSceneName);
            }
            else
            {
                SceneManager.LoadScene(settingsSceneName);
            }
        }
        
        public void QuitGame()
        {
            // Save before quitting
            if (gameManager != null)
            {
                gameManager.SaveGame();
            }
            
            // Quit application
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Check if the game is currently paused
        /// </summary>
        public bool IsPaused()
        {
            return isPaused;
        }
        
        /// <summary>
        /// Check if the scene is fully initialized
        /// </summary>
        public bool IsInitialized()
        {
            return isInitialized;
        }
        
        /// <summary>
        /// Get the current game time (accounting for pause)
        /// </summary>
        public float GetGameTime()
        {
            return Time.time;
        }
        
        /// <summary>
        /// Show a debug message in the UI
        /// </summary>
        public void ShowDebugMessage(string message)
        {
            if (uiAnimationController != null)
            {
                uiAnimationController.ShowInfoNotification($"DEBUG: {message}");
            }
            
            Debug.Log($"[GameSceneManager] {message}");
        }
        
        #endregion
        
        #region Event Handlers
        
        private void OnEnable()
        {
            // Subscribe to scene events
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
        }
        
        private void OnDisable()
        {
            // Unsubscribe from scene events
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;
        }
        
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            Debug.Log($"Scene loaded: {scene.name}");
        }
        
        private void OnSceneUnloaded(Scene scene)
        {
            Debug.Log($"Scene unloaded: {scene.name}");
        }
        
        #endregion
    }
}
